

-- Create a DB named chat-db and Switch to the `communication_core_db` database
USE `communication_core_db`;

-- Create chat_message table if it doesn't exist
CREATE TABLE IF NOT EXISTS chat_message (
    id VARCHAR(36) NOT NULL DEFAULT (UUID()),
    chat_id CHAR(36) NOT NULL,
    sender_id CHAR(36) NOT NULL,
    text TEXT NULL, -- Encrypted message
    created_on DATETIME DEFAULT CURRENT_TIMESTAMP,
    deleted BOOLEAN DEFAULT FALSE,
    deleted_on DATETIME NULL,
    updated_on DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by CHAR(36) NULL,
    deleted_by CHAR(36) NULL,
    updated_by CHAR(36) NULL,
    PRIMARY KEY (`id`)
);

-- Create attachment table if it doesn't exist
CREATE TABLE IF NOT EXISTS attachment (
    id VARCHAR(36) NOT NULL DEFAULT (UUID()),
    chat_message_id CHAR(36) NOT NULL,
    attachment_key TEXT NOT NULL,
    created_on DATETIME DEFAULT CURRENT_TIMESTAMP,
    deleted BOOLEAN DEFAULT FALSE,
    deleted_on DATETIME NULL,
    updated_on DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by CHAR(36) NULL,
    deleted_by CHAR(36) NULL,
    updated_by CHAR(36) NULL,
    PRIMARY KEY (`id`),
    FOREIGN KEY (chat_message_id) REFERENCES chat_message(id)
);

-- Ticket Migration

CREATE TABLE IF NOT EXISTS tickets (
  id VARCHAR(36) NOT NULL DEFAULT (UUID()),
  title VARCHAR(255) NOT NULL,
  description TEXT,
  priority ENUM('LOW', 'MEDIUM', 'HIGH', 'CRITICAL') DEFAULT 'MEDIUM',
  short_code VARCHAR(50) NOT NULL,
  status ENUM('OPEN', 'IN_PROGRESS', 'RESOLVED', 'CLOSED') DEFAULT 'OPEN',
  assignee_id VARCHAR(36),
  created_by CHAR(36),
  updated_by CHAR(36),
  created_on DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_on DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  deleted_on DATETIME,
  deleted_by CHAR(36) NULL,
  deleted BOOLEAN DEFAULT FALSE,
  PRIMARY KEY (`id`)
);

-- Ticket History table
CREATE TABLE IF NOT EXISTS ticket_histories (
  id VARCHAR(36) NOT NULL DEFAULT (UUID()),
  ticket_id VARCHAR(36) NOT NULL,
  action ENUM('CREATED', 'STATUS_CHANGED', 'ASSIGNEE_CHANGED', 'DELETED') NOT NULL,
  previous_value TEXT,
  new_value TEXT,
  created_by CHAR(36),
  updated_by CHAR(36),
  created_on DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_on DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  deleted_on DATETIME,
  deleted BOOLEAN DEFAULT FALSE,
  deleted_by CHAR(36) NULL,
  FOREIGN KEY (ticket_id) REFERENCES tickets(id),
  PRIMARY KEY (`id`)
);

-- Create chat table if it doesn't exist
CREATE TABLE IF NOT EXISTS `chat` (
  `id` CHAR(36) NOT NULL DEFAULT (UUID()),
  `ticket_id` CHAR(36) NOT NULL,
  `created_on` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `deleted` BOOLEAN DEFAULT FALSE,
  `deleted_on` DATETIME NULL,
  `updated_on` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `created_by` CHAR(36) NULL,
  `deleted_by` CHAR(36) NULL,
  `updated_by` CHAR(36) NULL,
  PRIMARY KEY (`id`)
);

-- Create chat_members table if it doesn't exist
CREATE TABLE IF NOT EXISTS `chat_members` (
  `id` CHAR(36) NOT NULL DEFAULT (UUID()),
  `chat_id` CHAR(36) NOT NULL,
  `member_id` CHAR(36) NOT NULL,
  `member_type` ENUM('agent', 'bot', 'user') NOT NULL,
  `status` ENUM('active', 'inactive') NOT NULL DEFAULT 'active',
  `created_on` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `deleted` BOOLEAN DEFAULT FALSE,
  `deleted_on` DATETIME NULL,
  `updated_on` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `created_by` CHAR(36) NULL,
  `deleted_by` CHAR(36) NULL,
  `updated_by` CHAR(36) NULL,
  PRIMARY KEY (`id`),
  FOREIGN KEY (`chat_id`) REFERENCES `chat`(`id`) ON UPDATE CASCADE ON DELETE CASCADE
);
