import app from './app';
import config from '@config/index';
import http from 'http';
import {ConnectionService} from './api/services/connection.service';
import logger from '@utils/logger';
import {connectDatabase} from '@db/index';
import 'module-alias/register';

const startServer = async () => {
  try {
    // Connect to the database
    await connectDatabase();

    // Start the server
    const server = http.createServer(app);

    new ConnectionService(server, config.corsOrigin);

    server.listen(config.port, () => {
      logger.info(
        `Server running in ${config.env} mode on port ${config.port}`,
      );
      logger.info(
        `API Documentation available at ${config.host}:${config.port}/api-docs`,
      );
    });

    // Handle graceful shutdown
    const gracefulShutdown = (signal: string) => {
      logger.info(`${signal} received. Shutting down gracefully.`);
      server.close(() => {
        logger.info('HTTP server closed.');
        process.exit(0);
      });

      // Force shutdown after 10 seconds
      setTimeout(() => {
        logger.error(
          'Could not close connections in time, forcefully shutting down',
        );
        process.exit(1);
      }, 10000);
    };

    // Listen for termination signals
    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => gracefulShutdown('SIGINT'));

    // Handle uncaught exceptions and unhandled rejections
    process.on('uncaughtException', err => {
      logger.error('Uncaught Exception:', err);
      gracefulShutdown('Uncaught Exception');
    });

    process.on('unhandledRejection', reason => {
      logger.error('Unhandled Rejection:', reason);
      gracefulShutdown('Unhandled Rejection');
    });
  } catch (error) {
    logger.error('Failed to start server:', error);
    process.exit(1);
  }
};

startServer();
