import {Response} from 'express';

export interface ApiResponse<T> {
  success: boolean;
  message: string;
  data?: T;
  errors?: Record<string, string[]>;
  meta?: {
    [key: string]: any;
  };
}

export const successResponse = <T>(
  res: Response,
  data: T,
  message = 'Success',
  statusCode = 200,
  meta?: Record<string, any>,
): Response => {
  return res.status(statusCode).json({
    success: true,
    message,
    data,
    ...(meta && {meta}),
  });
};

export const errorResponse = (
  res: Response,
  message = 'Error',
  statusCode = 500,
  errors?: Record<string, string[]>,
): Response => {
  return res.status(statusCode).json({
    success: false,
    message,
    ...(errors && {errors}),
  });
};
