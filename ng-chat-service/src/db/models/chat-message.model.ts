import {DataTypes, Model, Optional} from 'sequelize';
import {
  baseModelAttributes,
  BaseModelAttributes,
  getBaseModelOptions,
} from './base.model';

interface ChatMessageAttributes extends BaseModelAttributes {
  chatId: string;
  senderId: string;
  text: string | null;
}

type ChatMessageCreationAttributes = Optional<
  ChatMessageAttributes,
  keyof BaseModelAttributes
>;

class ChatMessage
  extends Model<ChatMessageCreationAttributes>
  implements ChatMessageAttributes
{
  public chatId!: string;
  public senderId!: string;
  public text!: string | null;

  // Base Model Attributes
  public id!: string;
  public createdOn!: Date;
  public deleted!: boolean;
  public deletedOn!: Date | null;
  public updatedOn!: Date;
  public createdBy!: string | null;
  public deletedBy!: string | null;
  public updatedBy!: string | null;
}

ChatMessage.init(
  {
    ...baseModelAttributes,

    chatId: {
      type: DataTypes.CHAR(36),
      allowNull: false,
      field: 'chat_id',
    },
    senderId: {
      type: DataTypes.CHAR(36),
      field: 'sender_id',
      allowNull: false,
    },
    text: {
      type: DataTypes.TEXT,
      allowNull: true,
      field: 'text',
      validate: {
        len: {
          args: [0, 1000],
          msg: 'Text must be at most 1000 characters long',
        },
      },
    },
  },
  getBaseModelOptions('ChatMessage', 'chat_message'),
);

export {ChatMessage};
