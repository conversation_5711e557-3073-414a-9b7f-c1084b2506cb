import {DataTypes, Model, Optional} from 'sequelize';
import {
  baseModelAttributes,
  BaseModelAttributes,
  getBaseModelOptions,
} from './base.model';

import {ChatMember} from './chat-member.model';

interface ChatAttributes extends BaseModelAttributes {
  ticketId: string;
  members?: ChatMember[];
}

type ChatCreationAttributes = Optional<
  ChatAttributes,
  keyof BaseModelAttributes
>;

class Chat extends Model<ChatCreationAttributes> implements ChatAttributes {
  public ticketId!: string;
  public members?: ChatMember[];

  // Base Model Attributes
  public id!: string;
  public createdOn!: Date;
  public deleted!: boolean;
  public deletedOn!: Date | null;
  public updatedOn!: Date;
  public createdBy!: string | null;
  public deletedBy!: string | null;
  public updatedBy!: string | null;
}

Chat.init(
  {
    ...baseModelAttributes,

    ticketId: {
      type: DataTypes.CHAR(36),
      allowNull: false,
      field: 'ticket_id',
    },
  },
  getBaseModelOptions('Chat', 'chat'),
);

export {Chat};
