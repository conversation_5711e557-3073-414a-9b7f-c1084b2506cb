import {DataTypes, Model, Optional} from 'sequelize';
import {
  BaseModelAttributes,
  baseModelAttributes,
  getBaseModelOptions,
} from './base.model';
import {TicketPriority, TicketStatus} from '../../enums';

interface TicketAttributes extends BaseModelAttributes {
  title: string;
  description?: string;
  priority: TicketPriority;
  shortCode: string;
  status: TicketStatus;
  assigneeId?: string | null;
}

export type TicketCreationAttributes = Optional<
  TicketAttributes,
  keyof BaseModelAttributes | 'description' | 'assigneeId'
>;

class Ticket
  extends Model<TicketAttributes, TicketCreationAttributes>
  implements TicketAttributes
{
  public id!: string;
  public title!: string;
  public description?: string;
  public priority!: TicketPriority;
  public shortCode!: string;
  public status!: TicketStatus;
  public assigneeId?: string | null;

  // From BaseModel
  public deleted!: boolean;
  public createdBy!: string | null;
  public updatedBy!: string | null;
  public deletedBy!: string | null;
  public createdOn!: Date;
  public updatedOn!: Date;
  public deletedOn!: Date | null;
}

Ticket.init(
  {
    ...baseModelAttributes,

    title: {
      type: DataTypes.STRING(255),
      allowNull: false,
    },

    description: {
      type: DataTypes.TEXT,
      allowNull: true,
    },

    priority: {
      type: DataTypes.ENUM(...Object.values(TicketPriority)),
      allowNull: false,
      defaultValue: TicketPriority.MEDIUM,
    },

    shortCode: {
      type: DataTypes.STRING(50),
      allowNull: false,
      unique: true,
    },

    status: {
      type: DataTypes.ENUM(...Object.values(TicketStatus)),
      allowNull: false,
      defaultValue: TicketStatus.OPEN,
    },

    assigneeId: {
      type: DataTypes.CHAR(36),
      allowNull: true,
    },
  },
  getBaseModelOptions('Ticket', 'tickets'),
);

export {Ticket};
