import {Chat} from './chat.model';
import {Chat<PERSON><PERSON>ber} from './chat-member.model';
export * from './base.model';
export * from './chat-message.model';
export * from './attachment.model';
export * from './chat.model';
export * from './chat-member.model';
export * from './ticket.model';
export * from './ticket-history.model';
export * from './chat-message-request-dto.model';
Chat.hasMany(ChatMember, {
  foreignKey: 'chatId',
  as: 'members',
});
