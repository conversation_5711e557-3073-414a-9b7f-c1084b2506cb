import {DataTypes, Model, Optional} from 'sequelize';

import {ChatMemberStatus, MemberType} from '../../enums';
import {
  baseModelAttributes,
  BaseModelAttributes,
  getBaseModelOptions,
} from './base.model';

interface ChatMemberAttributes extends BaseModelAttributes {
  chatId: string;
  memberId: string;
  status: ChatMemberStatus;
  memberType: MemberType;
}

type ChatMemberCreationAttributes = Optional<
  ChatMemberAttributes,
  keyof BaseModelAttributes
>;

class ChatMember
  extends Model<ChatMemberCreationAttributes>
  implements ChatMemberAttributes
{
  public chatId!: string;
  public memberId!: string;
  public status!: ChatMemberStatus;
  public memberType!: MemberType;
  // Base Model Attributes
  public id!: string;
  public createdOn!: Date;
  public deleted!: boolean;
  public deletedOn!: Date | null;
  public updatedOn!: Date;
  public createdBy!: string | null;
  public deletedBy!: string | null;
  public updatedBy!: string | null;
}

ChatMember.init(
  {
    ...baseModelAttributes,

    chatId: {
      type: DataTypes.CHAR(36),
      allowNull: false,
      field: 'chat_id',
    },
    memberId: {
      type: DataTypes.CHAR(36),
      allowNull: false,
      field: 'member_id',
    },
    memberType: {
      type: DataTypes.ENUM(...Object.values(MemberType)),
      allowNull: false,
      field: 'member_type',
      validate: {
        isIn: {
          args: [Object.values(MemberType)],
          msg: `memberType must be one of '${Object.values(MemberType)}'`,
        },
      },
    },
    status: {
      type: DataTypes.ENUM(...Object.values(ChatMemberStatus)),
      allowNull: false,
      defaultValue: ChatMemberStatus.ACTIVE,
      validate: {
        isIn: {
          args: [Object.values(ChatMemberStatus)],
          msg: `status must be one of '${Object.values(ChatMemberStatus)}'`,
        },
      },
    },
  },
  getBaseModelOptions('ChatMember', 'chat_members'),
);

export {ChatMember};
