import {DataTypes, Model, Optional} from 'sequelize';
import {
  baseModelAttributes,
  BaseModelAttributes,
  getBaseModelOptions,
} from './base.model';

import {ChatMessage} from './index';

interface AttachmentAttributes extends BaseModelAttributes {
  chatMessageId: string;

  attachmentKey: string;
}

type AttachmentCreationAttributes = Optional<
  AttachmentAttributes,
  keyof BaseModelAttributes
>;

class Attachment
  extends Model<AttachmentCreationAttributes>
  implements AttachmentAttributes
{
  public chatMessageId!: string;

  public attachmentKey!: string;

  // Base Model Attributes
  public id!: string;
  public createdOn!: Date;
  public deleted!: boolean;
  public deletedOn!: Date | null;
  public updatedOn!: Date;
  public createdBy!: string | null;
  public deletedBy!: string | null;
  public updatedBy!: string | null;
}

Attachment.init(
  {
    ...baseModelAttributes,
    chatMessageId: {
      type: DataTypes.CHAR(36),
      allowNull: false,
      references: {
        model: ChatMessage,
        key: 'id',
      },
    },
    attachmentKey: {
      type: DataTypes.TEXT,
      allowNull: false,
    },
  },
  getBaseModelOptions('Attachment', 'attachment'),
);

export {Attachment};
