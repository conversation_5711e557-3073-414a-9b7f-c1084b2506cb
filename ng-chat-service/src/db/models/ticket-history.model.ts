import {DataTypes, Model, Optional} from 'sequelize';
import {
  BaseModelAttributes,
  baseModelAttributes,
  getBaseModelOptions,
} from './base.model';
import {TicketActionType} from '../../enums';

interface TicketHistoryAttributes extends BaseModelAttributes {
  ticketId: string;
  action: TicketActionType;
  previousValue: string | null;
  newValue: string | null;
}

export type TicketHistoryCreationAttributes = Optional<
  TicketHistoryAttributes,
  keyof BaseModelAttributes | 'previousValue' | 'newValue'
>;

class TicketHistory
  extends Model<TicketHistoryAttributes, TicketHistoryCreationAttributes>
  implements TicketHistoryAttributes
{
  public id!: string;
  public ticketId!: string;
  public action!: TicketActionType;
  public previousValue!: string | null;
  public newValue!: string | null;

  // From BaseModel
  public deleted!: boolean;
  public createdBy!: string | null;
  public updatedBy!: string | null;
  public deletedBy!: string | null;
  public createdOn!: Date;
  public updatedOn!: Date;
  public deletedOn!: Date | null;
}

TicketHistory.init(
  {
    ...baseModelAttributes,

    ticketId: {
      type: DataTypes.CHAR(36),
      allowNull: false,
    },

    action: {
      type: DataTypes.ENUM(...Object.values(TicketActionType)),
      allowNull: false,
    },

    previousValue: {
      type: DataTypes.TEXT,
      allowNull: true,
    },

    newValue: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
  },
  getBaseModelOptions('TicketHistory', 'ticket_histories'),
);

export {TicketHistory};
