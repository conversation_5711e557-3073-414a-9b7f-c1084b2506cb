import {DataTypes} from 'sequelize';
import sequelize from '../index';

// Interface for common model attributes
export interface BaseModelAttributes {
  id: string;
  deleted: boolean;
  createdBy: string | null;
  deletedBy: string | null;
  updatedBy: string | null;
  createdOn: Date;
  updatedOn: Date;
  deletedOn: Date | null;
}

// Common attribute definitions for Sequelize's init method
export const baseModelAttributes = {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true,
    allowNull: false,
    field: 'id',
  },
  deleted: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    allowNull: false,
    field: 'deleted',
  },
  createdBy: {
    type: DataTypes.CHAR(36),
    allowNull: true,
    field: 'created_by',
  },
  deletedBy: {
    type: DataTypes.CHAR(36),
    allowNull: true,
    field: 'deleted_by',
  },
  updatedBy: {
    type: DataTypes.CHAR(36),
    allowNull: true,
    field: 'updated_by',
  },
  createdOn: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW,
    allowNull: false,
    field: 'created_on',
  },
  updatedOn: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW,
    allowNull: false,
    field: 'updated_on',
  },
  deletedOn: {
    type: DataTypes.DATE,
    allowNull: true,
    field: 'deleted_on',
  },
};

// Common model options for Sequelize's init method
export const getBaseModelOptions = (modelName: string, tableName: string) => ({
  sequelize,
  modelName,
  tableName,
  timestamps: true,
  paranoid: true,
  underscored: true,
  createdAt: 'createdOn',
  updatedAt: 'updatedOn',
  deletedAt: 'deletedOn',
});
