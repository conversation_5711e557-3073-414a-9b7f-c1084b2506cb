import request from 'supertest';
import app from '../../app';
import {ChatService} from '@services/chat.service';
import {Chat} from '@db/models';
import {chatData} from '../mock-data/chat.mock';

jest.mock('@services/chat.service');

const mockedChatService = jest.mocked(ChatService);

describe('ChatController Acceptance Tests', () => {
  const chatId = 'test-chat-id';

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('createChat', () => {
    it('should create a chat successfully and return 200', async () => {
      const createdChat = {
        id: chatId,
        ...chatData,
      } as unknown as Chat;

      mockedChatService.prototype.createChat.mockResolvedValue(createdChat);

      const res = await request(app)
        .post('/api/v1/chats/create')
        .send(chatData);

      expect(res.status).toBe(200);
      expect(res.body.data).toEqual(createdChat);
      expect(res.body.message).toBe('Chat Created Successfully');
      expect(mockedChatService.prototype.createChat).toHaveBeenCalledWith(
        chatData.ticketId,
        chatData.members,
      );
    });

    it('should return 400 if ticketId is not provided', async () => {
      const payload = {members: chatData.members};
      const res = await request(app).post('/api/v1/chats/create').send(payload);

      expect(res.status).toBe(400);
      expect(res.body.message).toBe('Please provide ticketId and members');
    });

    it('should return 400 if members are not provided', async () => {
      const payload = {ticketId: chatData.ticketId};
      const res = await request(app).post('/api/v1/chats/create').send(payload);

      expect(res.status).toBe(400);
      expect(res.body.message).toBe('Please provide ticketId and members');
    });
  });

  describe('getChatById', () => {
    it('should get a chat by id successfully and return 200', async () => {
      const chat = {id: chatId, name: 'Test Chat'} as unknown as Chat;
      mockedChatService.prototype.getChatById.mockResolvedValue(chat);

      const res = await request(app).get(`/api/v1/chats/${chatId}`);

      expect(res.status).toBe(200);
      expect(res.body.data).toEqual(chat);
      expect(res.body.message).toBe('Chat Retreived Successfully');
      expect(mockedChatService.prototype.getChatById).toHaveBeenCalledWith(
        chatId,
      );
    });
  });
});
