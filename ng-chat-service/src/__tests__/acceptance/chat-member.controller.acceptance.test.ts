import request from 'supertest';
import app from '../../app';
import {ChatMemberService} from '@services/chat-member.service';
import {Chat} from '@db/models';
import {memberData, updatedMemberData} from '../mock-data/chat-member.mock';

jest.mock('@services/chat-member.service');

const mockedChatMemberService = jest.mocked(ChatMemberService);

describe('ChatMemberController Acceptance Tests', () => {
  const chatId = 'test-chat-id';
  const memberId = 'test-member-id';

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('addChatMember', () => {
    it('should add a member successfully and return 200', async () => {
      const updatedChat = {
        id: chatId,
        members: [memberData.member],
      } as unknown as Chat;

      mockedChatMemberService.prototype.addChatMember.mockResolvedValue(
        updatedChat,
      );

      const res = await request(app)
        .post(`/api/v1/chats/${chatId}/member`)
        .send(memberData);

      expect(res.status).toBe(200);
      expect(res.body.data).toEqual(updatedChat);
      expect(res.body.message).toBe('Member Added Successfully');
      expect(
        mockedChatMemberService.prototype.addChatMember,
      ).toHaveBeenCalledWith(chatId, memberData.member);
    });

    it('should return 400 if member is not provided', async () => {
      const res = await request(app)
        .post(`/api/v1/chats/${chatId}/member`)
        .send({});

      expect(res.status).toBe(400);
      expect(res.body.message).toBe('Please provide member');
    });
  });

  describe('removeChatMember', () => {
    it('should remove a member successfully and return 200', async () => {
      const updatedChat = {
        id: chatId,
        members: [],
      } as unknown as Chat;

      mockedChatMemberService.prototype.removeChatMember.mockResolvedValue(
        updatedChat,
      );

      const res = await request(app).delete(
        `/api/v1/chats/${chatId}/member/${memberId}`,
      );

      expect(res.status).toBe(200);
      expect(res.body.data).toEqual(updatedChat);
      expect(res.body.message).toBe('Member Removed Successfully');
      expect(
        mockedChatMemberService.prototype.removeChatMember,
      ).toHaveBeenCalledWith(chatId, memberId);
    });
  });

  describe('updateChatMember', () => {
    it('should update a member successfully and return 200', async () => {
      const updatedChat = {
        id: chatId,
        members: [memberData.member],
      } as unknown as Chat;

      mockedChatMemberService.prototype.updateChatMember.mockResolvedValue(
        updatedChat,
      );

      const res = await request(app)
        .patch(`/api/v1/chats/${chatId}/member/${memberId}`)
        .send(updatedMemberData);

      expect(res.status).toBe(200);
      expect(res.body.data).toEqual(updatedChat);
      expect(res.body.message).toBe('Member Updated Successfully');
      expect(
        mockedChatMemberService.prototype.updateChatMember,
      ).toHaveBeenCalledWith(chatId, memberId, updatedMemberData.member);
    });

    it('should return 400 if member is not provided', async () => {
      const res = await request(app)
        .patch(`/api/v1/chats/${chatId}/member/${memberId}`)
        .send({});

      expect(res.status).toBe(400);
      expect(res.body.message).toBe('Please provide member');
    });
  });
});
