import request from 'supertest';
import app from '../../app';

import {ChatMessage} from '../../db/models';
import {ChatMessageService} from '@services/chat-message.service';

jest.mock('@services/chat-message.service');

describe('ChatMessageController Acceptance Tests', () => {
  const chatId = 'test-chat-id';

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('addChatMessage', () => {
    const messageData = {
      senderId: 'user1',
      text: 'Hello, world!',
    };

    it('should add a message successfully and return 200', async () => {
      const createdMessage = {
        ...messageData,
        id: 'msg1',
        chatId,
      } as ChatMessage;

      (ChatMessageService.prototype.addMessage as jest.Mock).mockResolvedValue(
        createdMessage,
      );

      const res = await request(app)
        .post(`/api/v1/chats/${chatId}/messages`)
        .send(messageData);

      expect(res.status).toBe(200);
      expect(res.body.data).toEqual(createdMessage);
      expect(res.body.message).toBe('Message Added Successfully');
      expect(ChatMessageService.prototype.addMessage).toHaveBeenCalledWith(
        chatId,
        {
          ...messageData,
          chatId,
        },
      );
    });

    it('should return 400 if senderId is not provided', async () => {
      const payload = {
        text: 'Hello, world!',
      };
      const res = await request(app)
        .post(`/api/v1/chats/${chatId}/messages`)
        .send(payload);

      expect(res.status).toBe(400);
      expect(res.body.message).toBe('Please provide senderId and text');
    });

    it('should return 400 if text is not provided', async () => {
      const payload = {
        senderId: 'user1',
      };
      const res = await request(app)
        .post(`/api/v1/chats/${chatId}/messages`)
        .send(payload);

      expect(res.status).toBe(400);
      expect(res.body.message).toBe('Please provide senderId and text');
    });
  });

  describe('getChatMessages', () => {
    it('should get messages successfully and return 200', async () => {
      const messages = [
        {id: 'msg1', text: 'Hello'},
        {id: 'msg2', text: 'World'},
      ] as ChatMessage[];
      (
        ChatMessageService.prototype.getChatMessages as jest.Mock
      ).mockResolvedValue(messages);

      const res = await request(app).get(`/api/v1/chats/${chatId}/messages`);

      expect(res.status).toBe(200);
      expect(res.body.data).toEqual(messages);
      expect(res.body.message).toBe('Message Retreived Successfully');
      expect(ChatMessageService.prototype.getChatMessages).toHaveBeenCalledWith(
        chatId,
        expect.any(Object),
      );
    });
  });
});
