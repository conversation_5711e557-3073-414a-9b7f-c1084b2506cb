import {ChatMessage} from '@db/models';

export const chatData = {
  ticketId: 'test-ticket-id',
  members: ['user1', 'user2'],
};

export const mockNewMessage = {
  senderId: 'user-123',
  text: 'Hello, this is a test message.',
  chatId: 'chat-session-123',
};

export const mockEncryptedCreatedMessage = {
  ...mockNewMessage,
  text: 'encrypted-text',
} as ChatMessage;

export const mockMessageList: ChatMessage[] = [
  {
    id: 'msg-1',
    senderId: 'user-123',
    memberType: 'user',
    text: 'encrypted-text-1',
    chatId: 'chat-session-123',
    createdOn: new Date(),
    updatedOn: new Date(),
  },
  {
    id: 'msg-2',
    senderId: 'agent-456',
    memberType: 'agent',
    text: 'encrypted-text-2',
    chatId: 'chat-session-123',
    createdOn: new Date(),
    updatedOn: new Date(),
  },
  {
    id: 'msg-3',
    senderId: 'user-123',
    memberType: 'user',
    text: null, // No text content
    chatId: 'chat-session-123',
    createdOn: new Date(),
    updatedOn: new Date(),
  },
] as unknown as ChatMessage[];

export const mockDecryptedMessageList: ChatMessage[] = [
  {
    ...mockMessageList[0],
    text: 'decrypted-text-1',
  },
  {
    ...mockMessageList[1],
    text: 'decrypted-text-2',
  },
  {
    ...mockMessageList[2],
    text: null,
  },
] as ChatMessage[];
