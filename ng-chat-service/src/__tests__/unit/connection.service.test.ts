import {ConnectionService} from '@services/connection.service';
import {EncryptionDecryptionService} from '@services/encryption-decryption.service';
import {ChatMessageService} from '@services/chat-message.service';
import {ChatService} from '@services/chat.service';
import {
  ChatMembersRepository,
  ChatMessageRepository,
  ChatRepository,
} from '@db/repositories';
import logger from '@utils/logger';
import {Server} from 'socket.io';
import {ChatMessageRequestDto} from '@db/models';

// Mock dependencies
jest.mock('socket.io');
jest.mock('@utils/logger');
jest.mock('@services/encryption-decryption.service');
jest.mock('@services/chat-message.service');
jest.mock('@services/chat.service');
jest.mock('@db/repositories');

const mockServerOn = jest.fn();
const mockServerTo = jest.fn();
const mockServerEmit = jest.fn();
const mockSocketOn = jest.fn();
const mockSocketJoin = jest.fn();
const mockSocketEmit = jest.fn();

const mockServer = {
  on: mockServerOn,
  to: mockServerTo,
  emit: mockServerEmit,
};

const mockSocket = {
  id: 'test-socket-id',
  on: mockSocketOn,
  join: mockSocketJoin,
  emit: mockSocketEmit,
};

// Mock implementations
(Server as unknown as jest.Mock).mockReturnValue(mockServer);
mockServerTo.mockReturnThis(); // for chaining .to().emit()

describe('ConnectionService', () => {
  let connectionService: ConnectionService;
  let server: any;
  let connectionCallback: (socket: any) => void;

  // Mocks for services initialized in ConnectionService
  const mockAddMessage = jest.fn();
  const mockChatMessageService = {
    addMessage: mockAddMessage,
  };
  const mockEncryptionService = {};

  beforeEach(async () => {
    jest.clearAllMocks();

    // Setup mocks for dependencies that are instantiated inside ConnectionService
    (EncryptionDecryptionService.create as jest.Mock).mockResolvedValue(
      mockEncryptionService,
    );
    (ChatMessageService as jest.Mock).mockImplementation(
      () => mockChatMessageService,
    );
    (ChatService as jest.Mock).mockImplementation(() => ({}));
    (ChatMessageRepository as jest.Mock).mockImplementation(() => ({}));
    (ChatRepository as jest.Mock).mockImplementation(() => ({}));
    (ChatMembersRepository as jest.Mock).mockImplementation(() => ({}));

    mockServerOn.mockImplementation((event, callback) => {
      if (event === 'connection') {
        connectionCallback = callback;
      }
    });

    server = {}; // Mock http server
    connectionService = new ConnectionService(server, '*');

    // The constructor calls initialize, which is async. We need to wait for it to complete.
    // Awaiting a short time for the async initialize to run. This is not ideal but necessary given the code structure.
    await new Promise(resolve => setImmediate(resolve));

    connectionCallback(mockSocket);
  });

  it('should be defined', () => {
    expect(connectionService).toBeDefined();
  });

  it('should initialize Socket.IO server and listen for connections', () => {
    expect(Server).toHaveBeenCalledWith(server, {
      cors: {
        origin: '*',
        methods: ['GET', 'POST'],
      },
    });
    expect(mockServerOn).toHaveBeenCalledWith(
      'connection',
      expect.any(Function),
    );
  });

  describe('when a socket connects', () => {
    it('should log connection, emit server-ready and set up event listeners', () => {
      expect(logger.info).toHaveBeenCalledWith(
        'Socket connected: test-socket-id',
      );
      expect(mockSocketEmit).toHaveBeenCalledWith('server-ready');
      expect(mockSocketOn).toHaveBeenCalledWith(
        'joinChannel',
        expect.any(Function),
      );
      expect(mockSocketOn).toHaveBeenCalledWith(
        'sendMessage',
        expect.any(Function),
      );
      expect(mockSocketOn).toHaveBeenCalledWith(
        'disconnect',
        expect.any(Function),
      );
    });
  });

  describe('joinChannel event', () => {
    it('should make the socket join a channel and log it', () => {
      const joinChannelCallback = mockSocketOn.mock.calls.find(
        call => call[0] === 'joinChannel',
      )[1];
      const channelId = 'test-channel';

      joinChannelCallback(channelId);

      expect(mockSocketJoin).toHaveBeenCalledWith(channelId);
      expect(logger.info).toHaveBeenCalledWith(
        `Socket test-socket-id joined channel ${channelId}`,
      );
    });
  });

  describe('sendMessage event', () => {
    it('should add a message and emit it to the channel', async () => {
      const sendMessageCallback = mockSocketOn.mock.calls.find(
        call => call[0] === 'sendMessage',
      )[1];
      const messageData: ChatMessageRequestDto = {
        id: 'msg-id-1',
        chatId: 'chat-id-1',
        senderId: 'sender-id-1',
        text: 'Hello!',
        createdOn: new Date(),
        deleted: false,
        deletedOn: null,
        updatedOn: new Date(),
        createdBy: 'user-1',
        deletedBy: null,
        updatedBy: null,
        ticketId: 'ticket-id-1',
      } as any;

      await sendMessageCallback(messageData);

      expect(logger.info).toHaveBeenCalledWith(
        'sendMessage event received on the server',
      );
      expect(mockAddMessage).toHaveBeenCalledWith(
        String(messageData.ticketId),
        messageData,
      );
      expect(mockServerTo).toHaveBeenCalledWith(String(messageData.ticketId));

      expect(mockServerEmit).toHaveBeenCalledWith(
        'receiveMessage',
        messageData,
      );
    });
  });

  describe('disconnect event', () => {
    it('should log socket disconnection', () => {
      const disconnectCallback = mockSocketOn.mock.calls.find(
        call => call[0] === 'disconnect',
      )[1];
      disconnectCallback();
      expect(logger.info).toHaveBeenCalledWith(
        'Socket disconnected: test-socket-id',
      );
    });
  });
});
