import {FindOptions} from 'sequelize';

import {ChatMessageRepository} from '@db/repositories';
import {EncryptionDecryptionService} from '@services/encryption-decryption.service';
import {
  mockNewMessage,
  mockEncryptedCreatedMessage,
  mockMessageList,
  mockDecryptedMessageList,
} from '../mock-data/chat.mock';
import {ChatMessage, ChatMessageRequestDto} from '@db/models';
import {ChatMessageService} from '@services/chat-message.service';
import {ChatService} from '@services/chat.service';

// Mock the dependencies
jest.mock('@db/repositories');
jest.mock('@services/encryption-decryption.service');
jest.mock('@services/chat.service');

describe('ChatService', () => {
  let chatMessageService: ChatMessageService;
  let chatMessageRepository: jest.Mocked<ChatMessageRepository>;
  let encryptionDecryptionService: jest.Mocked<EncryptionDecryptionService>;
  let chatService: jest.Mocked<ChatService>;

  beforeEach(() => {
    // Instantiate the mocked repository
    chatMessageRepository =
      new ChatMessageRepository() as jest.Mocked<ChatMessageRepository>;

    // Create a manual mock for the service with a private constructor
    encryptionDecryptionService = {
      encrypt: jest.fn(),
      decrypt: jest.fn(),
    } as unknown as jest.Mocked<EncryptionDecryptionService>;

    // Instantiate the service with the mocked dependencies
    chatService = new ChatService(
      null as any,
      null as any,
    ) as jest.Mocked<ChatService>;

    // Instantiate the service with the mocked dependencies
    chatMessageService = new ChatMessageService(
      chatMessageRepository,
      encryptionDecryptionService,
      chatService,
    );
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('addMessage', () => {
    it('should encrypt the message text and create a new message', async () => {
      const chatId = 'chat-session-123';
      const messageData = {...mockNewMessage} as ChatMessageRequestDto;
      const originalText = messageData.text;
      const encryptedText = 'encrypted-text';

      // Configure mock behavior
      encryptionDecryptionService.encrypt.mockReturnValue(encryptedText);
      chatMessageRepository.create.mockResolvedValue(
        mockEncryptedCreatedMessage,
      );
      chatService.getChats.mockResolvedValue([
        {
          id: chatId,
          members: [{memberId: messageData.senderId}],
        },
      ] as any);

      const result = await chatMessageService.addMessage(chatId, messageData);

      // Assertions
      expect(encryptionDecryptionService.encrypt).toHaveBeenCalledWith(
        originalText,
      );
      const expectedMessageData = {...messageData};
      delete expectedMessageData?.ticketId;
      expect(chatMessageRepository.create).toHaveBeenCalledWith({
        ...expectedMessageData,
        chatId,
        text: encryptedText,
      });
      expect(result).toEqual(mockEncryptedCreatedMessage);
    });

    it('should handle messages with no text content', async () => {
      const chatId = 'chat-session-123';
      const messageData = {
        ...mockNewMessage,
        text: null,
      } as ChatMessageRequestDto;

      chatMessageRepository.create.mockResolvedValue(messageData as any);
      chatService.getChats.mockResolvedValue([
        {
          id: chatId,
          members: [{memberId: messageData.senderId}],
        },
      ] as any);

      const result = await chatMessageService.addMessage(chatId, messageData);

      expect(encryptionDecryptionService.encrypt).not.toHaveBeenCalled();
      const expectedMessageData = {...messageData};
      delete expectedMessageData?.ticketId;
      expect(chatMessageRepository.create).toHaveBeenCalledWith({
        ...expectedMessageData,
        chatId,
      });
      expect(result).toEqual(messageData);
    });
  });

  describe('getChatMessages', () => {
    it('should fetch and decrypt all messages for a given chat ID', async () => {
      const chatId = 'chat-session-123';
      const options: FindOptions = {where: {chatId}};

      // Configure mock behavior
      chatMessageRepository.findAll.mockResolvedValue([...mockMessageList]);
      mockMessageList.forEach((msg: ChatMessage, index: number) => {
        if (msg.text) {
          encryptionDecryptionService.decrypt.mockReturnValueOnce(
            mockDecryptedMessageList[index].text!,
          );
        }
      });

      const result = await chatMessageService.getChatMessages(chatId, options);

      // Assertions
      expect(chatMessageRepository.findAll).toHaveBeenCalledWith(options);
      expect(encryptionDecryptionService.decrypt).toHaveBeenCalledTimes(2); // Two messages have text
      expect(result).toEqual(mockDecryptedMessageList);
    });

    it('should return an empty array if no messages are found', async () => {
      const chatId = 'non-existent-chat';
      const options: FindOptions = {where: {chatId}};

      chatMessageRepository.findAll.mockResolvedValue([]);

      const result = await chatMessageService.getChatMessages(chatId, options);

      expect(chatMessageRepository.findAll).toHaveBeenCalledWith(options);
      expect(encryptionDecryptionService.decrypt).not.toHaveBeenCalled();
      expect(result).toEqual([]);
    });
  });
});
