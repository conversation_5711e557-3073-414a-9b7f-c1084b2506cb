/**
 * Interface for the payload of a new chat message.
 */
export interface IChatMessagePayload {
  sender: string;
  message: string;
  timestamp?: string;
}

/**
 * Interface for a chat message object.
 */
export interface IChatMessage {
  id: string;
  sender: string;
  message: string;
  timestamp: string;
}

/**
 * Interface for filtering chat messages.
 */
export interface IChatMessageFilter {
  sender?: string;
  keyword?: string;
  limit?: number;
  offset?: number;
}
