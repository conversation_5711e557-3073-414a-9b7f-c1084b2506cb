import {Request, Response, NextFunction} from 'express';
import {
  validate,
  ValidationError as ClassValidatorError,
} from 'class-validator';
import {plainToInstance} from 'class-transformer';
import {ValidationError} from '@utils/errors';

export const validateDto = (dtoClass: any) => {
  return async (req: Request, _res: Response, next: NextFunction) => {
    try {
      // Transform plain object to class instance
      const dtoInstance = plainToInstance(dtoClass, req.body);
      // Validate the DTO
      const errors = await validate(dtoInstance, {
        whitelist: true,
        forbidNonWhitelisted: true,
      });

      if (errors.length > 0) {
        // Format validation errors
        const formattedErrors: Record<string, string[]> = {};

        errors.forEach((error: ClassValidatorError) => {
          const property = error.property;
          const constraints = error.constraints || {};

          formattedErrors[property] = Object.values(constraints);
        });

        throw new ValidationError('Validation failed', formattedErrors);
      }

      // Add validated data to request
      req.body = dtoInstance;
      next();
    } catch (error) {
      next(error);
    }
  };
};
