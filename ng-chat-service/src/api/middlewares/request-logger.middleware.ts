import {Request, Response, NextFunction} from 'express';
import {v4 as uuidv4} from 'uuid';
import logger from '@utils/logger';

// Extend Express Request interface to include id
declare global {
  // eslint-disable-next-line @typescript-eslint/no-namespace
  namespace Express {
    interface Request {
      id?: string;
      startTime?: [number, number];
    }
  }
}

// Generate unique ID for each request
export const requestId = (
  req: Request,
  _res: Response,
  next: NextFunction,
): void => {
  req.id = uuidv4();
  next();
};

// Log request details
export const requestLogger = (
  req: Request,
  res: Response,
  next: NextFunction,
): void => {
  // Record start time
  req.startTime = process.hrtime();

  // Log incoming request
  logger.info(`[${req.id}] Request: ${req.method} ${req.url}`, {
    method: req.method,
    url: req.url,
    ip: req.ip,
    userAgent: req.get('user-agent'),
  });

  // Log response when finished
  res.on('finish', () => {
    // Calculate request duration
    const hrtime = process.hrtime(req.startTime);
    const duration = hrtime[0] * 1000 + hrtime[1] / 1000000;

    const logLevel = res.statusCode >= 400 ? 'warn' : 'info';

    logger[logLevel](
      `[${req.id}] Response: ${res.statusCode} (${duration.toFixed(2)}ms)`,
      {
        statusCode: res.statusCode,
        duration: `${duration.toFixed(2)}ms`,
      },
    );
  });

  next();
};
