import {Request, Response, NextFunction} from 'express';
import {Model, ModelStatic, FindOptions} from 'sequelize';

/**
 * Configuration for filter validation.
 */
export interface FilterConfig<T extends Model> {
  allowedAttributes?: (keyof T | string)[];
  maxLimit?: number;
}

/**
 * Validates that all attributes in the where clause are allowed.
 * @param whereClause - The where clause object.
 * @param allowedAttributes - A list of allowed attribute names.
 */
function validateWhereClause<T extends Model>(
  whereClause: any,
  allowedAttributes: (keyof T | string)[],
) {
  if (!whereClause || typeof whereClause !== 'object') {
    throw new Error('Where clause must be an object.');
  }

  for (const key in whereClause) {
    if (!allowedAttributes.includes(key)) {
      throw new Error(`Filtering by attribute '${key}' is not allowed.`);
    }
  }
}

/**
 * A middleware factory that creates a filter-parsing middleware for a given Sequelize model.
 * It parses both a 'filter' JSON query parameter and direct query parameters.
 *
 * @param model The Sequelize model to apply filters for.
 * @param config Configuration for allowed attributes and limits.
 * @returns An Express middleware function.
 */
export function applyFilters<T extends Model>(
  model: ModelStatic<T>,
  config: Partial<FilterConfig<T>> = {},
) {
  return (req: Request, res: Response, next: NextFunction): void => {
    try {
      const {filter, limit, skip, order, ...where} = req.query;
      let options: FindOptions = {};

      // 1. Parse the 'filter' query parameter if it exists.
      if (filter && typeof filter === 'string') {
        try {
          options = JSON.parse(filter);
        } catch (error) {
          res.status(400).json({message: 'Invalid JSON in filter parameter.'});
          return;
        }
      }

      // 2. Layer direct query parameters (e.g., ?senderId=123) on top.
      if (Object.keys(where).length > 0) {
        options.where = {...(options.where || {}), ...where};
      }

      // 3. Determine configuration and defaults.
      const modelAttributes = Object.keys(model.getAttributes());
      const {allowedAttributes = modelAttributes, maxLimit = 100} = config;

      // 4. Validate and apply limit, skip, order, and where clauses.
      if (limit || options.limit) {
        const numLimit = parseInt((limit || options.limit) as string, 10);
        if (isNaN(numLimit) || numLimit <= 0) {
          res.status(400).json({message: 'Limit must be a positive number.'});
          return;
        }
        options.limit = Math.min(numLimit, maxLimit);
      }

      if (skip || options.offset) {
        const numSkip = parseInt((skip || options.offset) as string, 10);
        if (isNaN(numSkip) || numSkip < 0) {
          res
            .status(400)
            .json({message: 'Skip must be a non-negative number.'});
          return;
        }
        options.offset = numSkip;
      }

      if (order || options.order) {
        const orderField = (order as string) || (options.order as any);
        if (typeof orderField === 'string') {
          if (!allowedAttributes.includes(orderField)) {
            res.status(400).json({
              message: `Ordering by attribute '${orderField}' is not allowed.`,
            });
            return;
          }
          options.order = [[orderField, 'ASC']];
        } else if (Array.isArray(orderField)) {
          options.order = orderField.filter(
            item => Array.isArray(item) && allowedAttributes.includes(item[0]),
          );
        }
      }

      if (options.where) {
        validateWhereClause(options.where, allowedAttributes);
      }

      req.findOptions = options;
      next();
    } catch (error: any) {
      res.status(400).json({message: error.message});
    }
  };
}
