import {Request, Response, NextFunction} from 'express';
import {ValidationError as SequelizeValidationError} from 'sequelize';
import {AppError, ValidationError} from '@utils/errors';
import logger from '@utils/logger';
import {errorResponse} from '@utils/response';

const handleSequelizeValidationError = (
  err: SequelizeValidationError,
): ValidationError => {
  const errors: Record<string, string[]> = {};
  err.errors.forEach(error => {
    if (error.path) {
      if (!errors[error.path]) {
        errors[error.path] = [];
      }
      errors[error.path].push(error.message);
    }
  });
  return new ValidationError('Validation failed', errors);
};

export const errorMiddleware = (
  err: Error,
  req: Request,
  res: Response,
  _next: NextFunction,
): void => {
  if (err instanceof SequelizeValidationError) {
    const validationError = handleSequelizeValidationError(err);
    res.status(validationError.statusCode).json({
      message: validationError.message,
      errors: validationError.errors,
    });
    return;
  }
  let statusCode = 500;
  let message = 'Internal Server Error';
  let errors: Record<string, string[]> | undefined;
  if (err instanceof AppError) {
    statusCode = err.statusCode;
    message = err.message;

    if ('errors' in err && err.errors) {
      errors = err.errors as Record<string, string[]>;
    }
  }

  // Log the error
  logger.error(`[${req.id ?? 'UNKNOWN'}] Error: ${message}`, {
    path: `${req.method} ${req.path}`,
    error: err.stack,
    statusCode,
  });

  // Send error response
  errorResponse(res, message, statusCode, errors);
};
