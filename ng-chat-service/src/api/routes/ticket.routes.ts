import {Router} from 'express';
import {validateDto} from '@middlewares/validation.middleware';
import {authenticate} from '@middlewares/authenticate.middleware';
import {CreateTicketDto, UpdateTicketDto} from '@dtos/ticket.dto';
import {TicketHistoryRepository, TicketRepository} from '@db/repositories';
import {TicketController} from '@controllers/ticket.controller';
import {TicketService} from '@services/ticket.service';

const router = Router();

// Instantiate controller with repository
const ticketController = new TicketController(
  new TicketRepository(),
  new TicketService(new TicketRepository(), new TicketHistoryRepository()),
);

router.use(authenticate);

/**
 * @swagger
 * tags:
 *   name: Tickets
 *   description: Ticket management
 */

/**
 * @swagger
 * /tickets:
 *   get:
 *     summary: Get all tickets
 *     tags: [Tickets]
 *     responses:
 *       200:
 *         description: Tickets retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/TicketResponseDto'
 *     security:
 *       - bearerAuth: []
 */
router.get('/', ticketController.findAll.bind(ticketController));

/**
 * @swagger
 * /tickets/{id}:
 *   get:
 *     summary: Get a ticket by ID
 *     tags: [Tickets]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Ticket ID
 *     responses:
 *       200:
 *         description: Ticket retrieved
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/TicketResponseDto'
 *       404:
 *         description: Ticket not found
 *     security:
 *       - bearerAuth: []
 */
router.get('/:id', ticketController.findById.bind(ticketController));

/**
 * @swagger
 * /tickets:
 *   post:
 *     summary: Create a new ticket
 *     tags: [Tickets]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CreateTicketDto'
 *     responses:
 *       201:
 *         description: Ticket created successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/TicketResponseDto'
 *       400:
 *         description: Validation error
 *     security:
 *       - bearerAuth: []
 */
router.post(
  '/',
  validateDto(CreateTicketDto),
  ticketController.create.bind(ticketController),
);

/**
 * @swagger
 * /tickets/{id}:
 *   patch:
 *     summary: Update a ticket
 *     tags: [Tickets]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Ticket ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/UpdateTicketDto'
 *     responses:
 *       200:
 *         description: Ticket updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/TicketResponseDto'
 *       404:
 *         description: Ticket not found
 *     security:
 *       - bearerAuth: []
 */
router.patch(
  '/:id',
  validateDto(UpdateTicketDto),
  ticketController.update.bind(ticketController),
);

/**
 * @swagger
 * /tickets/{id}:
 *   delete:
 *     summary: Delete a ticket
 *     tags: [Tickets]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Ticket ID
 *     responses:
 *       204:
 *         description: Ticket deleted successfully
 *       404:
 *         description: Ticket not found
 *     security:
 *       - bearerAuth: []
 */
router.delete('/:id', ticketController.delete.bind(ticketController));

export default router;
