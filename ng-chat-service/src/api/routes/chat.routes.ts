import {Router} from 'express';

import {ChatService} from '@services/chat.service';
import {
  ChatRepository,
  ChatMembersRepository,
  TicketRepository,
  TicketHistoryRepository,
} from '@db/repositories';
import {Chat<PERSON>ontroller} from '@controllers/chat.controller';
import {ChatMemberService} from '@services/chat-member.service';
import {TicketService} from '@services/ticket.service';

const router = Router();

(async () => {
  const chatRepository = new ChatRepository();
  const chatMembersRepository = new ChatMembersRepository();
  const chatService = new ChatService(chatRepository, chatMembersRepository);
  const chatMemberService = new ChatMemberService(
    chatMembersRepository,
    chatService,
  );
  const ticketService = new TicketService(
    new TicketRepository(),
    new TicketHistoryRepository(),
  );
  const chatController = new ChatController(
    chatService,
    chatMemberService,
    ticketService,
  );

  router.post('/create', chatController.createChat.bind(chatController));
  router.get('/:chatId', chatController.getChatById.bind(chatController));
  //mock api to start conversation and add user to chat
  router.post(
    '/start-conversation',
    chatController.startConversation.bind(chatController),
  );
  //mock api to accept/reject conversation and add user to chat
  router.post(
    '/tickets/:ticketId/agents/:agentId/action/:action',
    chatController.acceptOrRejectChat.bind(chatController),
  );
})();

/**
 * @swagger
 * tags:
 *   name: Chat
 *   description: API for managing chats
 */

export default router;
