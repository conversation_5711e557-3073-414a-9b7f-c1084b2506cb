import {Router} from 'express';
import {ChatMessageController} from '@controllers/chat-message.controller';
import {applyFilters} from '@middlewares/filter.middleware';
import {ChatMessageService} from '@services/chat-message.service';
import {EncryptionDecryptionService} from '@services/encryption-decryption.service';
import {
  ChatMessageRepository,
  ChatMembersRepository,
  ChatRepository,
} from '@db/repositories';
import {ChatMessage} from '@db/models';
import {ChatService} from '@services/chat.service';

const router = Router();

(async () => {
  const chatMessageRepository = new ChatMessageRepository();
  const chatRepository = new ChatRepository();
  const chatMembersRepository = new ChatMembersRepository();
  const chatService = new ChatService(chatRepository, chatMembersRepository);
  const encryptionDecryptionService =
    await EncryptionDecryptionService.create();
  const chatMessageService = new ChatMessageService(
    chatMessageRepository,
    encryptionDecryptionService,
    chatService,
  );
  const chatMessageController = new ChatMessageController(chatMessageService);

  router.post(
    '/:chatId/messages',
    chatMessageController.addChatMessage.bind(chatMessageController),
  );
  router.get(
    '/:chatId/messages',
    applyFilters(ChatMessage),
    chatMessageController.getChatMessages.bind(chatMessageController),
  );
})();

/**
 * @swagger
 * tags:
 *   name: Chat Messages
 *   description: API for managing chat messages
 */

export default router;
