import {Router} from 'express';
import {validateDto} from '@middlewares/validation.middleware';
import {authenticate} from '@middlewares/authenticate.middleware';
import {CreateTicketHistoryDto} from '@dtos/ticket-history.dto';
import {TicketHistoryRepository} from '@db/repositories';
import {TicketHistoryController} from '@controllers/ticket-history.controller';

const router = Router();

// Instantiate controller with repository
const ticketHistoryController = new TicketHistoryController(
  new TicketHistoryRepository(),
);

router.use(authenticate);

/**
 * @swagger
 * tags:
 *   name: TicketHistory
 *   description: Ticket audit logs
 */

/**
 * @swagger
 * /ticket-history:
 *   get:
 *     summary: Get all ticket history records
 *     tags: [TicketHistory]
 *     responses:
 *       200:
 *         description: History retrieved
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/TicketHistoryResponseDto'
 *     security:
 *       - bearerAuth: []
 */
router.get('/', ticketHistoryController.findAll.bind(ticketHistoryController));

/**
 * @swagger
 * /ticket-history/{id}:
 *   get:
 *     summary: Get ticket history by ID
 *     tags: [TicketHistory]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: History found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/TicketHistoryResponseDto'
 *       404:
 *         description: History not found
 *     security:
 *       - bearerAuth: []
 */
router.get(
  '/:id',
  ticketHistoryController.findById.bind(ticketHistoryController),
);

/**
 * @swagger
 * /ticket-history:
 *   post:
 *     summary: Create ticket history
 *     tags: [TicketHistory]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CreateTicketHistoryDto'
 *     responses:
 *       201:
 *         description: History created
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/TicketHistoryResponseDto'
 *       400:
 *         description: Validation error
 *     security:
 *       - bearerAuth: []
 */
router.post(
  '/',
  validateDto(CreateTicketHistoryDto),
  ticketHistoryController.create.bind(ticketHistoryController),
);

/**
 * @swagger
 * /ticket-history/{id}:
 *   delete:
 *     summary: Delete ticket history record
 *     tags: [TicketHistory]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       204:
 *         description: Deleted
 *       404:
 *         description: Record not found
 *     security:
 *       - bearerAuth: []
 */
router.delete(
  '/:id',
  ticketHistoryController.delete.bind(ticketHistoryController),
);

export default router;
