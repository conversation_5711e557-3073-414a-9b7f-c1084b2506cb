import {HealthController} from '@controllers/health.controller';
import {Router} from 'express';

const router = Router();

const healthController = new HealthController();

/**
 * @swagger
 * /health:
 *   get:
 *     summary: Check service health
 *     description: Returns the health status of the service and its dependencies
 *     tags: [Health]
 *     responses:
 *       200:
 *         description: Service is healthy
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: Service is healthy
 *                 data:
 *                   type: object
 */

router.get('/', healthController.check.bind(HealthController));

export default router;
