import {Router} from 'express';

import {ChatMemberService} from '@services/chat-member.service';
import {ChatService} from '@services/chat.service';
import {ChatMembersRepository, ChatRepository} from '@db/repositories';
import {ChatMemberController} from '@controllers/chat-member.controller';

const router = Router();

(async () => {
  const chatMembersRepository = new ChatMembersRepository();
  const chatRepository = new ChatRepository();
  const chatService = new ChatService(chatRepository, chatMembersRepository);
  const chatMemberService = new ChatMemberService(
    chatMembersRepository,
    chatService,
  );
  const chatMemberController = new ChatMemberController(chatMemberService);

  router.post(
    '/:chatId/member',
    chatMemberController.addChatMember.bind(chatMemberController),
  );
  router.delete(
    '/:chatId/member/:memberId',
    chatMemberController.removeChatMember.bind(chatMemberController),
  );
  router.patch(
    '/:chatId/member/:memberId',
    chatMemberController.updateChatMember.bind(chatMemberController),
  );
})();

/**
 * @swagger
 * tags:
 *   name: Chat Members
 *   description: API for managing chat members
 */

export default router;
