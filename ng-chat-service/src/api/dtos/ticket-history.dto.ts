import {TicketActionType} from '@enums/ticket.enum';
import {IsString, IsNotEmpty, IsOptional, IsEnum} from 'class-validator';

/**
 * @swagger
 * components:
 *   schemas:
 *     CreateTicketHistoryDto:
 *       type: object
 *       required:
 *         - ticketId
 *         - action
 *       properties:
 *         ticketId:
 *           type: string
 *         action:
 *           type: string
 *           enum: [CREATED, STATUS_CHANGED, ASSIGNEE_CHANGED]
 *         previousValue:
 *           type: string
 *         newValue:
 *           type: string
 *       example:
 *         ticketId: "fa34a5ee-3fd5-4201-b7a0-eaef45d1ee2b"
 *         action: STATUS_CHANGED
 *         previousValue: OPEN
 *         newValue: IN_PROGRESS
 */

export class CreateTicketHistoryDto {
  @IsString()
  @IsNotEmpty()
  ticketId!: string;

  @IsEnum(TicketActionType)
  action!: TicketActionType;

  @IsOptional()
  @IsString()
  previousValue?: string;

  @IsOptional()
  @IsString()
  newValue?: string;
}

/**
 * @swagger
 *   TicketHistoryResponseDto:
 *     type: object
 *     properties:
 *       id:
 *         type: string
 *       ticketId:
 *         type: string
 *       action:
 *         type: string
 *       previousValue:
 *         type: string
 *       newValue:
 *         type: string
 *       createdOn:
 *         type: string
 *         format: date-time
 *     example:
 *       id: "history-id-123"
 *       ticketId: "ticket-id-123"
 *       action: STATUS_CHANGED
 *       previousValue: "OPEN"
 *       newValue: "IN_PROGRESS"
 *       createdOn: "2024-06-01T12:00:00Z"
 */
