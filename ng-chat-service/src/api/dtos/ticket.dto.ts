import {TicketPriority, TicketStatus} from '@enums/ticket.enum';
import {
  IsString,
  IsNotEmpty,
  IsOptional,
  IsEnum,
  MaxLength,
} from 'class-validator';

/**
 * @swagger
 * components:
 *   schemas:
 *     CreateTicketDto:
 *       type: object
 *       required:
 *         - title
 *         - shortCode
 *       properties:
 *         title:
 *           type: string
 *           maxLength: 255
 *         description:
 *           type: string
 *         priority:
 *           type: string
 *           enum: [LOW, MEDIUM, HIGH, CRITICAL]
 *         shortCode:
 *           type: string
 *           description: A unique code for identifying the ticket
 *         status:
 *           type: string
 *           enum: [OPEN, IN_PROGRESS, RESOLVED, CLOSED]
 *         assigneeId:
 *           type: string
 *       example:
 *         title: Login issue on dashboard
 *         description: User unable to log in with valid credentials.
 *         priority: HIGH
 *         shortCode: "TCK-0001"
 *         status: OPEN
 *         assigneeId: "9b162e5e-2fdc-4b0d-ae34-bf8c7cddca81"
 */

export class CreateTicketDto {
  @IsString()
  @IsNotEmpty()
  @MaxLength(255)
  title!: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  @IsEnum(TicketPriority)
  priority?: TicketPriority;

  @IsString()
  @IsOptional()
  shortCode?: string;

  @IsOptional()
  @IsEnum(TicketStatus)
  status?: TicketStatus;

  @IsOptional()
  @IsString()
  assigneeId?: string;
}

/**
 * @swagger
 *   UpdateTicketDto:
 *     type: object
 *     properties:
 *       title:
 *         type: string
 *       description:
 *         type: string
 *       priority:
 *         type: string
 *         enum: [LOW, MEDIUM, HIGH, CRITICAL]
 *       shortCode:
 *         type: string
 *       status:
 *         type: string
 *         enum: [OPEN, IN_PROGRESS, RESOLVED, CLOSED]
 *       assigneeId:
 *         type: string
 *     example:
 *       status: IN_PROGRESS
 */

export class UpdateTicketDto {
  @IsOptional()
  @IsString()
  @MaxLength(255)
  title?: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  @IsEnum(TicketPriority)
  priority?: TicketPriority;

  @IsOptional()
  @IsString()
  shortCode?: string;

  @IsOptional()
  @IsEnum(TicketStatus)
  status?: TicketStatus;

  @IsOptional()
  @IsString()
  assigneeId?: string;
}

/**
 * @swagger
 *   TicketResponseDto:
 *     type: object
 *     properties:
 *       id:
 *         type: string
 *         format: uuid
 *       title:
 *         type: string
 *       shortCode:
 *         type: string
 *       priority:
 *         type: string
 *       status:
 *         type: string
 *       description:
 *         type: string
 *       assigneeId:
 *         type: string
 *       createdOn:
 *         type: string
 *         format: date-time
 *       updatedOn:
 *         type: string
 *         format: date-time
 *     example:
 *       id: "fa34a5ee-3fd5-4201-b7a0-eaef45d1ee2b"
 *       title: "Login issue"
 *       shortCode: "TCK-0001"
 *       priority: "HIGH"
 *       status: "OPEN"
 *       description: "User cannot log in"
 *       assigneeId: "user-123"
 *       createdOn: "2024-06-01T12:00:00Z"
 *       updatedOn: "2024-06-01T12:00:00Z"
 */
