import {TicketRepository, TicketHistoryRepository} from '@db/repositories';
import {
  Ticket,
  TicketCreationAttributes,
  TicketHistoryCreationAttributes,
} from '@db/models';
import {CreateTicketDto, UpdateTicketDto} from '@dtos/ticket.dto';
import logger from '@utils/logger';
import {TicketActionType} from '@enums/ticket.enum';
import {NotFoundError} from '@utils/errors';

export interface ITicketService {
  create(data: CreateTicketDto, userId: string): Promise<Ticket>;
  update(id: string, data: UpdateTicketDto, userId: string): Promise<Ticket>;
}

export class TicketService implements ITicketService {
  constructor(
    private readonly ticketRepository: TicketRepository,
    private readonly ticketHistoryRepository: TicketHistoryRepository,
  ) {}

  /**
   * Create a ticket with shortCode and initial history entry
   */
  async create(data: CreateTicketDto): Promise<Ticket> {
    const shortCode = this.generateShortCode();
    const ticketData = {
      ...data,
      shortCode,
    };

    const ticket = await this.ticketRepository.create(
      ticketData as TicketCreationAttributes,
    );

    // Log creation event to history
    await this.ticketHistoryRepository.create({
      ticketId: ticket.id,
      action: TicketActionType.CREATED,
      previousValue: null,
      newValue: ticket.status,
    });

    logger.info(`Ticket created with shortCode ${shortCode}`);

    return ticket;
  }

  /**
   * Update a ticket and log any status or assignee changes
   */
  async update(
    id: string,
    data: UpdateTicketDto,
    userId: string,
  ): Promise<Ticket> {
    const existingTicket = await this.ticketRepository.findById(id);
    if (!existingTicket) throw new NotFoundError('Ticket not found');

    const updatedTicket = await this.ticketRepository.update(id, data);

    const historyEvents: TicketHistoryCreationAttributes[] = [];

    if (data.status && data.status !== existingTicket.status) {
      historyEvents.push({
        ticketId: id,
        action: TicketActionType.STATUS_CHANGED,
        previousValue: existingTicket.status,
        newValue: data.status,
      });
    }

    if (data.assigneeId && data.assigneeId !== existingTicket.assigneeId) {
      historyEvents.push({
        ticketId: id,
        createdBy: userId,
        action: TicketActionType.ASSIGNEE_CHANGED,
        previousValue: existingTicket.assigneeId,
        newValue: data.assigneeId,
      });
    }

    // Save all history events
    await Promise.all(
      historyEvents.map(e => this.ticketHistoryRepository.create(e)),
    );

    logger.info(`Ticket ${id} updated by ${userId}`);

    return updatedTicket;
  }

  async delete(id: string): Promise<void> {
    const ticket = await this.ticketRepository.findById(id);
    if (!ticket) throw new NotFoundError('Ticket not found');

    await this.ticketRepository.delete(id);

    await this.ticketHistoryRepository.create({
      ticketId: id,
      action: TicketActionType.DELETED,
      previousValue: null,
      newValue: TicketActionType.DELETED,
    });

    logger.info(`Ticket ${id} deleted`);
  }

  /**
   * Generate a shortCode like TCK-20240625-001
   */
  private generateShortCode(): string {
    const now = new Date();
    const datePart = now.toISOString().slice(0, 10).replace(/-/g, '');
    const random = Math.floor(100 + Math.random() * 900); // 3-digit
    return `TCK-${datePart}-${random}`;
  }
}
