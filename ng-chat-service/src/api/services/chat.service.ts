import {FindOptions} from 'sequelize';
import {ChatMembersRepository, ChatRepository} from '@db/repositories';
import {Chat, ChatMember} from '@db/models';
import logger from '@utils/logger';
import {ChatMemberStatus} from '../../enums';

export interface IChatService {
  createChat(ticketId: string, chatMembers: ChatMember[]): Promise<Chat | null>;
  getChatById(chatId: string, options?: FindOptions): Promise<Chat | null>;
}

/**
 * @class ChatService
 * @description This service is responsible for all business logic related to chat.
 */
export class ChatService implements IChatService {
  constructor(
    private readonly chatRepository: ChatRepository,
    private readonly chatMembersRepository: ChatMembersRepository,
  ) {}
  /**
   * The function `createChat` creates a new chat associated with a ticket and adds members to the chat
   * with default status if not provided.
   * @param {string} ticketId - The `ticketId` parameter in the `createChat` function represents the
   * unique identifier of the ticket for which the chat is being created. It is used to associate the
   * chat with a specific ticket in the system.
   * @param {ChatMembers[]} chatMembers - The `chatMembers` parameter in the `createChat` function is an
   * array of objects representing the members of the chat. Each object in the array contains information
   * about a chat member, such as their user ID, display name, and status. The function creates a new
   * chat instance and associates the provided
   * @returns The `createChat` function returns a Promise that resolves to a `Chat` object after creating
   * a new chat instance, associating members with the chat, and adding members to the chat.
   */
  async createChat(
    ticketId: string,
    chatMembers: ChatMember[],
  ): Promise<Chat | null> {
    logger.info(
      `Create a chat for ticket ${ticketId}  where members are ${JSON.stringify(
        chatMembers,
      )}`,
    );

    // Create a new chat instance
    const chat = await this.chatRepository.create({
      ticketId,
    });
    logger.info(`Chat created with ID: ${chat.id}`);
    // Associate members with the chat
    const members = chatMembers.map(member => ({
      ...member,
      chatId: chat.id,
      status: member?.status ?? ChatMemberStatus.ACTIVE, // Default status if not provided
    }));
    await this.chatMembersRepository.bulkCreate(members);
    logger.info(`Members added to chat ${chat.id}`);
    return this.getChatById(chat.id);
  }

  /**
   * This function asynchronously retrieves a chat by its ID using the provided options.
   * @param {string} chatId - The `chatId` parameter is a string that represents the unique identifier
   * of the chat you want to retrieve.
   * @param {FindOptions} [options] - The `options` parameter in the `getChatById` function is of type
   * `FindOptions`. It is an optional parameter that allows you to specify additional options for
   * finding the chat. These options could include things like filtering criteria, sorting options, or
   * any other parameters that can be used to customize the
   * @returns The `getChatById` function returns a Promise that resolves to either a `Chat` object or
   * `null`.
   */
  async getChatById(
    chatId: string,
    options?: FindOptions,
  ): Promise<Chat | null> {
    logger.info(`Fetching chat with ID ${chatId}`);
    return this.chatRepository.findById(chatId, {
      ...options,
      include: [
        {
          model: ChatMember,
          as: 'members',
        },
      ],
    });
  }

  /**
   * This function asynchronously retrieves a chat by its ID using the provided options.
   * @param {string} chatId - The `chatId` parameter is a string that represents the unique identifier
   * of the chat you want to retrieve.
   * @param {FindOptions} [options] - The `options` parameter in the `getChatById` function is of type
   * `FindOptions`. It is an optional parameter that allows you to specify additional options for
   * finding the chat. These options could include things like filtering criteria, sorting options, or
   * any other parameters that can be used to customize the
   * @returns The `getChatById` function returns a Promise that resolves to either a `Chat` object or
   * `null`.
   */
  async getChats(options?: FindOptions): Promise<Chat[]> {
    logger.info(`Fetching chats with options: ${JSON.stringify(options)}`);
    return this.chatRepository.findAll({
      ...options,
      include: [
        {
          model: ChatMember,
          as: 'members',
        },
      ],
    });
  }
}
