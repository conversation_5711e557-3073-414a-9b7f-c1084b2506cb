import {ChatMembersRepository} from '@db/repositories';
import {Chat, ChatMember} from '@db/models';
import logger from '@utils/logger';
import {BadRequestError} from '@utils/errors';
import {ChatService} from './chat.service';

export interface IChatMemberService {
  addChatMember(chatId: string, member: ChatMember): Promise<Chat | null>;
  removeChatMember(chatId: string, memberId: string): Promise<Chat | null>;
  updateChatMember(
    chatId: string,
    memberId: string,
    member: Partial<ChatMember>,
  ): Promise<Chat | null>;
}

/**
 * @class ChatMemberService
 * @description This service is responsible for all business logic related to chat members.
 */
export class ChatMemberService implements IChatMemberService {
  constructor(
    private readonly chatMembersRepository: ChatMembersRepository,
    private readonly chatService: ChatService,
  ) {}

  /**
   * The function `addChatMember` adds a member to a chat and returns the updated chat details.
   * @param {string} chatId - The `chatId` parameter is a string that represents the unique identifier
   * of the chat to which a member is being added.
   * @param {ChatMembers} member - The `member` parameter in the `addChatMember` function represents the
   * details of a new member that you want to add to a chat. It could include information such as the
   * member's username, display name, role in the chat, and any other relevant details needed to
   * identify and manage chat members
   * @returns The `addChatMember` function returns a Promise that resolves to either a `Chat` object or
   * `null`.
   */
  async addChatMember(
    chatId: string,
    member: ChatMember,
  ): Promise<Chat | null> {
    //verify if the chat exists
    const chat = await this.chatService.getChatById(chatId);
    if (!chat) {
      logger.error(`Chat with ID ${chatId} does not exist.`);
      throw new BadRequestError(`Chat with ID ${chatId} does not exist.`);
    }
    logger.info(`Adding member ${JSON.stringify(member)} to chat ${chatId}`);
    // Set the chatId for the member
    member.chatId = chatId;
    await this.chatMembersRepository.create(member);
    // Create the chat member and return latest chat details
    return this.chatService.getChatById(chatId);
  }

  /**
   * The `removeChatMember` function asynchronously removes a member from a chat and returns the updated
   * chat details.
   * @param {string} chatId - The `chatId` parameter is a string that represents the unique identifier of
   * the chat from which you want to remove a member.
   * @param {string} memberId - The `memberId` parameter in the `removeChatMember` function represents
   * the unique identifier of the member that you want to remove from the chat. This ID is used to
   * identify the specific member within the chat and is essential for performing operations such as
   * updating the member's status to `INACTIVE`
   * @returns The `removeChatMember` function returns a Promise that resolves to either a `Chat` object
   * or `null`.
   */
  async removeChatMember(
    chatId: string,
    memberId: string,
  ): Promise<Chat | null> {
    //verify if the chat exists
    const member = await this.chatMembersRepository.findOne({
      where: {
        chatId: chatId,
        memberId: memberId,
      },
    });
    if (!member) {
      logger.error(
        `Member with ID ${memberId} does not exist in chat ${chatId}.`,
      );
      throw new BadRequestError(
        `Member with ID ${memberId} does not exist in chat ${chatId}.`,
      );
    }
    logger.info(
      `removing member ${JSON.stringify(memberId)} from the chat ${chatId}`,
    );
    await this.chatMembersRepository.delete(member.id);
    logger.info(`Member ${member.memberId} removed from chat ${chatId}`);

    // Create the chat member and return latest chat details
    return this.chatService.getChatById(chatId);
  }

  /**
   * The function `updateChatMember` updates a chat member's details and returns the updated chat
   * information.
   * @param {string} chatId - The `chatId` parameter is a string that represents the unique identifier
   * of the chat where the member is being updated.
   * @param {string} memberId - The `memberId` parameter in the `updateChatMember` function represents
   * the unique identifier of the member whose details are being updated in a chat. It is used to
   * identify the specific member within the chat and is essential for locating and updating the
   * member's information in the database.
   * @param member - The `updateChatMember` function you provided is used to update a chat member's
   * details in a chat. The `member` parameter is of type `Partial<ChatMember>`, which means it contains
   * partial data of the `ChatMember` object that needs to be updated.
   * @returns The `updateChatMember` function returns a Promise that resolves to either a `Chat` object
   * or `null`.
   */
  async updateChatMember(
    chatId: string,
    memberId: string,
    member: Partial<ChatMember>,
  ): Promise<Chat | null> {
    //verify if the chat exists
    const memberDetails = await this.chatMembersRepository.findOne({
      where: {
        chatId: chatId,
        memberId: memberId,
      },
    });
    if (!memberDetails?.id) {
      logger.error(
        `Member with ID ${memberId} does not exist in chat ${chatId}.`,
      );
      throw new BadRequestError(
        `Member with ID ${memberId} does not exist in chat ${chatId}.`,
      );
    }
    logger.info(
      `updating member ${JSON.stringify(memberId)} for the chat ${chatId}`,
    );
    await this.chatMembersRepository.update(memberDetails?.id, member);
    logger.info(`Member ${member.memberId} updated for chat ${chatId}`);
    // Create the chat member and return latest chat details
    return this.chatService.getChatById(chatId);
  }
}
