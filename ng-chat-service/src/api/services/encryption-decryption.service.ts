import {createCipheriv, createDecipheriv, randomBytes, scrypt} from 'crypto';
import {promisify} from 'util';
import logger from '@utils/logger';
import config from '@config/index';

// IMPORTANT: These should be stored securely in environment variables.
const ENCRYPTION_KEY =
  config.encryptionKey || 'default_super_secret_key_must_be_32_bytes';
const SALT = config.encryptionSalt || '10';

const ALGORITHM = 'aes-256-gcm';
const IV_LENGTH = 16;
const AUTH_TAG_LENGTH = 16;

export interface IEncryptionDecryptionService {
  encrypt(text: string): string;
  decrypt(encryptedText: string): string;
}

export class EncryptionDecryptionService
  implements IEncryptionDecryptionService
{
  private key: Buffer;

  private constructor(key: Buffer) {
    this.key = key;
  }

  public static async create(): Promise<EncryptionDecryptionService> {
    const key = (await promisify(scrypt)(ENCRYPTION_KEY, SALT, 32)) as Buffer;
    return new EncryptionDecryptionService(key);
  }

  encrypt(text: string): string {
    const iv = randomBytes(IV_LENGTH);
    const cipher = createCipheriv(ALGORITHM, this.key, iv);
    const encrypted = Buffer.concat([
      cipher.update(text, 'utf8'),
      cipher.final(),
    ]);
    const tag = cipher.getAuthTag();
    return Buffer.concat([iv, tag, encrypted]).toString('hex');
  }

  decrypt(encryptedText: string): string {
    try {
      const data = Buffer.from(encryptedText, 'hex');
      const iv = data.slice(0, IV_LENGTH);
      const tag = data.slice(IV_LENGTH, IV_LENGTH + AUTH_TAG_LENGTH);
      const encrypted = data.slice(IV_LENGTH + AUTH_TAG_LENGTH);
      const decipher = createDecipheriv(ALGORITHM, this.key, iv);
      decipher.setAuthTag(tag);
      const decrypted = Buffer.concat([
        decipher.update(encrypted),
        decipher.final(),
      ]);
      return decrypted.toString('utf8');
    } catch (error) {
      logger.error(`Decryption failed for text: ${encryptedText}`, error);
      return encryptedText;
    }
  }
}
