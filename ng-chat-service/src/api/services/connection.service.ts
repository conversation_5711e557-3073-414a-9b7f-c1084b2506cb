import {Server as SocketIOServer, Socket} from 'socket.io';
import logger from '@utils/logger';
import {ChatMessage, ChatMessageRequestDto} from '@db/models';
import {
  ChatMembersRepository,
  ChatMessageRepository,
  ChatRepository,
} from '@db/repositories';
import {EncryptionDecryptionService} from './encryption-decryption.service';
import {ChatMessageService} from './chat-message.service';
import {ChatService} from './chat.service';
import {SocketEvent} from '../../enums/socket.enum';

export class ConnectionService {
  private readonly io: SocketIOServer;
  private chatMessageService!: ChatMessageService;
  private chatService!: ChatService;

  constructor(server: any, corsOrigin: string | string[]) {
    this.io = new SocketIOServer(server, {
      cors: {
        origin: corsOrigin,
        methods: ['GET', 'POST'],
      },
    });

    this.initialize();

    this.io.on(SocketEvent.CONNECTION, (socket: Socket) => {
      this.handleConnection(socket);
    });
  }

  private handleConnection(socket: Socket): void {
    logger.info(`Socket connected: ${socket.id}`);
    socket.emit(SocketEvent.SERVER_READY);

    socket.on(SocketEvent.JOIN_CHANNEL, (channelId: string) => {
      this.joinChannel(socket, channelId);
    });

    socket.on(SocketEvent.SEND_MESSAGE, (data: ChatMessageRequestDto) => {
      logger.info('sendMessage event received on the server');
      this.sendMessage(data);
    });

    socket.on(SocketEvent.DISCONNECT, () => {
      this.disconnect(socket);
    });
  }

  private joinChannel(socket: Socket, channelId: string): void {
    socket.join(channelId);
    logger.info(`Socket ${socket.id} joined channel ${channelId}`);
  }

  private async initialize(): Promise<void> {
    const encryptionDecryptionService =
      await EncryptionDecryptionService.create();
    const chatMessageRepository = new ChatMessageRepository();
    const chatRepository = new ChatRepository();
    const chatMembersRepository = new ChatMembersRepository();
    this.chatService = new ChatService(chatRepository, chatMembersRepository);

    this.chatMessageService = new ChatMessageService(
      chatMessageRepository,
      encryptionDecryptionService,
      this.chatService,
    );
  }

  private async sendMessage(data: ChatMessageRequestDto): Promise<void> {
    const {ticketId} = data;
    logger.info(`Received message: ${JSON.stringify(data)}`);

    await this.chatMessageService.addMessage(String(ticketId), data);

    this.io
      .to(String(ticketId))
      .emit('receiveMessage', this.receiveMessage(`${ticketId}`, data));
    logger.info(`Sent message: ${JSON.stringify(data)}`);
  }

  private async receiveMessage(
    ticketId: string,
    data: ChatMessage,
  ): Promise<void> {
    this.io.to(String(ticketId)).emit('receiveMessage', data);
    logger.info(`Sent message: ${JSON.stringify(data)}`);
  }

  private disconnect(socket: Socket): void {
    logger.info(`Socket disconnected: ${socket.id}`);
  }
}
