import {FindOptions} from 'sequelize';
import {ChatMessageRepository} from '@db/repositories';
import {ChatMessage, ChatMessageRequestDto} from '@db/models';
import logger from '@utils/logger';
import {EncryptionDecryptionService} from './encryption-decryption.service';
import {ChatService} from './chat.service';
import {BadRequestError} from '@utils/errors';

export interface IChatMessageService {
  addMessage(chatId: string, data: ChatMessage): Promise<ChatMessage>;
  getChatMessages(
    chatId: string,
    options?: FindOptions,
  ): Promise<ChatMessage[]>;
}

/**
 * @class ChatMessageService
 * @description This service is responsible for all business logic related to chat messages.
 */
export class ChatMessageService implements IChatMessageService {
  constructor(
    private readonly chatMessageRepository: ChatMessageRepository,
    private readonly encryptionDecryptionService: EncryptionDecryptionService,
    private readonly chatService: ChatService,
  ) {}

  /**
   * @function addMessage
   * @description A function to add a new chat message.
   * @param {ChatMessage} data - The message data.
   * @returns {Promise<ChatMessage>} - The created chat message.
   */
  async addMessage(
    ticketId: string,
    data: ChatMessageRequestDto,
  ): Promise<ChatMessage> {
    logger.info(
      `Add chat message for chat ${ticketId}, message is ${JSON.stringify(
        data,
      )}`,
    );
    //verify if sender is part of the chat
    const chat = await this.chatService.getChats({
      where: {ticketId},
    });
    logger.info(`Chat for ticket ${ticketId} is ${JSON.stringify(chat)}`);
    const chatMembers = chat?.[0]?.members?.map(res => res.memberId);
    logger.info(
      `members for ticket ${ticketId}, members are ${JSON.stringify(
        chatMembers,
      )}`,
    );
    if (!chatMembers?.includes(data.senderId)) {
      logger.error(
        `Sender ${data.senderId} is not part of ticket ${ticketId}. Cannot add message.`,
      );
      throw new BadRequestError(
        `Sender ${data.senderId} is not part of ticket ${ticketId}`,
      );
    }

    data.chatId = chat?.[0]?.id; // Set the chatId from the chat object
    const message = {...data};
    delete message.ticketId; // Remove ticketId as it's not needed in the message
    // Encrypt the message content before saving
    if (message.text) {
      message.text = this.encryptionDecryptionService.encrypt(message.text);
    }
    return this.chatMessageRepository.create(message);
  }

  /**
   * @function getChatMessages
   * @description A function to retrieve chat messages.
   * @param {FindOptions} [options] - Options for finding messages.
   * @returns {Promise<ChatMessage[]>} - A list of chat messages.
   */
  async getChatMessages(
    chatId: string,
    options?: FindOptions,
  ): Promise<ChatMessage[]> {
    logger.info(
      `Fetching chat messages for chat session ${chatId} and options ${JSON.stringify(
        options,
      )}`,
    );
    const filter = {where: {chatId}};

    if (options?.where) {
      options.where = {...options.where, chatId};
    } else {
      options = filter;
    }
    options.order = [['created_on', 'ASC']];
    const messages = await this.chatMessageRepository.findAll(options);

    // Decrypt the message content after retrieval
    return messages.map(message => {
      if (message.text) {
        message.text = this.encryptionDecryptionService.decrypt(message.text);
      }
      return message;
    });
  }
}
