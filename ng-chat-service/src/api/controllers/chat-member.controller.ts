import {Request, Response, NextFunction} from 'express';
import {ChatMemberService} from '../services/chat-member.service';
import logger from '@utils/logger';
import {successResponse} from '@utils/response';
import {BadRequestError} from '@utils/errors';

export class ChatMemberController {
  constructor(private readonly chatMemberService: ChatMemberService) {}

  public addChatMember = async (
    req: Request,
    res: Response,
    next: NextFunction,
  ): Promise<void> => {
    try {
      const {chatId} = req.params;
      const {member} = req.body;

      if (!member) {
        throw new BadRequestError('Please provide member');
      }

      logger.info(
        `Adding member to chat ${chatId}: ${JSON.stringify(req.body)}`,
      );

      const chat = await this.chatMemberService.addChatMember(chatId, member);
      successResponse(res, chat, 'Member Added Successfully');
    } catch (error) {
      next(error);
    }
  };

  public removeChatMember = async (
    req: Request,
    res: Response,
    next: NextFunction,
  ): Promise<void> => {
    try {
      const {chatId, memberId} = req.params;
      logger.info(`Removing member ${memberId} from chat ${chatId}`);
      const chat = await this.chatMemberService.removeChatMember(
        chatId,
        memberId,
      );
      successResponse(res, chat, 'Member Removed Successfully');
    } catch (error) {
      next(error);
    }
  };

  public updateChatMember = async (
    req: Request,
    res: Response,
    next: NextFunction,
  ): Promise<void> => {
    try {
      const {chatId, memberId} = req.params;
      const {member} = req.body;

      if (!member) {
        throw new BadRequestError('Please provide member');
      }

      logger.info(
        `Updating member ${memberId} in chat ${chatId}: ${JSON.stringify(
          req.body,
        )}`,
      );

      const chat = await this.chatMemberService.updateChatMember(
        chatId,
        memberId,
        member,
      );
      successResponse(res, chat, 'Member Updated Successfully');
    } catch (error) {
      next(error);
    }
  };
}
