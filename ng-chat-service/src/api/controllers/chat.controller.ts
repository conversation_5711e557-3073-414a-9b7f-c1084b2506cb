import {Request, Response, NextFunction} from 'express';
import {ChatService} from '../services/chat.service';
import logger from '@utils/logger';
import {successResponse} from '@utils/response';
import {BadRequestError} from '@utils/errors';
import {ChatMember} from '@db/models';
import {ChatMemberService} from '@services/chat-member.service';
import {MemberType} from '@enums/chat.enum';
import {TicketService} from '@services/ticket.service';
import {TicketPriority, TicketStatus} from '@enums/ticket.enum';

export class ChatController {
  constructor(
    private readonly chatService: ChatService,
    private readonly chatMemberService: ChatMemberService,
    private readonly ticketService: TicketService,
  ) {}

  public createChat = async (
    req: Request,
    res: Response,
    next: NextFunction,
  ): Promise<void> => {
    try {
      const {ticketId, members} = req.body;

      if (!ticketId || !members) {
        throw new BadRequestError('Please provide ticketId and members');
      }

      logger.info(
        `Creating chat for ticket ${ticketId}: ${JSON.stringify(req.body)}`,
      );

      const chat = await this.chatService.createChat(ticketId, members);
      successResponse(res, chat, 'Chat Created Successfully');
    } catch (error) {
      next(error);
    }
  };

  public getChatById = async (
    req: Request,
    res: Response,
    next: NextFunction,
  ): Promise<void> => {
    try {
      const {chatId} = req.params;
      logger.info(`Fetching chat with id ${chatId}`);
      const chat = await this.chatService.getChatById(chatId);
      successResponse(res, chat, 'Chat Retreived Successfully');
    } catch (error) {
      next(error);
    }
  };
  //mock api to start conversation
  public startConversation = async (
    req: Request,
    res: Response,
    next: NextFunction,
  ): Promise<void> => {
    try {
      const {userId} = req.body;

      logger.info(
        `Starting conversation for ticket with user ${userId}: ${JSON.stringify(
          req.body,
        )}`,
      );
      const ticket = await this.ticketService.create({
        title: '',
        priority: TicketPriority.MEDIUM,
        status: TicketStatus.OPEN,
      });
      //create a chat session
      const chat = await this.chatService.createChat(ticket.id, [
        {memberId: userId, memberType: MemberType.USER},
        {memberId: 'bot-1', memberType: MemberType.BOT},
      ] as ChatMember[]);

      //should communicate with the bot service to start the conversation
      successResponse(
        res,
        {
          message: 'Conversation started',
          chatId: chat?.id,
          ticketId: ticket.id,
        },
        'Conversation Started Successfully',
      );
    } catch (error) {
      next(error);
    }
  };
  public acceptOrRejectChat = async (
    req: Request,
    res: Response,
    next: NextFunction,
  ): Promise<void> => {
    try {
      const {ticketId, action} = req.params;
      const {agentId} = req.body;
      logger.info(
        `agent ${agentId} performed action ${action} on ticket ${ticketId}`,
      );
      //get chat by ticketId
      const chat = await this.chatService.getChats({where: {ticketId}});

      if (action === 'accept') {
        //add agent to the chat channel
        const updatedChat = await this.chatMemberService.addChatMember(
          chat[0]?.id,
          {
            memberId: agentId,
            memberType: MemberType.AGENT,
          } as ChatMember,
        );
        logger.info(
          `updated chat members with agent ${agentId} for ticket ${ticketId} is ${JSON.stringify(
            updatedChat,
          )}`,
        );

        successResponse(
          res,
          {chatId: chat?.[0]?.id, ticketId, agentId, action},
          'Agent added to chat successfully',
        );
      } else {
        successResponse(
          res,
          {ticketId, agentId, action},
          'Agent rejected the chat request successfully',
        );
      }
    } catch (error) {
      next(error);
    }
  };
}
