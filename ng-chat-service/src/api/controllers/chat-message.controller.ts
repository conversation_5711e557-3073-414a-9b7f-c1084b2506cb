import {Request, Response, NextFunction} from 'express';
import {ChatMessageService} from '../services/chat-message.service';
import {ChatMessageRequestDto} from '@db/models';
import logger from '@utils/logger';
import {successResponse} from '@utils/response';
import {BadRequestError} from '@utils/errors';

export class ChatMessageController {
  constructor(private readonly chatMessageService: ChatMessageService) {}

  /* The `addChatMessage` method in the `ChatMessageController` class is responsible for handling the
 logic to add a new chat message to a specific chat session. Here's a breakdown of what the method
 does: */
  public addChatMessage = async (
    req: Request,
    res: Response,
    next: NextFunction,
  ): Promise<void> => {
    try {
      const {chatId} = req.params;
      const {senderId, text} = req.body;

      if (!senderId || !text) {
        throw new BadRequestError('Please provide senderId and text');
      }

      logger.info(
        `Adding chat message for chat ${chatId}: ${JSON.stringify(req.body)}`,
      );

      const message = await this.chatMessageService.addMessage(chatId, {
        ...req.body,
        chatId: chatId,
      } as ChatMessageRequestDto);
      successResponse(res, message, 'Message Added Successfully');
    } catch (error) {
      next(error);
    }
  };

  /* The `getChatMessages` method in the `ChatMessageController` class is responsible for handling the
 logic to retrieve chat messages for a specific chat session. Here's a breakdown of what the method
 does: */
  public getChatMessages = async (
    req: Request,
    res: Response,
    next: NextFunction,
  ): Promise<void> => {
    try {
      const {chatId} = req.params;
      logger.info(`Fetching chat messages for chat session ${chatId}`);
      const messages = await this.chatMessageService.getChatMessages(
        chatId,
        req.findOptions,
      );
      successResponse(res, messages, 'Message Retreived Successfully');
    } catch (error) {
      next(error);
    }
  };
}
