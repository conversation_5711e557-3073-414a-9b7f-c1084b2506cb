import {Request, Response, NextFunction} from 'express';
import {successResponse} from '@utils/response';
import {TicketHistoryRepository} from '@db/repositories';

/**
 * Controller for ticket history operations
 */
export class TicketHistoryController {
  constructor(
    private readonly ticketHistoryRepository: TicketHistoryRepository,
  ) {}

  async create(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const history = await this.ticketHistoryRepository.create(
        req.body,
        req.user,
      );
      successResponse(res, history, 'Ticket history created successfully', 201);
    } catch (error) {
      next(error);
    }
  }

  async findAll(
    req: Request,
    res: Response,
    next: NextFunction,
  ): Promise<void> {
    try {
      const historyList = await this.ticketHistoryRepository.findAll();
      successResponse(
        res,
        historyList,
        'Ticket history retrieved successfully',
      );
    } catch (error) {
      next(error);
    }
  }

  async findById(
    req: Request,
    res: Response,
    next: NextFunction,
  ): Promise<void> {
    try {
      const history = await this.ticketHistoryRepository.findById(
        req.params.id,
      );
      successResponse(
        res,
        history,
        'Ticket history record retrieved successfully',
      );
    } catch (error) {
      next(error);
    }
  }

  async delete(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      await this.ticketHistoryRepository.delete(req.params.id);
      res.status(204).send();
    } catch (error) {
      next(error);
    }
  }
}
