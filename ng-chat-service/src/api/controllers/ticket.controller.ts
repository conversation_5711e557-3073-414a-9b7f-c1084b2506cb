import {Request, Response, NextFunction} from 'express';
import {successResponse} from '@utils/response';
import {TicketRepository} from '@db/repositories';
import {TicketService} from '../services/ticket.service';

/**
 * Controller for ticket operations
 */
export class TicketController {
  constructor(
    private readonly ticketRepository: TicketRepository,
    private readonly ticketService: TicketService,
  ) {}

  async create(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const ticket = await this.ticketService.create(req.body);
      successResponse(res, ticket, 'Ticket created successfully', 201);
    } catch (error) {
      next(error);
    }
  }

  async findAll(
    req: Request,
    res: Response,
    next: NextFunction,
  ): Promise<void> {
    try {
      const tickets = await this.ticketRepository.findAll();
      successResponse(res, tickets, 'Tickets retrieved successfully');
    } catch (error) {
      next(error);
    }
  }

  async findById(
    req: Request,
    res: Response,
    next: NextFunction,
  ): Promise<void> {
    try {
      const ticket = await this.ticketRepository.findById(req.params.id);
      successResponse(res, ticket, 'Ticket retrieved successfully');
    } catch (error) {
      next(error);
    }
  }

  async update(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const ticket = await this.ticketService.update(
        req.params.id,
        req.body,
        req.user,
      );
      successResponse(res, ticket, 'Ticket updated successfully');
    } catch (error) {
      next(error);
    }
  }

  async delete(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      await this.ticketService.delete(req.params.id);
      res.status(204).send();
    } catch (error) {
      next(error);
    }
  }
}
