{"parser": "@typescript-eslint/parser", "extends": ["plugin:@typescript-eslint/recommended", "prettier", "plugin:prettier/recommended"], "parserOptions": {"ecmaVersion": 2018, "sourceType": "module"}, "rules": {"@typescript-eslint/explicit-function-return-type": "off", "@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/no-unused-vars": ["error", {"argsIgnorePattern": "^_"}], "prettier/prettier": "error"}}