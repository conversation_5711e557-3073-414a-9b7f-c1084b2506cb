sonar.host.url=http://sonar.comviva.com/sonar
sonar.links.scm=http://blrgitlab.comviva.com/mbs/ng/sf/chatbot/ng-chat-service.git
sonar.projectVersion=8.0
sonar.sources=./src
sonar.cfamily.build-wrapper-output.bypass=true
sonar.projectName=MCS_CPAAS_8x_RM_Communication-Service
sonar.projectKey=my:MCS_CPAAS_8x_RM_Communication-Service
sonar.branch.name=ng-mainline-dev
sonar.exclusions=**/*.spec.ts, **/__tests__/**, **/test/**, **/mocks/**,src/db/migrations/**,src/db/seeders/**





