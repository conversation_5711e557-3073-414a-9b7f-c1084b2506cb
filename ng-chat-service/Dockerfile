# Build stage
FROM node:20-alpine AS builder

# This command is used to install some dependencies in the Docker image.
# Nessasary for running node-prune and npm install
# RUN  apk update &&  apk add --no-cache --virtual .gyp \
#     python3 \
#     make \
#     g++ \
#     bash \
#     curl

WORKDIR /app

# Copy package files
COPY package*.json ./

# Install all dependencies (so `prepare` runs)
RUN npm install

# Copy source code
COPY . .

# Build the application
RUN npm run build

# Production stage
FROM node:20-alpine

WORKDIR /app

# Copy package files
COPY package*.json ./

# Install all dependencies, run `prepare`, then prune dev deps
RUN npm install && npm run prepare && npm prune --production

# Copy built application from builder stage
COPY --from=builder /app/dist ./dist

# Set environment variables
ENV NODE_ENV=production

# Expose the port
EXPOSE 3002

# Start the application
CMD ["node", "-r", "module-alias/register", "dist/server.js"]
