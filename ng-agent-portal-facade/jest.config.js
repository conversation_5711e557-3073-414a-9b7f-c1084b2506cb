/** @type {import('ts-jest').JestConfigWithTsJest} */
module.exports = {
  preset: "ts-jest",
  testEnvironment: "node",
  moduleFileExtensions: ["ts", "js", "json", "node"],
  testMatch: ["<rootDir>/src/**/*.test.ts"],
  testPathIgnorePatterns: ["<rootDir>/dist/", "<rootDir>/node_modules/"],
  transformIgnorePatterns: [
    "node_modules/(?!(got|@sindresorhus|@szmarczak|cacheable-lookup|cacheable-request|decompress-response|get-stream|http2-wrapper|lowercase-keys|p-cancelable|responselike)/)",
  ],
  moduleNameMapper: {
    "^@/(.*)$": "<rootDir>/src/$1",
    "^@controllers/(.*)$": "<rootDir>/src/api/controllers/$1",
    "^@middlewares/(.*)$": "<rootDir>/src/api/middlewares/$1",
    "^@dtos/(.*)$": "<rootDir>/src/api/dtos/$1",
    "^@routes/(.*)$": "<rootDir>/src/api/routes/$1",
    "^@services/(.*)$": "<rootDir>/src/api/services/$1",
    "^@config/(.*)$": "<rootDir>/src/config/$1",
    "^@db/(.*)$": "<rootDir>/src/db/$1",
    "^@types/(.*)$": "<rootDir>/src/types/$1",
    "^@utils/(.*)$": "<rootDir>/src/utils/$1",
    "^@constants/(.*)$": "<rootDir>/src/constants/$1",
    "^@enums/(.*)$": "<rootDir>/src/enums/$1",
    "^got$": "<rootDir>/src/__mocks__/got.js",
  },
  transform: {
    "^.+\\.ts$": [
      "ts-jest",
      {
        tsconfig: {
          module: "commonjs",
        },
      },
    ],
  },
};
