export interface IUserWithPermissions {
  id: string;
  name: string;
  username: string;
  permissions: string[];
}

export interface DecodedTokenPayload {
  exp: number;
  iat: number;
  jti: string;
  iss: string;
  sub: string;
  typ: "Bearer";
  azp: string;
  session_state: string;
  realm_access: {
    roles: string[];
  };
  scope: string; // space-separated string of permissions
  sid: string;
  email_verified: boolean;
  groupId: string[];
  name: string;
  groups: string[];
  preferred_username: string;
  given_name: string;
  email: string;
}
