// Mock implementation of the got library for Jest tests
const mockGot = jest.fn();

// Mock the extend method
mockGot.extend = jest.fn(() => ({
  get: jest.fn(),
  post: jest.fn(),
  put: jest.fn(),
  delete: jest.fn(),
}));

// Mock HTTPError class
class HTTPError extends Error {
  constructor(response) {
    super(`Request failed with status code ${response.statusCode}`);
    this.name = 'HTTPError';
    this.response = response;
  }
}

// Export the mock
module.exports = mockGot;
module.exports.HTTPError = HTTPError;
module.exports.Got = class Got {};

// Default export for ES modules
module.exports.default = mockGot;
