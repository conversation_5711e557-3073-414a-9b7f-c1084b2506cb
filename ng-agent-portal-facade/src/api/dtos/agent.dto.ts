import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Is<PERSON><PERSON>,
  ValidatorConstraint,
  ValidatorConstraintInterface,
  ValidationArguments,
  <PERSON>idate,
  isEmail,
} from "class-validator";

/**
 * @swagger
 * components:
 *   schemas:
 *     CreateAgentDto:
 *       type: object
 *       required:
 *         - name
 *         - mobile
 *         - email
 *         - departmentId
 *       properties:
 *         name:
 *           type: string
 *           maxLength: 100
 *           description: Agent name
 *         mobile:
 *           type: string
 *           pattern: '^\+[1-9]\d{1,14}$'
 *           description: Mobile number with country code (e.g., +************)
 *         email:
 *           type: string
 *           format: email
 *           description: Unique email ID
 *         departmentId:
 *           type: string
 *           description: Agent's department name
 *       example:
 *         name: <PERSON>
 *         mobile: "+************"
 *         email: <EMAIL>
 *         departmentId: "Support"
 *
 *     UpdateAgentDto:
 *       type: object
 *       properties:
 *         name:
 *           type: string
 *           maxLength: 100
 *           description: Agent name
 *         mobile:
 *           type: string
 *           pattern: '^\+[1-9]\d{1,14}$'
 *           description: Mobile number with country code (e.g., +************)
 *         email:
 *           type: string
 *           format: email
 *           description: Unique email ID
 *         departmentId:
 *           type: string
 *           description: Agent's department name
 *       example:
 *         name: Jane Smith
 *         mobile: "+************"
 *         email: <EMAIL>
 *         departmentId: "Enquiries"
 *
 *     AgentResponseDto:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           format: uuid
 *         name:
 *           type: string
 *         email:
 *           type: string
 *         mobile:
 *           type: string
 *         departmentId:
 *           type: string
 *         availability:
 *           type: string
 *         status:
 *           type: string
 *         createdAt:
 *           type: string
 *           format: date-time
 *         updatedAt:
 *           type: string
 *           format: date-time
 *       example:
 *         id: "fcb92c1a-d4a5-4c58-b3ee-2fdadf8cb189"
 *         name: "John Doe"
 *         mobile: "+************"
 *         email: "<EMAIL>"
 *         departmentId: "Support"
 *         availability: "offline"
 *         status: "active"
 *         createdAt: "2024-06-01T12:00:00Z"
 *         updatedAt: "2024-06-01T12:00:00Z"
 */

@ValidatorConstraint({ name: "OnlyLettersAndSpaces", async: false })
class OnlyLettersAndSpaces implements ValidatorConstraintInterface {
  validate(text: string | undefined, args: ValidationArguments) {
    if (typeof text !== "string" || text.trim().length === 0) return true; // Let IsNotEmpty handle this
    return /^[A-Za-z\s]+$/.test(text);
  }

  defaultMessage(args: ValidationArguments) {
    return "Please enter a valid agent name. Only letters and spaces are allowed.";
  }
}

@ValidatorConstraint({ name: "IsValidMobile", async: false })
class IsValidMobile implements ValidatorConstraintInterface {
  validate(mobile: string | undefined, _args: ValidationArguments): boolean {
    if (typeof mobile !== "string" || mobile.trim().length === 0) return true; // Let IsNotEmpty handle it
    return /^\+[1-9]\d{1,14}$/.test(mobile); // E.164 format
  }

  defaultMessage(): string {
    return "Please enter a valid 10-digit mobile number. Digits only are allowed.";
  }
}

@ValidatorConstraint({ name: "IsValidEmail", async: false })
class IsValidEmail implements ValidatorConstraintInterface {
  validate(email: string | undefined, _args: ValidationArguments): boolean {
    if (typeof email !== "string" || email.trim().length === 0) return true; // Let IsNotEmpty handle it
    return isEmail(email);
  }

  defaultMessage(): string {
    return "Please enter a valid email address.";
  }
}

export class CreateAgentDto {
  @IsString()
  @IsNotEmpty({
    message:
      "Please enter a valid agent name. Only letters and spaces are allowed.",
  })
  @MaxLength(25, { message: "Agent name cannot exceed 25 characters." })
  @Validate(OnlyLettersAndSpaces)
  name!: string;

  @IsString()
  @IsNotEmpty({
    message:
      "Please enter a valid 10-digit mobile number. Digits only are allowed.",
  })
  @Validate(IsValidMobile)
  mobile!: string;

  @IsString()
  @IsNotEmpty({ message: "Please enter a valid email address." })
  @Validate(IsValidEmail)
  email!: string;

  @IsString()
  @IsNotEmpty({ message: "Department name is required." })
  departmentId!: string;
}

export class UpdateAgentDto {
  @IsString()
  @IsOptional()
  @IsNotEmpty({
    message:
      "Please enter a valid agent name. Only letters and spaces are allowed.",
  })
  @MaxLength(25, { message: "Agent name cannot exceed 25 characters." })
  @Validate(OnlyLettersAndSpaces)
  name?: string;

  @IsOptional()
  @IsString()
  @IsNotEmpty({
    message:
      "Please enter a valid 10-digit mobile number. Digits only are allowed.",
  })
  @Validate(IsValidMobile)
  mobile?: string;

  @IsString()
  @IsOptional()
  @IsEmail({}, { message: "Please enter a valid email address." })
  email?: string;

  @IsString()
  @IsOptional()
  departmentId?: string;
}
