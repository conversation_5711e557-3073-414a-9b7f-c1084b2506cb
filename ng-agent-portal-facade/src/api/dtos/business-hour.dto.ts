import { IsString, IsArray, IsO<PERSON>al, IsUUID, IsNotEmpty, IsIn, ValidateNested, MaxLength, Matches, ArrayNotEmpty } from 'class-validator';
import { Type } from 'class-transformer';

class SlotRangeDto {
  @IsString()
  @IsNotEmpty()
  open!: string;

  @IsString()
  @IsNotEmpty()
  close!: string;
}

class SlotDto {
  @IsString()
  @IsNotEmpty()
  day!: string;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => SlotRangeDto)
  ranges!: SlotRangeDto[];
}

export class CreateBusinessHourDto {
  @IsString()
  @IsNotEmpty({message: 'Please enter a valid business hour name.'})
  @MaxLength(32, { message: 'Name must be at most 32 characters long' })
  @Matches(/^[A-Za-z0-9._ ]+$/, {
    message: "Name can only contain letters, numbers, '.' and '_'",
  })
  name!: string;

  @IsString()
  @IsNotEmpty()
  timezone!: string;

  @IsString()
  @IsIn(['EVERYDAY', 'CUSTOM'])
  category!: string;

  @IsString()
  @IsOptional()
  @MaxLength(250, { message: 'Message must be at most 250 characters long' })
  message?: string;

  @IsArray()
  @IsUUID('all', { each: true })
  @ArrayNotEmpty({ message: 'Department should not be empty' })
  departments!: string[];

  @IsArray()
  @ArrayNotEmpty({ message: 'Slots should not be empty' })
  @ValidateNested({ each: true })
  @Type(() => SlotDto)
  slots!: SlotDto[];
}

export class UpdateBusinessHourDto {
  @IsString()
  @IsOptional()
  @IsNotEmpty({message: 'Please enter a valid business hour name.'})
  @MaxLength(32, { message: 'Name must be at most 32 characters long' })
  @Matches(/^[A-Za-z0-9._ ]+$/, {
    message: "Name can only contain letters, numbers, '.' and '_'",
  })
  name?: string;

  @IsString()
  @IsNotEmpty()
  @IsOptional()
  timezone?: string;

  @IsString()
  @IsOptional()
  @IsIn(['EVERYDAY', 'CUSTOM'])
  category?: string;

  @IsString()
  @IsOptional()
  message?: string;

  @IsArray()
  @IsUUID('all', { each: true })
  @IsOptional()
  @ArrayNotEmpty({ message: 'Department should not be empty' })
  departments?: string[];

  @IsArray()
  @IsOptional()
  @ArrayNotEmpty({ message: 'Slots should not be empty' })
  @ValidateNested({ each: true })
  @Type(() => SlotDto)
  slots?: SlotDto[];
}
