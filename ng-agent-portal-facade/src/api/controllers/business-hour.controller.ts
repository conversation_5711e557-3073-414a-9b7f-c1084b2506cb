import {NextFunction, Request, Response} from 'express';
import { extractBearerToken } from '../../utils/auth';
import {successResponse} from '@utils/response';
import BusinessHourService from '@services/business-hour.service';

class BusinessHourController {
  constructor(private readonly businessHourService: BusinessHourService) {}
  /**
   * Create a new business Hour
   */
  async create(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const token = extractBearerToken(req.headers.authorization);
      const response = await this.businessHourService.create(req.body, token);
      successResponse(res, response.data, 201);
    } catch (error) {
      console.error('Error creating business hour:', error);
      next(error);
    }
  }

  /* Get all business hour */
  async getAll(req: Request, res: Response) {
    const token = extractBearerToken(req.headers.authorization);
    const response = await this.businessHourService.getAll(token);
    successResponse(res, response.data);
  }

  /* Update business hour details */
  async update(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const token = extractBearerToken(req.headers.authorization);
      const response = await this.businessHourService.patch(
        req.params.id, 
        req.body,
        token
      );
      successResponse(res, response.data);
    } catch (error) {
      next(error);
    }
  }

  /* Delete business hour */
  async delete(req: Request, res: Response, next: NextFunction): Promise<void> {
      try {
        const token = extractBearerToken(req.headers.authorization);
        await this.businessHourService.delete(req.params.id, token);
        successResponse(res, null);
      } catch (error) {
        next(error);
      }
    }
}

export default BusinessHourController;
