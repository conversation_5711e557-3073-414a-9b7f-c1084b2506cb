import { Request, Response, NextFunction } from "express";
import agentService from "@services/agent.service";
import { successResponse } from "@utils/response";

/**
 * Controller for agent operations
 */
class AgentController {
  /**
   * Get all agents
   */
  async findAll(
    req: Request,
    res: Response,
    next: NextFunction
  ): Promise<void> {
    try {
      const token = req.headers.authorization?.split(" ")[1] ?? "";
      const response = await agentService.getAll(token, req.query as any);
      successResponse(res, response.data);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get agent by ID
   */
  async findById(
    req: Request,
    res: Response,
    next: NextFunction
  ): Promise<void> {
    try {
      const token = req.headers.authorization?.split(" ")[1] ?? "";
      const response = await agentService.getById(req.params.id, token);
      successResponse(res, response.data);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Create a new agent
   */
  async create(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const token = req.headers.authorization?.split(" ")[1] ?? "";
      const response = await agentService.create(req.body, token);
      successResponse(res, response.data, 201);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Update an agent
   */
  async update(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const token = req.headers.authorization?.split(" ")[1] ?? "";
      const response = await agentService.update(
        req.params.id,
        req.body,
        token
      );
      successResponse(res, response.data);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Delete an agent
   */
  async delete(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const token = req.headers.authorization?.split(" ")[1] ?? "";
      await agentService.delete(req.params.id, token);
      successResponse(res, null);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Activate an agent
   */
  async activate(
    req: Request,
    res: Response,
    next: NextFunction
  ): Promise<void> {
    try {
      const token = req.headers.authorization?.split(" ")[1] ?? "";
      const response = await agentService.activate(req.params.id, token);
      successResponse(res, response.data);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Deactivate an agent
   */
  async deactivate(
    req: Request,
    res: Response,
    next: NextFunction
  ): Promise<void> {
    try {
      const token = req.headers.authorization?.split(" ")[1] ?? "";
      const response = await agentService.deactivate(req.params.id, token);
      successResponse(res, response.data);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get agents by department
   */
  async getByDepartment(
    req: Request,
    res: Response,
    next: NextFunction
  ): Promise<void> {
    try {
      const token = req.headers.authorization?.split(" ")[1] ?? "";
      const response = await agentService.getByDepartment(
        req.params.departmentId,
        token
      );
      successResponse(res, response.data);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get agent availability
   */
  async getAvailability(
    req: Request,
    res: Response,
    next: NextFunction
  ): Promise<void> {
    try {
      const token = req.headers.authorization?.split(" ")[1] ?? "";
      const response = await agentService.getAvailability(req.params.id, token);
      successResponse(res, response.data);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Update agent availability
   */
  async updateAvailability(
    req: Request,
    res: Response,
    next: NextFunction
  ): Promise<void> {
    try {
      const token = req.headers.authorization?.split(" ")[1] ?? "";
      const { availability } = req.body;
      const response = await agentService.updateAvailability(
        req.params.id,
        availability,
        token
      );
      successResponse(res, response.data);
    } catch (error) {
      next(error);
    }
  }
}

export default new AgentController();
