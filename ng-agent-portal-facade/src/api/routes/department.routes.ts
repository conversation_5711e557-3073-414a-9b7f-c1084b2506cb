import { Router } from "express";
import departmentController from "@controllers/department.controller";
import { validateDto } from "@middlewares/validation.middleware";
import {
  CreateDepartmentDto,
  UpdateDepartmentDto,
} from "../dtos/department.dto";
import { authenticate } from "@middlewares/authenticate.middleware";
import { authorize } from "@middlewares/authorize.middleware";
import { PermissionKeys } from "@enums/permission-keys.enum";

const router = Router();

// Apply authentication middleware to all routes

/**
 * @swagger
 * tags:
 *   name: Departments
 *   description: Department management
 */

router.use(authenticate);

// GET /departments
router.get(
  "/",
  authorize([PermissionKeys.READ_DEPARTMENT]),
  departmentController.findAll
);

// GET /departments/:id
router.get(
  "/:id",
  authorize([PermissionKeys.READ_DEPARTMENT]),
  departmentController.findById
);

/**
 * @swagger
 * /departments:
 *   post:
 *     summary: Create a new department
 *     tags: [Departments]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CreateDepartmentDto'
 *     responses:
 *       201:
 *         description: Department created successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/DepartmentResponseDto'
 *       400:
 *         description: Validation error
 *     security:
 *       - bearerAuth: []
 */
router.post(
  "/",
  authorize([PermissionKeys.CREATE_DEPARTMENT]),
  validateDto(CreateDepartmentDto),
  departmentController.create
);

/**
 * @swagger
 * /departments/{id}:
 *   patch:
 *     summary: Update a department
 *     tags: [Departments]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Department ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/UpdateDepartmentDto'
 *     responses:
 *       200:
 *         description: Department updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/DepartmentResponseDto'
 *       404:
 *         description: Department not found
 *     security:
 *       - bearerAuth: []
 */
router.patch(
  "/:id",
  authorize([PermissionKeys.UPDATE_DEPARTMENT]),
  validateDto(UpdateDepartmentDto),
  departmentController.update
);

/**
 * @swagger
 * /departments/{id}:
 *   delete:
 *     summary: Delete a department
 *     tags: [Departments]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Department ID
 *     responses:
 *       204:
 *         description: Department deleted successfully
 *       404:
 *         description: Department not found
 *     security:
 *       - bearerAuth: []
 */
router.delete(
  "/:id",
  authorize([PermissionKeys.DELETE_DEPARTMENT]),
  departmentController.delete
);

export default router;
