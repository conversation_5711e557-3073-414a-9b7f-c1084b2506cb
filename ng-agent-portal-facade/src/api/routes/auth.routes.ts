import { AuthController } from "@controllers/auth.controller";
import { authenticate } from "@middlewares/authenticate.middleware";
import { authorize } from "@middlewares/authorize.middleware";
import { Router } from "express";

const router = Router();
router.use(authenticate);

(async () => {
  const authController = new AuthController();
  router.get("/me", authorize([]), authController.me.bind(authController));
})();

export default router;
