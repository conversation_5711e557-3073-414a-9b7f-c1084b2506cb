import { Router } from "express";
import agent<PERSON>ontroller from "@controllers/agent.controller";
import { validateDto } from "@middlewares/validation.middleware";
import { CreateAgentDto, UpdateAgentDto } from "@dtos/agent.dto";
import { authenticate } from "@middlewares/authenticate.middleware";
import { authorize } from "@middlewares/authorize.middleware";
import { PermissionKeys } from "@enums/permission-keys.enum";

const router = Router();
router.use(authenticate);
/**
 * @swagger
 * tags:
 *   name: Agents
 *   description: Agent management
 */

/**
 * @swagger
 * /agents:
 *   get:
 *     summary: Get all agents
 *     tags: [Agents]
 *     parameters:
 *       - in: query
 *         name: departmentId
 *         schema:
 *           type: string
 *         description: Filter agents by department ID
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [active, inactive]
 *         description: Filter agents by status
 *       - in: query
 *         name: availability
 *         schema:
 *           type: string
 *           enum: [online, offline, busy]
 *         description: Filter agents by availability
 *     responses:
 *       200:
 *         description: A list of agents
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/AgentResponseDto'
 *     security:
 *       - bearerAuth: []
 */
router.get(
  "/",
  authorize([PermissionKeys.READ_AGENT]),
  agentController.findAll
);

/**
 * @swagger
 * /agents/{id}:
 *   get:
 *     summary: Get agent by ID
 *     tags: [Agents]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Agent ID
 *     responses:
 *       200:
 *         description: Agent details
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/AgentResponseDto'
 *       404:
 *         description: Agent not found
 *     security:
 *       - bearerAuth: []
 */
router.get(
  "/:id",
  authorize([PermissionKeys.READ_AGENT]),
  agentController.findById
);

/**
 * @swagger
 * /agents:
 *   post:
 *     summary: Create a new agent
 *     tags: [Agents]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CreateAgentDto'
 *     responses:
 *       201:
 *         description: Agent created successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/AgentResponseDto'
 *       400:
 *         description: Validation error
 *     security:
 *       - bearerAuth: []
 */
router.post(
  "/",
  authorize([PermissionKeys.CREATE_AGENT]),
  validateDto(CreateAgentDto),
  agentController.create
);

/**
 * @swagger
 * /agents/{id}:
 *   put:
 *     summary: Update an agent
 *     tags: [Agents]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Agent ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/UpdateAgentDto'
 *     responses:
 *       200:
 *         description: Agent updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/AgentResponseDto'
 *       404:
 *         description: Agent not found
 *     security:
 *       - bearerAuth: []
 */
router.put(
  "/:id",
  authorize([PermissionKeys.UPDATE_AGENT]),
  validateDto(UpdateAgentDto),
  agentController.update
);

/**
 * @swagger
 * /agents/{id}:
 *   delete:
 *     summary: Delete an agent
 *     tags: [Agents]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Agent ID
 *     responses:
 *       204:
 *         description: Agent deleted successfully
 *       404:
 *         description: Agent not found
 *     security:
 *       - bearerAuth: []
 */
router.delete(
  "/:id",
  authorize([PermissionKeys.DELETE_AGENT]),
  agentController.delete
);

/**
 * @swagger
 * /agents/{id}/activate:
 *   put:
 *     summary: Activate an agent
 *     tags: [Agents]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Agent ID
 *     responses:
 *       200:
 *         description: Agent activated successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/AgentResponseDto'
 *       404:
 *         description: Agent not found
 *     security:
 *       - bearerAuth: []
 */
router.put(
  "/:id/activate",
  authorize([PermissionKeys.UPDATE_AGENT]),
  agentController.activate
);

/**
 * @swagger
 * /agents/{id}/deactivate:
 *   put:
 *     summary: Deactivate an agent
 *     tags: [Agents]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Agent ID
 *     responses:
 *       200:
 *         description: Agent deactivated successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/AgentResponseDto'
 *       404:
 *         description: Agent not found
 *     security:
 *       - bearerAuth: []
 */
router.put(
  "/:id/deactivate",
  authorize([PermissionKeys.UPDATE_AGENT]),
  agentController.deactivate
);

/**
 * @swagger
 * /agents/department/{departmentId}:
 *   get:
 *     summary: Get agents by department
 *     tags: [Agents]
 *     parameters:
 *       - in: path
 *         name: departmentId
 *         required: true
 *         schema:
 *           type: string
 *         description: Department ID
 *     responses:
 *       200:
 *         description: List of agents in the department
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/AgentResponseDto'
 *     security:
 *       - bearerAuth: []
 */
router.get(
  "/department/:departmentId",
  authorize([PermissionKeys.READ_DEPARTMENT]),
  agentController.getByDepartment
);

/**
 * @swagger
 * /agents/{id}/availability:
 *   get:
 *     summary: Get agent availability
 *     tags: [Agents]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Agent ID
 *     responses:
 *       200:
 *         description: Agent availability status
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 availability:
 *                   type: string
 *                   enum: [online, offline, busy]
 *       404:
 *         description: Agent not found
 *     security:
 *       - bearerAuth: []
 */
router.get(
  "/:id/availability",
  authorize([PermissionKeys.READ_AGENT]),
  agentController.getAvailability
);

/**
 * @swagger
 * /agents/{id}/availability:
 *   put:
 *     summary: Update agent availability
 *     tags: [Agents]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Agent ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               availability:
 *                 type: string
 *                 enum: [online, offline, busy]
 *             required:
 *               - availability
 *     responses:
 *       200:
 *         description: Agent availability updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/AgentResponseDto'
 *       404:
 *         description: Agent not found
 *     security:
 *       - bearerAuth: []
 */
router.put(
  "/:id/availability",
  authorize([PermissionKeys.UPDATE_AGENT]),
  agentController.updateAvailability
);

export default router;
