import { Router } from 'express';
import { validateDto } from '@middlewares/validation.middleware';
import { CreateBusinessHourDto, UpdateBusinessHourDto } from '@dtos/business-hour.dto';
import BusinessHourService from '@services/business-hour.service';
import BusinessHourController from '@controllers/business-hour.controller';
import { authenticate } from '@middlewares/authenticate.middleware';
import { authorize } from '@middlewares/authorize.middleware';
import { PermissionKeys } from '@enums/permission-keys.enum';

const router = Router();
router.use(authenticate);

const businessHourService = new BusinessHourService();
const businessHourController = new BusinessHourController(businessHourService);

/**
 * @swagger
 * /business-hour:
 *   get:
 *     summary: Get all business hours
 *     tags:
 *       - BusinessHour
 *     responses:
 *       200:
 *         description: List of business hours
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/BusinessHour'
 */
router.get('/', authorize([PermissionKeys.READ_AGENT_SETTING]), businessHourController.getAll.bind(businessHourController));

/**
 * @swagger
 * /business-hour:
 *   post:
 *     summary: Create a new business hour
 *     tags:
 *       - BusinessHour
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/BusinessHourDto'
 *     responses:
 *       201:
 *         description: Business hour created
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/BusinessHour'
 *       400:
 *         description: Validation error
 */
router.post('/', authorize([PermissionKeys.CREATE_AGENT_SETTING]), validateDto(CreateBusinessHourDto), businessHourController.create.bind(businessHourController));

/**
 * @swagger
 * /business-hour/{id}:
 *   patch:
 *     summary: Update a business hour
 *     tags:
 *       - BusinessHour
 *     parameters:
 *       - in: path
 *         name: id
 *         schema:
 *           type: string
 *         required: true
 *         description: Business hour ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/BusinessHourDto'
 *     responses:
 *       200:
 *         description: Business hour updated
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/BusinessHour'
 *       404:
 *         description: Not found
 */
router.patch('/:id',  authorize([PermissionKeys.UPDATE_AGENT_SETTING]), validateDto(UpdateBusinessHourDto), businessHourController.update.bind(businessHourController));

/**
 * @swagger
 * /business-hour/{id}:
 *   delete:
 *     summary: Delete a business hour
 *     tags:
 *       - BusinessHour
 *     parameters:
 *       - in: path
 *         name: id
 *         schema:
 *           type: string
 *         required: true
 *         description: Business hour ID
 *     responses:
 *       204:
 *         description: Deleted successfully
 *       404:
 *         description: Not found
 */
router.delete('/:id', authorize([PermissionKeys.DELETE_AGENT_SETTING]), businessHourController.delete.bind(businessHourController));

export default router;
