import { Request, Response, NextFunction } from "express";
import { UnauthorizedError, ForbiddenError } from "@utils/errors";

export const authorize = (requiredPermissions: string[]) => {
  return (req: Request, res: Response, next: NextFunction) => {
    try {
      if (!req.user) {
        throw new UnauthorizedError("Unauthorized");
      }

      const userPermissions = req.user.permissions || [];

      if (!requiredPermissions?.length) {
        return next(); // No specific permission required, allow any authenticated user
      }

      if (requiredPermissions.includes("")) {
        throw new ForbiddenError(
          "Invalid permission configuration: empty string detected."
        );
      }

      const hasAllPermissions = !requiredPermissions.some(
        (permission) => !userPermissions.includes(permission)
      );

      if (!hasAllPermissions) {
        throw new ForbiddenError("Forbidden");
      }

      next();
    } catch (error) {
      next(error);
    }
  };
};
