import { Request, Response, NextFunction } from "express";
import jwt from "jsonwebtoken";
import logger from "@utils/logger";
import { UnauthorizedError } from "@utils/errors";
import {
  DecodedTokenPayload,
  IUserWithPermissions,
} from "../../types/user-with-permissions.type";

// Extend Express Request interface
declare global {
  namespace Express {
    interface Request {
      user?: IUserWithPermissions;
      token?: string;
    }
  }
}

export const authenticate = (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const authHeader = req.headers.authorization;

    if (!authHeader?.startsWith("Bearer ")) {
      throw new UnauthorizedError("Missing or invalid Authorization header");
    }

    const token = authHeader.split(" ")[1];
    req.token = token;

    const decoded = jwt.decode(token) as DecodedTokenPayload;

    const currentTime = Math.floor(Date.now() / 1000); // Current time in seconds
    if (decoded.exp && decoded.exp < currentTime) {
      throw new UnauthorizedError("Token has expired");
    }

    if (!decoded) {
      throw new UnauthorizedError("Invalid token");
    }

    const user: IUserWithPermissions = {
      id: decoded.sub,
      name: decoded.name,
      username: decoded.preferred_username,
      permissions: decoded.scope?.split(" ") ?? [],
    };

    req.user = user;

    next();
  } catch (error: any) {
    logger.error("Authentication failed:", error.message);
    next(error);
  }
};
