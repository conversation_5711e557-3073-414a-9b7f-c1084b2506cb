import { Request, Response, NextFunction } from "express";
import { HTTPError } from "got";
import { AppError } from "@utils/errors";
import logger from "@utils/logger";
import { errorResponse } from "@utils/response";

export const errorHandler = (
  err: Error,
  req: Request,
  res: Response,
  _next: NextFunction
): void => {
  let statusCode = 500;
  let message = "Internal Server Error";
  let errors: Record<string, string[] | string> | undefined;

  // Handle known AppError
  if (err instanceof AppError) {
    statusCode = err.statusCode;
    message = err.message;
    if ("errors" in err && err.errors) {
      errors = err.errors as Record<string, string[]>;
    } else {
      errors = { message: err.message };
    }
  }

  // Handle Got HTTP errors
  else if (err instanceof HTTPError) {
    const gotError = err;
    statusCode = gotError.response.statusCode;
    message = gotError.message;

    logger.error(`[${req.id ?? "UNKNOWN"}] HTTP Error: ${message}`, {
      path: `${req.method} ${req.path}`,
      url: gotError.request?.requestUrl?.toString(),
      statusCode,
      error: gotError.stack,
      responseBody: gotError.response.body,
    });

    res.status(statusCode).json({
      ...(typeof gotError.response.body === "object" &&
      gotError.response.body !== null
        ? (gotError.response.body as Record<string, string[]>)
        : {}),
    });
    return;
  }

  // Log other errors
  logger.error(`[${req.id ?? "UNKNOWN"}] Error: ${message}`, {
    path: `${req.method} ${req.path}`,
    error: err.stack,
    statusCode,
  });
  // Send standard error response
  errorResponse(res, statusCode, errors);
};
