import got, { Got } from "got";
import logger from "@utils/logger";
import { ServiceUnavailableError } from "@utils/errors";

// Type definitions for BaseService
export type QueryParams = Record<
  string,
  string | number | boolean | string[] | number[] | boolean[] | undefined
>;

export interface ServiceResponse<T = unknown> {
  data: T;
  status: number;
  statusText: string | undefined;
  headers: Record<string, string | string[] | undefined>;
}

export interface RequestOptions {
  headers?: Record<string, string>;
  json?: unknown;
  timeout?: number | { request?: number; response?: number };
  retry?: { limit?: number; methods?: string[] };
  [key: string]: unknown;
}

/**
 * Base service for REST API connections
 * Acts as a generic connector to microservices
 */
export class BaseService {
  protected client: Got;
  protected endpoint: string;

  /**
   * Create a new BaseService
   * @param baseURL - Base URL for the service
   * @param endpoint - API endpoint for this resource
   */
  constructor(baseURL: string, endpoint: string) {
    this.client = got.extend({
      prefixUrl: baseURL,
      timeout: {
        request: 10000,
      },
      responseType: "json",
      retry: {
        limit: 2,
        methods: ["GET", "PUT", "HEAD", "DELETE", "OPTIONS", "TRACE"],
      },
    });
    this.endpoint = endpoint;
  }

  /**
   * Get all resources
   * @param token - JWT token for authentication
   * @param queryParams - Query parameters for filtering
   */
  async getAll<T = unknown>(
    token: string,
    queryParams?: QueryParams
  ): Promise<ServiceResponse<T>> {
    try {
      const response = await this.client.get(this.endpoint, {
        headers: this.getAuthHeader(token),
        ...(queryParams && {
          searchParams: new URLSearchParams(
            queryParams as Record<string, string>
          ),
        }),
      });
      return {
        data: response.body as T,
        status: response.statusCode,
        statusText: response.statusMessage,
        headers: response.headers,
      };
    } catch (error) {
      logger.error(`Error fetching ${this.endpoint}:`, error);
      throw new ServiceUnavailableError(
        `Unable to fetch ${this.endpoint} from service`
      );
    }
  }

  /**
   * Get resource by ID
   * @param id - Resource ID
   * @param token - JWT token for authentication
   */
  async getById<T = unknown>(
    id: string,
    token: string
  ): Promise<ServiceResponse<T>> {
    try {
      const response = await this.client.get(`${this.endpoint}/${id}`, {
        headers: this.getAuthHeader(token),
      });
      return {
        data: response.body as T,
        status: response.statusCode,
        statusText: response.statusMessage,
        headers: response.headers,
      };
    } catch (error) {
      logger.error(`Error fetching ${this.endpoint}/${id}:`, error);
      throw error;
    }
  }

  /**
   * Create a new resource
   * @param data - Resource data
   * @param token - JWT token for authentication
   */
  async create<TRequest = unknown, TResponse = unknown>(
    data: TRequest,
    token: string
  ): Promise<ServiceResponse<TResponse>> {
    try {
      const response = await this.client.post(this.endpoint, {
        json: data,
        headers: this.getAuthHeader(token),
      });
      return {
        data: response.body as TResponse,
        status: response.statusCode,
        statusText: response.statusMessage,
        headers: response.headers,
      };
    } catch (error) {
      logger.error(`Error creating ${this.endpoint}:`, error);
      throw error;
    }
  }

  /**
   * Update a resource
   * @param id - Resource ID
   * @param data - Resource data to update
   * @param token - JWT token for authentication
   */
  async update<TRequest = unknown, TResponse = unknown>(
    id: string,
    data: TRequest,
    token: string
  ): Promise<ServiceResponse<TResponse>> {
    try {
      const response = await this.client.patch(`${this.endpoint}/${id}`, {
        json: data,
        headers: this.getAuthHeader(token),
      });
      return {
        data: response.body as TResponse,
        status: response.statusCode,
        statusText: response.statusMessage,
        headers: response.headers,
      };
    } catch (error) {
      logger.error(`Error updating ${this.endpoint}/${id}:`, error);
      throw error;
    }
  }

  /**
   * Partially update a resource (PATCH)
   * @param id - Resource ID
   * @param data - Partial resource data to update
   * @param token - JWT token for authentication
   */
  async patch<TRequest = unknown, TResponse = unknown>(
    id: string,
    data: TRequest,
    token: string
  ): Promise<ServiceResponse<TResponse>> {
    try {
      const response = await this.client.patch(`${this.endpoint}/${id}`, {
        json: data,
        headers: this.getAuthHeader(token),
      });
      return {
        data: response.body as TResponse,
        status: response.statusCode,
        statusText: response.statusMessage,
        headers: response.headers,
      };
    } catch (error) {
      logger.error(`Error patching ${this.endpoint}/${id}:`, error);
      throw error;
    }
  }

  /**
   * Delete a resource
   * @param id - Resource ID
   * @param token - JWT token for authentication
   */
  async delete<T = unknown>(
    id: string,
    token: string
  ): Promise<ServiceResponse<T>> {
    try {
      const response = await this.client.delete(`${this.endpoint}/${id}`, {
        headers: this.getAuthHeader(token),
      });
      return {
        data: response.body as T,
        status: response.statusCode,
        statusText: response.statusMessage,
        headers: response.headers,
      };
    } catch (error) {
      logger.error(`Error deleting ${this.endpoint}/${id}:`, error);
      throw error;
    }
  }

  /**
   * Custom request to the service
   * @param method - HTTP method
   * @param url - Request URL
   * @param token - JWT token for authentication
   * @param options - Request options
   */
  async request<T = unknown>(
    method: string,
    url: string,
    token: string,
    options: RequestOptions = {}
  ): Promise<ServiceResponse<T>> {
    try {
      const requestOptions: any = {
        method: method.toUpperCase(),
        headers: {
          ...options.headers,
          ...this.getAuthHeader(token),
        },
      };

      if (options.json) {
        requestOptions.json = options.json;
      }

      if (options.timeout) {
        requestOptions.timeout = options.timeout;
      }

      const response = await this.client(url, requestOptions);
      return {
        data: response.body as T,
        status: response.statusCode,
        statusText: response.statusMessage,
        headers: response.headers,
      };
    } catch (error) {
      logger.error(`Error in custom request to ${url}:`, error);
      throw error;
    }
  }

  /**
   * Get authorization header with token
   * @param token - JWT token
   * @returns Authorization header object
   */
  protected getAuthHeader(token: string): Record<string, string> {
    return {
      Authorization: `Bearer ${token}`,
    };
  }
}
