import { BaseService } from "./base.service";
import config from "@config/index";

/**
 * Service for Business Hour operations
 * Extends BaseService to handle business hour specific operations
 */
class BusinessHourService extends BaseService {
  /**
   * Create a new BusinessHourService
   */
  constructor() {
    super(config.services.agentPortal.baseUrl, "business-hour");
  }
}

export default BusinessHourService;
