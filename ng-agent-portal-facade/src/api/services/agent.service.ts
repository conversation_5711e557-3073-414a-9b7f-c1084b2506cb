import { BaseService } from "./base.service";
import config from "@config/index";

/**
 * Service for agent operations
 * Extends BaseService to handle agent-specific operations
 */
class AgentService extends BaseService {
  /**
   * Create a new AgentService
   */
  constructor() {
    super(config.services.agentPortal.baseUrl, "agents");
  }

  /**
   * Activate an agent
   * @param id - Agent ID
   * @param token - JWT token for authentication
   */
  async activate<T = unknown>(id: string, token: string) {
    return this.request<T>("PUT", `${this.endpoint}/${id}/activate`, token);
  }

  /**
   * Deactivate an agent
   * @param id - Agent ID
   * @param token - JWT token for authentication
   */
  async deactivate<T = unknown>(id: string, token: string) {
    return this.request<T>("PUT", `${this.endpoint}/${id}/deactivate`, token);
  }

  /**
   * Get agents by department
   * @param departmentId - Department ID
   * @param token - JWT token for authentication
   */
  async getByDepartment<T = unknown>(departmentId: string, token: string) {
    return this.getAll<T>(token, { departmentId });
  }

  /**
   * Get agent availability status
   * @param id - Agent ID
   * @param token - JWT token for authentication
   */
  async getAvailability<T = unknown>(id: string, token: string) {
    return this.request<T>("GET", `${this.endpoint}/${id}/availability`, token);
  }

  /**
   * Update agent availability
   * @param id - Agent ID
   * @param availability - New availability status
   * @param token - JWT token for authentication
   */
  async updateAvailability<T = unknown>(
    id: string,
    availability: string,
    token: string
  ) {
    return this.request<T>(
      "PUT",
      `${this.endpoint}/${id}/availability`,
      token,
      {
        json: { availability },
      }
    );
  }
}

export default new AgentService();
