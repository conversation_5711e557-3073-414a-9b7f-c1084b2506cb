import { CreateAgentDto, UpdateAgentDto } from "../api/dtos/agent.dto";
import { validate } from "class-validator";

describe("Agent DTOs", () => {
  describe("CreateAgentDto", () => {
    it("should pass validation with valid data", async () => {
      const dto = new CreateAgentDto();
      dto.name = "<PERSON>";
      dto.email = "<EMAIL>";
      dto.mobile = "+918078054465";
      dto.departmentId = "Support";

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it("should fail validation with invalid name", async () => {
      const dto = new CreateAgentDto();
      dto.name = "John123"; // Invalid: contains numbers
      dto.email = "<EMAIL>";
      dto.mobile = "+918078054465";
      dto.departmentId = "Support";

      const errors = await validate(dto);
      expect(errors).toHaveLength(1);
      expect(errors[0].property).toBe("name");
    });

    it("should fail validation with invalid email", async () => {
      const dto = new CreateAgentDto();
      dto.name = "John <PERSON>e";
      dto.email = "invalid-email"; // Invalid email format
      dto.mobile = "+918078054465";
      dto.departmentId = "Support";

      const errors = await validate(dto);
      expect(errors).toHaveLength(1);
      expect(errors[0].property).toBe("email");
    });

    it("should fail validation with invalid mobile", async () => {
      const dto = new CreateAgentDto();
      dto.name = "John Doe";
      dto.email = "<EMAIL>";
      dto.mobile = "123"; // Invalid: missing country code and wrong format
      dto.departmentId = "Support";

      const errors = await validate(dto);
      expect(errors).toHaveLength(1);
      expect(errors[0].property).toBe("mobile");
    });

    it("should fail validation with missing required fields", async () => {
      const dto = new CreateAgentDto();
      // Missing all required fields

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);

      const errorProperties = errors.map((error) => error.property);
      expect(errorProperties).toContain("name");
      expect(errorProperties).toContain("email");
      expect(errorProperties).toContain("mobile");
      expect(errorProperties).toContain("departmentId");
    });

    it("should fail validation with name too long", async () => {
      const dto = new CreateAgentDto();
      dto.name = "A".repeat(101); // Exceeds 100 character limit
      dto.email = "<EMAIL>";
      dto.mobile = "+918078054465";
      dto.departmentId = "Support";

      const errors = await validate(dto);
      expect(errors).toHaveLength(1);
      expect(errors[0].property).toBe("name");
    });
  });

  describe("UpdateAgentDto", () => {
    it("should pass validation with valid partial data", async () => {
      const dto = new UpdateAgentDto();
      dto.name = "Jane Smith";
      dto.email = "<EMAIL>";

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it("should pass validation with empty object", async () => {
      const dto = new UpdateAgentDto();
      // All fields are optional

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it("should fail validation with invalid name format", async () => {
      const dto = new UpdateAgentDto();
      dto.name = "Jane123"; // Invalid: contains numbers

      const errors = await validate(dto);
      expect(errors).toHaveLength(1);
      expect(errors[0].property).toBe("name");
    });

    it("should fail validation with invalid email format", async () => {
      const dto = new UpdateAgentDto();
      dto.email = "invalid-email";

      const errors = await validate(dto);
      expect(errors).toHaveLength(1);
      expect(errors[0].property).toBe("email");
    });

    it("should fail validation with invalid mobile format", async () => {
      const dto = new UpdateAgentDto();
      dto.mobile = "abc1234567"; // Invalid: contains letters and missing country code

      const errors = await validate(dto);
      expect(errors).toHaveLength(1);
      expect(errors[0].property).toBe("mobile");
    });

    it("should fail validation with name too long", async () => {
      const dto = new UpdateAgentDto();
      dto.name = "A".repeat(101); // Exceeds 100 character limit

      const errors = await validate(dto);
      expect(errors).toHaveLength(1);
      expect(errors[0].property).toBe("name");
    });
  });
});
