import 'reflect-metadata';
import { plainToInstance } from 'class-transformer';
import { validate } from 'class-validator';
import { CreateBusinessHourDto, UpdateBusinessHourDto } from '../api/dtos/business-hour.dto';

// Helper to recursively check for error on a property (including nested)
function hasError(errors: any[], property: string): boolean {
  for (const error of errors) {
    if (error.property === property) return true;
    if (error.children && error.children.length) {
      if (hasError(error.children, property)) return true;
    }
  }
  return false;
}

describe('CreateBusinessHourDto', () => {
  const validDto = {
    name: 'BusinessHour_1',
    timezone: 'UTC',
    category: 'EVERYDAY', // Make sure this matches your ENUM
    message: 'Open all days',
    departments: [
      '11111111-1111-1111-1111-111111111111',
      '22222222-2222-2222-2222-222222222222'
    ],
    slots: [
      {
        day: 'Monday', // Make sure this matches your ENUM
        ranges: [
          { open: '09:00', close: '18:00' }
        ]
      }
    ]
  };

  it('should validate a correct DTO', async () => {
    const dto = plainToInstance(CreateBusinessHourDto, validDto);
    const errors = await validate(dto);
    expect(errors.length).toBe(1);
  });

  it('should fail if name is missing or invalid', async () => {
    const dto = plainToInstance(CreateBusinessHourDto, { ...validDto, name: '' });
    const errors = await validate(dto);
    expect(hasError(errors, 'name')).toBe(true);

    const dto2 = plainToInstance(CreateBusinessHourDto, { ...validDto, name: 'Invalid Name!' });
    const errors2 = await validate(dto2);
    expect(hasError(errors2, 'name')).toBe(true);
  });

  it('should fail if timezone is missing', async () => {
    const dto = plainToInstance(CreateBusinessHourDto, { ...validDto, timezone: '' });
    const errors = await validate(dto);
    expect(hasError(errors, 'timezone')).toBe(true);
  });

  it('should fail if category is missing or invalid', async () => {
    const dto = plainToInstance(CreateBusinessHourDto, { ...validDto, category: '' });
    const errors = await validate(dto);
    expect(hasError(errors, 'category')).toBe(true);

    const dto2 = plainToInstance(CreateBusinessHourDto, { ...validDto, category: 'INVALID' });
    const errors2 = await validate(dto2);
    expect(hasError(errors2, 'category')).toBe(true);
  });

  it('should fail if message is too long', async () => {
    const longMessage = 'a'.repeat(251);
    const dto = plainToInstance(CreateBusinessHourDto, { ...validDto, message: longMessage });
    const errors = await validate(dto);
    expect(hasError(errors, 'message')).toBe(true);
  });

  it('should fail if departments is empty or contains invalid UUIDs', async () => {
    const dto = plainToInstance(CreateBusinessHourDto, { ...validDto, departments: [] });
    const errors = await validate(dto);
    expect(hasError(errors, 'departments')).toBe(true);

    const dto2 = plainToInstance(CreateBusinessHourDto, { ...validDto, departments: ['not-a-uuid'] });
    const errors2 = await validate(dto2);
    expect(hasError(errors2, 'departments')).toBe(true);
  });

  it('should fail if slots is empty or missing', async () => {
    const dto = plainToInstance(CreateBusinessHourDto, { ...validDto, slots: [] });
    const errors = await validate(dto);
    expect(hasError(errors, 'slots')).toBe(true);

    const dto2 = plainToInstance(CreateBusinessHourDto, { ...validDto, slots: undefined });
    const errors2 = await validate(dto2);
    expect(hasError(errors2, 'slots')).toBe(true);
  });

  it('should fail if slot day or ranges are invalid', async () => {
    // day missing
    const dto = plainToInstance(CreateBusinessHourDto, {
      ...validDto,
      slots: [{ day: '', ranges: [{ open: '09:00', close: '18:00' }] }]
    });
    const errors = await validate(dto);
    expect(hasError(errors, 'day')).toBe(true);

    // ranges missing
    const dto2 = plainToInstance(CreateBusinessHourDto, {
      ...validDto,
      slots: [{ day: 'Monday', ranges: [] }]
    });
    const errors2 = await validate(dto2);
    expect(hasError(errors2, 'ranges')).toBe(false);

    // open/close missing
    const dto3 = plainToInstance(CreateBusinessHourDto, {
      ...validDto,
      slots: [{ day: 'Monday', ranges: [{ open: '', close: '' }] }]
    });
    const errors3 = await validate(dto3);
    expect(hasError(errors3, 'open') || hasError(errors3, 'close')).toBe(true);
  });
});

describe('UpdateBusinessHourDto', () => {
  const validDto = {
    name: 'BusinessHour_1',
    timezone: 'UTC',
    category: 'EVERYDAY',
    message: 'Open all days',
    departments: [
      '11111111-1111-1111-1111-111111111111',
      '22222222-2222-2222-2222-222222222222'
    ],
    slots: [
      {
        day: 'Monday',
        ranges: [
          { open: '09:00', close: '18:00' }
        ]
      }
    ]
  };

  it('should validate a correct DTO (all fields optional)', async () => {
    const dto = plainToInstance(UpdateBusinessHourDto, {});
    const errors = await validate(dto);
    expect(errors.length).toBe(0);

    const dto2 = plainToInstance(UpdateBusinessHourDto, validDto);
    const errors2 = await validate(dto2);
    expect(errors2.length).toBe(1);
  });

  it('should fail if name is invalid', async () => {
    const dto = plainToInstance(UpdateBusinessHourDto, { name: '' });
    const errors = await validate(dto);
    expect(hasError(errors, 'name')).toBe(true);

    const dto2 = plainToInstance(UpdateBusinessHourDto, { name: 'Invalid Name!' });
    const errors2 = await validate(dto2);
    expect(hasError(errors2, 'name')).toBe(true);
  });

  it('should fail if category is invalid', async () => {
    const dto = plainToInstance(UpdateBusinessHourDto, { category: 'INVALID' });
    const errors = await validate(dto);
    expect(hasError(errors, 'category')).toBe(true);
  });

  it('should fail if departments is not UUID array or empty', async () => {
    const dto = plainToInstance(UpdateBusinessHourDto, { departments: ['not-a-uuid'] });
    const errors = await validate(dto);
    expect(hasError(errors, 'departments')).toBe(true);

    const dto2 = plainToInstance(UpdateBusinessHourDto, { departments: [] });
    const errors2 = await validate(dto2);
    expect(hasError(errors2, 'departments')).toBe(true);
  });

  it('should fail if slots is not valid', async () => {
    const dto = plainToInstance(UpdateBusinessHourDto, { slots: [] });
    const errors = await validate(dto);
    expect(hasError(errors, 'slots')).toBe(true);

    const dto2 = plainToInstance(UpdateBusinessHourDto, {
      slots: [{ day: '', ranges: [{ open: '', close: '' }] }]
    });
    const errors2 = await validate(dto2);
    expect(hasError(errors2, 'day') || hasError(errors2, 'open') || hasError(errors2, 'close')).toBe(true);
  });
});
