import 'reflect-metadata';
import { successResponse } from '@utils/response';
import BusinessHourController from '../api/controllers/business-hour.controller';
import BusinessHourService from '../api/services/business-hour.service';

jest.mock('../api/services/business-hour.service');
jest.mock('@utils/response');

const mockReq = (body = {}, params = {}, headers = {}) => ({
  body,
  params,
  headers,
});
const mockRes = () => {
  const res: any = {};
  res.status = jest.fn().mockReturnValue(res);
  res.json = jest.fn().mockReturnValue(res);
  return res;
};
const mockNext = jest.fn();

describe('BusinessHourController', () => {
  let businessHourService: jest.Mocked<BusinessHourService>;
  let businessHourController: BusinessHourController;

  beforeEach(() => {
    jest.clearAllMocks();
    // Create a new mocked service instance for each test
    businessHourService = new (BusinessHourService as jest.Mock<BusinessHourService>)() as jest.Mocked<BusinessHourService>;
    businessHourController = new BusinessHourController(businessHourService);
  });

  describe('create', () => {
    it('should create a business hour and return 201', async () => {
      const req = mockReq(
        { name: 'Default' },
        {},
        { authorization: 'Bearer testtoken' }
      );
      const res = mockRes();
      const data = { id: '1', name: 'Default' };
      businessHourService.create.mockResolvedValue({ status: 200,
        statusText: 'OK',
        headers: {},
        data: { id: '1', name: 'Default' },
      });
      (successResponse as jest.Mock).mockImplementation((res, data, status = 200) => {
        res.status(status).json(data);
      });

      await businessHourController.create(req as any, res as any, mockNext);

      expect(businessHourService.create).toHaveBeenCalledWith(req.body, 'testtoken');
      expect(successResponse).toHaveBeenCalledWith(res, data, 201);
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle errors and call next', async () => {
      const req = mockReq({}, {}, { authorization: 'Bearer testtoken' });
      const res = mockRes();
      const error = new Error('Create error');
      businessHourService.create.mockRejectedValue(error);

      const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});

      await businessHourController.create(req as any, res as any, mockNext);

      expect(mockNext).toHaveBeenCalledWith(error);
      expect(consoleSpy).toHaveBeenCalled();
      consoleSpy.mockRestore();
    });
  });

  describe('getAll', () => {
    it('should get all business hours and return 200', async () => {
      const req = mockReq({}, {}, { authorization: 'Bearer testtoken' });
      const res = mockRes();
      const data = [{ id: '1' }];
      businessHourService.getAll.mockResolvedValue({ 
        status: 200,
        statusText: 'OK',
        headers: {},
        data: [{ id: '1', name: 'Default' }],
      });
      (successResponse as jest.Mock).mockImplementation((res, data, status = 200) => {
        res.status(status).json(data);
      });

      await businessHourController.getAll(req as any, res as any);

      expect(businessHourService.getAll).toHaveBeenCalledWith('testtoken');
      // expect(successResponse).toHaveBeenCalledWith(res, data, 200);
    });
  });

  describe('update', () => {
    it('should update a business hour and return 200', async () => {
      const req = mockReq(
        { name: 'Updated' },
        { id: '1' },
        { authorization: 'Bearer testtoken' }
      );
      const res = mockRes();
      const data = { id: '1', name: 'Updated' };
      businessHourService.patch.mockResolvedValue({ 
        status: 200,
        statusText: 'OK',
        headers: {},
        data: { id: '1', name: 'Updated' },
       });
      (successResponse as jest.Mock).mockImplementation((res, data, status = 200) => {
        res.status(status).json(data);
      });

      await businessHourController.update(req as any, res as any, mockNext);

      expect(businessHourService.patch).toHaveBeenCalledWith('1', req.body, 'testtoken');
      expect(successResponse).toHaveBeenCalledWith(res, data);
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle errors and call next', async () => {
      const req = mockReq({}, { id: '1' }, { authorization: 'Bearer testtoken' });
      const res = mockRes();
      const error = new Error('Update error');
      businessHourService.patch.mockRejectedValue(error);

      await businessHourController.update(req as any, res as any, mockNext);

      expect(mockNext).toHaveBeenCalledWith(error);
    });
  });

  describe('delete', () => {
    it('should delete a business hour and return 200', async () => {
      const req = mockReq({}, { id: '1' }, { authorization: 'Bearer testtoken' });
      const res = mockRes();
      businessHourService.delete.mockResolvedValue({
        status: 200,
        statusText: 'OK',
        headers: {},
        data: null,
      });
      (successResponse as jest.Mock).mockImplementation((res, data, status = 200) => {
        res.status(status).json(data);
      });

      await businessHourController.delete(req as any, res as any, mockNext);

      expect(businessHourService.delete).toHaveBeenCalledWith('1', 'testtoken');
      expect(successResponse).toHaveBeenCalledWith(res, null);
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle errors and call next', async () => {
      const req = mockReq({}, { id: '1' }, { authorization: 'Bearer testtoken' });
      const res = mockRes();
      const error = new Error('Delete error');
      businessHourService.delete.mockRejectedValue(error);

      await businessHourController.delete(req as any, res as any, mockNext);

      expect(mockNext).toHaveBeenCalledWith(error);
    });
  });
});
