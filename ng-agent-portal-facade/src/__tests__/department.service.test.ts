import departmentService from "@services/department.service";
import { v4 as uuidv4 } from "uuid";

// Mock the client in the department service
jest.mock("got");

describe("DepartmentService", () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Mock the client methods
    (departmentService as any).client = {
      get: jest.fn(),
      post: jest.fn(),
      put: jest.fn(),
      patch: jest.fn(),
      delete: jest.fn(),
    };

    // Ensure all methods return a proper response structure by default
    (departmentService as any).client.get.mockResolvedValue({
      body: {},
      statusCode: 200,
      statusMessage: "OK",
      headers: {},
    });
    (departmentService as any).client.post.mockResolvedValue({
      body: {},
      statusCode: 201,
      statusMessage: "Created",
      headers: {},
    });
    (departmentService as any).client.put.mockResolvedValue({
      body: {},
      statusCode: 200,
      statusMessage: "OK",
      headers: {},
    });
    (departmentService as any).client.patch.mockResolvedValue({
      body: {},
      statusCode: 200,
      statusMessage: "OK",
      headers: {},
    });
    (departmentService as any).client.delete.mockResolvedValue({
      body: {},
      statusCode: 204,
      statusMessage: "No Content",
      headers: {},
    });
  });

  describe("create", () => {
    it("should create a department successfully", async () => {
      // Mock data
      const departmentData = { name: "Test Department", isDefault: false };
      const createdDepartment = { ...departmentData, id: uuidv4() };
      const token = "test-token";

      // Mock client response
      (departmentService as any).client.post.mockResolvedValue({
        body: createdDepartment,
        statusCode: 201,
        statusMessage: "Created",
        headers: {},
      });

      // Call the service method
      const result = await departmentService.create(departmentData, token);

      // Assertions
      expect((departmentService as any).client.post).toHaveBeenCalledWith(
        "departments",
        {
          json: departmentData,
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      console.log(result);
      expect(result.data).toEqual(createdDepartment);
    });
  });

  describe("findAll", () => {
    it("should return all departments", async () => {
      // Mock data
      const departments = [
        { id: uuidv4(), name: "Department 1", isDefault: true },
        { id: uuidv4(), name: "Department 2", isDefault: false },
      ];
      const options = { limit: 10, offset: 0 };
      const token = "test-token";

      // Mock client response
      (departmentService as any).client.get.mockResolvedValue({
        body: departments,
        statusCode: 200,
        statusMessage: "OK",
        headers: {},
      });

      // Call the service method
      const result = await departmentService.getAll(token, options);

      // Assertions
      expect((departmentService as any).client.get).toHaveBeenCalledWith(
        "departments",
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
          searchParams: new URLSearchParams(options as any),
        }
      );
      expect(result.data).toEqual(departments);
    });
  });

  describe("findById", () => {
    it("should return a department by ID", async () => {
      // Mock data
      const departmentId = uuidv4();
      const department = {
        id: departmentId,
        name: "Test Department",
        isDefault: false,
      };
      const token = "test-token";

      // Mock client response
      (departmentService as any).client.get.mockResolvedValue({
        body: department,
        statusCode: 200,
        statusMessage: "OK",
        headers: {},
      });

      // Call the service method
      const result = await departmentService.getById(departmentId, token);

      // Assertions
      expect((departmentService as any).client.get).toHaveBeenCalledWith(
        `departments/${departmentId}`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      expect(result.data).toEqual(department);
    });
  });

  describe("update", () => {
    it("should update a department successfully", async () => {
      // Mock data
      const departmentId = uuidv4();
      const updateData = { name: "Updated Department" };
      const updatedDepartment = {
        id: departmentId,
        name: "Updated Department",
        isDefault: false,
      };
      const token = "test-token";

      // Mock client response
      (departmentService as any).client.patch.mockResolvedValue({
        body: updatedDepartment,
        statusCode: 200,
        statusMessage: "OK",
        headers: {},
      });

      // Call the service method
      const result = await departmentService.update(
        departmentId,
        updateData,
        token
      );

      // Assertions
      expect((departmentService as any).client.patch).toHaveBeenCalledWith(
        `departments/${departmentId}`,
        {
          json: updateData,
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      expect(result.data).toEqual(updatedDepartment);
    });
  });

  describe("delete", () => {
    it("should delete a department successfully", async () => {
      // Mock data
      const departmentId = uuidv4();
      const token = "test-token";

      // Mock client response
      (departmentService as any).client.delete.mockResolvedValue({
        body: null,
        statusCode: 204,
        statusMessage: "No Content",
        headers: {},
      });

      // Call the service method
      await departmentService.delete(departmentId, token);

      // Assertions
      expect((departmentService as any).client.delete).toHaveBeenCalledWith(
        `departments/${departmentId}`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
    });
  });
});
