import agentService from "@services/agent.service";
import { v4 as uuidv4 } from "uuid";

// Mock the got client
jest.mock("got", () => ({
  extend: jest.fn(() => ({
    get: jest.fn(),
    post: jest.fn(),
    put: jest.fn(),
    delete: jest.fn(),
  })),
}));

describe("AgentService", () => {
  const mockToken = "test-token";

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe("getAll", () => {
    it("should return all agents", async () => {
      // Mock data
      const agents = [
        {
          id: uuidv4(),
          name: "Agent 1",
          email: "<EMAIL>",
          mobile: "+************",
          departmentId: "Support",
          status: "active",
          availability: "online",
        },
        {
          id: uuidv4(),
          name: "Agent 2",
          email: "<EMAIL>",
          mobile: "+************",
          departmentId: "Sales",
          status: "active",
          availability: "offline",
        },
      ];
      const queryParams = { departmentId: "dept-1" };

      // Mock client response
      (agentService as any).client.get.mockResolvedValue({
        body: agents,
        statusCode: 200,
        statusMessage: "OK",
        headers: {},
      });

      // Call the service method
      const result = await agentService.getAll(mockToken, queryParams);

      // Assertions
      expect((agentService as any).client.get).toHaveBeenCalledWith("agents", {
        headers: {
          Authorization: `Bearer ${mockToken}`,
        },
        searchParams: new URLSearchParams(
          queryParams as Record<string, string>
        ),
      });
      expect(result.data).toEqual(agents);
      expect(result.status).toBe(200);
    });
  });

  describe("getById", () => {
    it("should return an agent by ID", async () => {
      // Mock data
      const agentId = uuidv4();
      const agent = {
        id: agentId,
        name: "Test Agent",
        email: "<EMAIL>",
        mobile: "+************",
        departmentId: "Support",
        status: "active",
        availability: "online",
      };

      // Mock client response
      (agentService as any).client.get.mockResolvedValue({
        body: agent,
        statusCode: 200,
        statusMessage: "OK",
        headers: {},
      });

      // Call the service method
      const result = await agentService.getById(agentId, mockToken);

      // Assertions
      expect((agentService as any).client.get).toHaveBeenCalledWith(
        `agents/${agentId}`,
        {
          headers: {
            Authorization: `Bearer ${mockToken}`,
          },
        }
      );
      expect(result.data).toEqual(agent);
    });
  });

  describe("create", () => {
    it("should create a new agent", async () => {
      // Mock data
      const agentData = {
        name: "New Agent",
        email: "<EMAIL>",
        mobile: "+************",
        departmentId: "Support",
      };
      const createdAgent = {
        id: uuidv4(),
        ...agentData,
        status: "active",
        availability: "offline",
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      // Mock client response
      (agentService as any).client.post.mockResolvedValue({
        body: createdAgent,
        statusCode: 201,
        statusMessage: "Created",
        headers: {},
      });

      // Call the service method
      const result = await agentService.create(agentData, mockToken);

      // Assertions
      expect((agentService as any).client.post).toHaveBeenCalledWith("agents", {
        json: agentData,
        headers: {
          Authorization: `Bearer ${mockToken}`,
        },
      });
      expect(result.data).toEqual(createdAgent);
      expect(result.status).toBe(201);
    });
  });
});
