import { Request, Response } from "express";
import { AxiosError } from "axios";
import { errorHandler } from "@middlewares/error.middleware";
import { AppError } from "@utils/errors";
import logger from "@utils/logger";
import { errorResponse } from "@utils/response";

// Mock dependencies
jest.mock("@utils/logger", () => ({
  error: jest.fn(),
}));

jest.mock("@utils/response", () => ({
  errorResponse: jest.fn(),
}));

describe("Error Middleware", () => {
  let mockRequest: Partial<Request>;
  let mockResponse: Partial<Response>;
  let nextFunction: jest.Mock;

  beforeEach(() => {
    mockRequest = {
      id: "test-id",
      method: "GET",
      path: "/test",
    };
    mockResponse = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn(),
    };
    nextFunction = jest.fn();
    jest.clearAllMocks();
  });

  it("should handle AppError correctly", () => {
    const appError = new AppError("Bad Request", 400);

    errorHandler(
      appError,
      mockRequest as Request,
      mockResponse as Response,
      nextFunction
    );

    expect(logger.error).toHaveBeenCalled();
    expect(errorResponse).toHaveBeenCalled();
  });

  it("should handle generic errors correctly", () => {
    const genericError = new Error("Something went wrong");

    errorHandler(
      genericError,
      mockRequest as Request,
      mockResponse as Response,
      nextFunction
    );

    expect(logger.error).toHaveBeenCalled();
    expect(errorResponse).toHaveBeenCalledWith(mockResponse, 500, undefined);
  });
});
