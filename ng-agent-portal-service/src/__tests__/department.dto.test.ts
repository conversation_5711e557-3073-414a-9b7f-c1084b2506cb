import {validate} from 'class-validator';
import {
  CreateDepartmentDto,
  UpdateDepartmentDto,
} from '../api/dtos/department.dto';

describe('Department DTOs', () => {
  describe('CreateDepartmentDto', () => {
    it('should validate a valid DTO', async () => {
      const dto = new CreateDepartmentDto();
      dto.name = 'Test Department';

      const errors = await validate(dto);
      expect(errors.length).toBe(0);
    });

    it('should fail validation when name is missing', async () => {
      const dto = new CreateDepartmentDto();
      // name is missing

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);
      expect(errors[0].property).toBe('name');
    });

    it('should fail validation when name is empty', async () => {
      const dto = new CreateDepartmentDto();
      dto.name = '';

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);
      expect(errors[0].property).toBe('name');
    });

    it('should fail validation when name is too long', async () => {
      const dto = new CreateDepartmentDto();
      dto.name = 'a'.repeat(101); // Assuming max length is 100

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);
      expect(errors[0].property).toBe('name');
    });

    it('should validate when isDefault is missing (should use default value)', async () => {
      const dto = new CreateDepartmentDto();
      dto.name = 'Test Department';
      // isDefault is missing, should use default value

      const errors = await validate(dto);
      expect(errors.length).toBe(0);
    });
  });

  describe('UpdateDepartmentDto', () => {
    it('should validate a valid DTO with name', async () => {
      const dto = new UpdateDepartmentDto();
      dto.name = 'Updated Department';

      const errors = await validate(dto);
      expect(errors.length).toBe(0);
    });

    it('should validate a valid DTO with isDefault', async () => {
      const dto = new UpdateDepartmentDto();

      const errors = await validate(dto);
      expect(errors.length).toBe(0);
    });

    it('should validate a valid DTO with both properties', async () => {
      const dto = new UpdateDepartmentDto();
      dto.name = 'Updated Department';

      const errors = await validate(dto);
      expect(errors.length).toBe(0);
    });

    it('should fail validation when name is empty', async () => {
      const dto = new UpdateDepartmentDto();
      dto.name = '';

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);
      expect(errors[0].property).toBe('name');
    });

    it('should fail validation when name is too long', async () => {
      const dto = new UpdateDepartmentDto();
      dto.name = 'a'.repeat(101); // Assuming max length is 100

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);
      expect(errors[0].property).toBe('name');
    });

    it('should validate an empty DTO (partial update)', async () => {
      const dto = new UpdateDepartmentDto();
      // Both properties are missing, which is valid for a partial update

      const errors = await validate(dto);
      expect(errors.length).toBe(0);
    });
  });
});
