import {Request, Response} from 'express';
import agentController from '@controllers/agent.controller';
import agentService from '@services/agent.service';
import {HttpError} from '@utils/errors';
import {v4 as uuidv4} from 'uuid';

// Mock the agent service
jest.mock('@services/agent.service');

describe('AgentController', () => {
  let mockRequest: Partial<Request>;
  let mockResponse: Partial<Response>;
  let nextFunction: jest.Mock;

  beforeEach(() => {
    mockRequest = {};
    mockResponse = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn(),
      send: jest.fn(),
    };
    nextFunction = jest.fn();
    jest.clearAllMocks();
  });

  describe('create', () => {
    it('should create an agent and return 201 status', async () => {
      // Mock data
      const agentData = {name: 'Test Agent', email: '<EMAIL>'};
      const createdAgent = {
        id: uuidv4(),
        ...agentData,
      };

      // Setup request
      mockRequest.body = agentData;

      // Mock service response
      (agentService.create as jest.Mock).mockResolvedValue(createdAgent);

      // Call controller
      await agentController.create(
        mockRequest as Request,
        mockResponse as Response,
        nextFunction,
      );

      // Assertions
      expect(agentService.create).toHaveBeenCalledWith(agentData);
      expect(mockResponse.status).toHaveBeenCalledWith(201);
      expect(mockResponse.json).toHaveBeenCalledWith(expect.objectContaining({
        status: 'success',
        data: createdAgent,
        message: 'Agent created successfully'
      }));
      expect(nextFunction).not.toHaveBeenCalled();
    });

    it('should call next with error if service throws', async () => {
      // Mock data
      const agentData = {name: 'Test Agent', email: '<EMAIL>'};
      const error = new Error('Service error');

      // Setup request
      mockRequest.body = agentData;

      // Mock service to throw error
      (agentService.create as jest.Mock).mockRejectedValue(error);

      // Call controller
      await agentController.create(
        mockRequest as Request,
        mockResponse as Response,
        nextFunction,
      );

      // Assertions
      expect(agentService.create).toHaveBeenCalledWith(agentData);
      expect(mockResponse.status).not.toHaveBeenCalled();
      expect(mockResponse.json).not.toHaveBeenCalled();
      expect(nextFunction).toHaveBeenCalledWith(error);
    });
  });

  describe('findAll', () => {
    it('should return all agents with 200 status', async () => {
      // Mock data
      const agents = [
        {id: uuidv4(), name: 'Agent 1', email: '<EMAIL>'},
        {id: uuidv4(), name: 'Agent 2', email: '<EMAIL>'},
      ];
      const total = agents.length;
      const options = {limit: 10, offset: 0};

      // Setup request
      mockRequest.filterOptions = options;

      // Mock service response
      (agentService.findAll as jest.Mock).mockResolvedValue(agents);
      (agentService.count as jest.Mock).mockResolvedValue(total);

      // Call controller
      await agentController.findAll(
        mockRequest as Request,
        mockResponse as Response,
        nextFunction,
      );

      // Assertions
      expect(agentService.findAll).toHaveBeenCalledWith(options);
      expect(agentService.count).toHaveBeenCalledWith(options);
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith(expect.objectContaining({
        status: 'success',
        data: agents,
        message: 'Agents retrieved successfully',
        meta: {
          total,
          limit: options.limit,
          offset: options.offset,
        }
      }));
      expect(nextFunction).not.toHaveBeenCalled();
    });

    it('should call next with error if service throws', async () => {
      // Mock service to throw error
      const error = new Error('Service error');
      (agentService.findAll as jest.Mock).mockRejectedValue(error);

      // Setup request
      mockRequest.filterOptions = {};

      // Call controller
      await agentController.findAll(
        mockRequest as Request,
        mockResponse as Response,
        nextFunction,
      );

      // Assertions
      expect(agentService.findAll).toHaveBeenCalledWith({});
      expect(mockResponse.status).not.toHaveBeenCalled();
      expect(mockResponse.json).not.toHaveBeenCalled();
      expect(nextFunction).toHaveBeenCalledWith(error);
    });
  });

  describe('findById', () => {
    it('should return an agent by ID with 200 status', async () => {
      // Mock data
      const agentId = uuidv4();
      const agent = {
        id: agentId,
        name: 'Test Agent',
        email: '<EMAIL>',
      };

      // Setup request
      mockRequest.params = {id: agentId};

      // Mock service response
      (agentService.findById as jest.Mock).mockResolvedValue(agent);

      // Call controller
      await agentController.findById(
        mockRequest as Request,
        mockResponse as Response,
        nextFunction,
      );

      // Assertions
      expect(agentService.findById).toHaveBeenCalledWith(agentId);
      expect(mockResponse.json).toHaveBeenCalledWith(expect.objectContaining({
        status: 'success',
        data: agent,
        message: 'Agent retrieved successfully'
      }));
      expect(nextFunction).not.toHaveBeenCalled();
    });

    it('should call next with error if service throws', async () => {
      // Mock data
      const agentId = uuidv4();
      const error = new HttpError(404, 'Agent not found');

      // Setup request
      mockRequest.params = {id: agentId};

      // Mock service to throw error
      (agentService.findById as jest.Mock).mockRejectedValue(error);

      // Call controller
      await agentController.findById(
        mockRequest as Request,
        mockResponse as Response,
        nextFunction,
      );

      // Assertions
      expect(agentService.findById).toHaveBeenCalledWith(agentId);
      expect(mockResponse.status).not.toHaveBeenCalled();
      expect(mockResponse.json).not.toHaveBeenCalled();
      expect(nextFunction).toHaveBeenCalledWith(error);
    });
  });

  describe('update', () => {
    it('should update an agent and return 200 status', async () => {
      // Mock data
      const agentId = uuidv4();
      const updateData = {name: 'Updated Agent'};
      const updatedAgent = {
        id: agentId,
        name: 'Updated Agent',
        email: '<EMAIL>',
      };

      // Setup request
      mockRequest.params = {id: agentId};
      mockRequest.body = updateData;

      // Mock service response
      (agentService.update as jest.Mock).mockResolvedValue(updatedAgent);

      // Call controller
      await agentController.update(
        mockRequest as Request,
        mockResponse as Response,
        nextFunction,
      );

      // Assertions
      expect(agentService.update).toHaveBeenCalledWith(agentId, updateData);
      expect(mockResponse.json).toHaveBeenCalledWith(expect.objectContaining({
        status: 'success',
        data: updatedAgent,
        message: 'Agent updated successfully'
      }));
      expect(nextFunction).not.toHaveBeenCalled();
    });

    it('should call next with error if service throws', async () => {
      // Mock data
      const agentId = uuidv4();
      const updateData = {name: 'Updated Agent'};
      const error = new HttpError(404, 'Agent not found');

      // Setup request
      mockRequest.params = {id: agentId};
      mockRequest.body = updateData;

      // Mock service to throw error
      (agentService.update as jest.Mock).mockRejectedValue(error);

      // Call controller
      await agentController.update(
        mockRequest as Request,
        mockResponse as Response,
        nextFunction,
      );

      // Assertions
      expect(agentService.update).toHaveBeenCalledWith(agentId, updateData);
      expect(mockResponse.status).not.toHaveBeenCalled();
      expect(mockResponse.json).not.toHaveBeenCalled();
      expect(nextFunction).toHaveBeenCalledWith(error);
    });
  });

  describe('delete', () => {
    it('should delete an agent and return 204 status', async () => {
      // Mock data
      const agentId = uuidv4();

      // Setup request
      mockRequest.params = {id: agentId};

      // Mock service response
      (agentService.delete as jest.Mock).mockResolvedValue(undefined);

      // Call controller
      await agentController.delete(
        mockRequest as Request,
        mockResponse as Response,
        nextFunction,
      );

      // Assertions
      expect(agentService.delete).toHaveBeenCalledWith(agentId);
      expect(mockResponse.status).toHaveBeenCalledWith(204);
      expect(mockResponse.send).toHaveBeenCalled();
      expect(nextFunction).not.toHaveBeenCalled();
    });

    it('should call next with error if service throws', async () => {
      // Mock data
      const agentId = uuidv4();
      const error = new HttpError(404, 'Agent not found');

      // Setup request
      mockRequest.params = {id: agentId};

      // Mock service to throw error
      (agentService.delete as jest.Mock).mockRejectedValue(error);

      // Call controller
      await agentController.delete(
        mockRequest as Request,
        mockResponse as Response,
        nextFunction,
      );

      // Assertions
      expect(agentService.delete).toHaveBeenCalledWith(agentId);
      expect(mockResponse.status).not.toHaveBeenCalled();
      expect(mockResponse.send).not.toHaveBeenCalled();
      expect(nextFunction).toHaveBeenCalledWith(error);
    });
  });
});