import dotenv from 'dotenv';

// Load environment variables from .env file
dotenv.config();

interface Config {
  env: string;
  port: number;
  apiPrefix: string;
  host: string;
  db: {
    host: string;
    port: number;
    name: string;
    user: string;
    password: string;
  };
  jwt: {
    secret: string;
    expiresIn: string;
  };
  logging: {
    level: string;
  };
  rateLimit: {
    windowMs: number;
    max: number;
  };
}

const config: Config = {
  env: process.env.NODE_ENV ?? 'development',
  port: parseInt(process.env.PORT ?? '3001', 10),
  host: process.env.HOST ?? 'http://localhost',
  apiPrefix: process.env.API_PREFIX ?? '/api/v1',
  db: {
    host: process.env.DB_HOST ?? 'localhost',
    port: parseInt(process.env.DB_PORT ?? '3306', 10),
    name: process.env.DB_NAME ?? 'ng_bot_builder_db',
    user: process.env.DB_USER ?? 'root',
    password: process.env.DB_PASSWORD ?? '',
  },
  jwt: {
    secret: process.env.JWT_SECRET ?? 'your_jwt_secret',
    expiresIn: process.env.JWT_EXPIRES_IN ?? '1d',
  },
  logging: {
    level: process.env.LOG_LEVEL ?? 'info',
  },
  rateLimit: {
    windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS ?? '900000', 10), // 15 minutes
    max: parseInt(process.env.RATE_LIMIT_MAX ?? '100', 10), // limit each IP to 100 requests per windowMs
  },
};

export default config;
