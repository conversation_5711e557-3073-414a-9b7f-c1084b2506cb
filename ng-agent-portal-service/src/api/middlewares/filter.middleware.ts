import {Request, Response, NextFunction} from 'express';
import {Model, ModelStatic, FindOptions, Op} from 'sequelize';
import {BadRequestError} from '@utils/errors';
import logger from '@utils/logger';

/**
 * Configuration for filter validation
 */
export interface FilterValidationConfig<T extends Model> {
  allowedAttributes?: (keyof T)[];
  maxLimit?: number;
  allowedOperators?: symbol[];
}

/**
 * Middleware to validate and apply Sequelize filters
 * @param model - Sequelize model
 * @param config - Filter validation configuration
 * @returns Express middleware
 */
export function applyFilters<T extends Model>(
  model: ModelStatic<T>,
  config: Partial<FilterValidationConfig<T>> = {},
) {
  return (req: Request, _res: Response, next: NextFunction): void => {
    try {
      // Get model attributes for allowed filters
      const modelAttributes = Object.keys(model.getAttributes()) as (keyof T)[];

      // Default configuration
      const {
        allowedAttributes = modelAttributes,
        maxLimit = 100,
        allowedOperators = [
          Op.eq,
          Op.ne,
          Op.gt,
          Op.gte,
          Op.lt,
          Op.lte,
          Op.in,
          Op.notIn,
          Op.like,
          Op.startsWith,
          Op.endsWith,
        ],
      } = config;

      // Parse filter from query
      let filterOptions: FindOptions = {};
      console.log('req.query', req.query.filter);
      if (req.query.filter) {
        try {
          filterOptions = JSON.parse(req.query.filter as string);
        } catch (error) {
          logger.error('Error parsing filter:', error);
          throw new BadRequestError(
            'Invalid filter format. Must be valid JSON.',
          );
        }
      }

      // Validate limit
      if (filterOptions.limit) {
        const limit = Number(filterOptions.limit);
        if (isNaN(limit) || limit <= 0) {
          throw new BadRequestError('Limit must be a positive number');
        }
        filterOptions.limit = Math.min(limit, maxLimit);
      }

      // Validate offset
      if (filterOptions.offset) {
        const offset = Number(filterOptions.offset);
        if (isNaN(offset) || offset < 0) {
          throw new BadRequestError('Offset must be a non-negative number');
        }
      }

      // Validate attributes
      if (filterOptions.attributes) {
        if (Array.isArray(filterOptions.attributes)) {
          // Validate array of attributes
          filterOptions.attributes = filterOptions.attributes.filter(
            attr =>
              typeof attr === 'string' &&
              allowedAttributes.includes(attr as any),
          );
        } else if (typeof filterOptions.attributes === 'object') {
          // Validate include/exclude object
          if (filterOptions.attributes.include) {
            filterOptions.attributes.include =
              filterOptions.attributes.include.filter(
                attr =>
                  typeof attr === 'string' &&
                  allowedAttributes.includes(attr as any),
              );
          }
          if (filterOptions.attributes.exclude) {
            filterOptions.attributes.exclude =
              filterOptions.attributes.exclude.filter(
                attr =>
                  typeof attr === 'string' &&
                  allowedAttributes.includes(attr as any),
              );
          }
        }
      }

      // Convert string operators to Sequelize operators and validate where clause
      if (filterOptions.where) {
        filterOptions.where = convertOperators(filterOptions.where);
        validateWhereClause(
          filterOptions.where,
          allowedAttributes,
          allowedOperators,
        );
      }

      // Validate order
      if (filterOptions.order) {
        if (Array.isArray(filterOptions.order)) {
          filterOptions.order = filterOptions.order.filter(orderItem => {
            if (Array.isArray(orderItem) && orderItem.length >= 2) {
              const field = orderItem[0];
              const direction = orderItem[1];
              return (
                typeof field === 'string' &&
                allowedAttributes.includes(field as any) &&
                ['ASC', 'DESC', 'asc', 'desc'].includes(String(direction))
              );
            }
            return false;
          });
        } else {
          // If order is not an array, remove it
          delete filterOptions.order;
        }
      }

      // Remove any potentially unsafe properties
      const safeProperties: (keyof FindOptions)[] = [
        'where',
        'attributes',
        'order',
        'limit',
        'offset',
        'paranoid',
      ];

      Object.keys(filterOptions).forEach(key => {
        if (!safeProperties.includes(key as keyof FindOptions)) {
          delete filterOptions[key as keyof FindOptions];
        }
      });

      // Attach the validated options to the request object
      req.filterOptions = filterOptions;

      next();
    } catch (error) {
      next(error);
    }
  };
}

/**
 * Convert string operator representations to actual Sequelize operators
 * @param obj - Object to convert
 * @returns Converted object
 */
function convertOperators(obj: any): any {
  if (!obj || typeof obj !== 'object') {
    return obj;
  }

  if (Array.isArray(obj)) {
    return obj.map(item => convertOperators(item));
  }

  const result: any = {};

  Object.keys(obj).forEach(key => {
    let newKey = key;
    let value = obj[key];

    // Convert string operator keys to actual Sequelize operators
    if (key.startsWith('[Op.') && key.endsWith(']')) {
      const opName = key.substring(4, key.length - 1);
      if (Op[opName as keyof typeof Op]) {
        newKey = Op[opName as keyof typeof Op] as any;
      }
    }

    // Recursively convert nested objects
    if (value && typeof value === 'object') {
      value = convertOperators(value);
    }

    result[newKey] = value;
  });

  return result;
}

/**
 * Recursively validate where clause
 * @param whereClause - Where clause to validate
 * @param allowedAttributes - Allowed attributes
 * @param allowedOperators - Allowed operators
 */
function validateWhereClause(
  whereClause: any,
  allowedAttributes: any[],
  allowedOperators: symbol[],
): void {
  if (!whereClause || typeof whereClause !== 'object') {
    throw new BadRequestError('Where clause must be an object');
  }

  // Check each property in the where clause
  Object.keys(whereClause).forEach(key => {
    const value = whereClause[key];
    const keyStr = String(key);

    // Handle Sequelize operators
    if (typeof key === 'symbol' || keyStr.includes('Op.')) {
      // Check if operator is allowed
      const isAllowedOp = allowedOperators.some(
        op => op.toString() === key || String(op) === keyStr,
      );

      if (!isAllowedOp) {
        throw new BadRequestError(`Operator ${keyStr} is not allowed`);
      }

      // Recursively validate nested conditions for logical operators
      if (
        key === Op.and.toString() ||
        key === Op.or.toString() ||
        keyStr.includes('Op.and') ||
        keyStr.includes('Op.or')
      ) {
        if (Array.isArray(value)) {
          value.forEach(condition =>
            validateWhereClause(condition, allowedAttributes, allowedOperators),
          );
        }
      }
    }
    // Handle regular field conditions
    else if (!allowedAttributes.includes(key)) {
      throw new BadRequestError(
        `Filtering on attribute '${key}' is not allowed`,
      );
    }
    // Handle nested operators on fields
    else if (
      typeof value === 'object' &&
      value !== null &&
      !Array.isArray(value)
    ) {
      Object.keys(value).forEach(opKey => {
        const opKeyStr = String(opKey);

        // Check if operator is allowed
        const isAllowedOp = allowedOperators.some(
          op => op.toString() === opKey || String(op) === opKeyStr,
        );

        if (!isAllowedOp && allowedOperators.length) {
          throw new BadRequestError(`Operator ${opKeyStr} is not allowed`);
        }
      });
    }
  });
}

// Extend Express Request interface to include filterOptions
declare global {
  // eslint-disable-next-line @typescript-eslint/no-namespace
  namespace Express {
    interface Request {
      filterOptions?: FindOptions;
    }
  }
}
