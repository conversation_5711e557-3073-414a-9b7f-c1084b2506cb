import {Request, Response, NextFunction} from 'express';
import agentService from '@services/agent.service';
import {successResponse} from '@utils/response';

/**
 * Controller for agent operations
 */
class AgentController {
  /**
   * Create a new agent
   */
  async create(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const agent = await agentService.create(req.body);
      successResponse(res, agent, 'Agent created successfully', 201);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get all agents
   */
  async findAll(
    req: Request,
    res: Response,
    next: NextFunction,
  ): Promise<void> {
    try {
      const options = req.filterOptions || {};
      const [agents, total] = await Promise.all([
        agentService.findAll(options),
        agentService.count(options),
      ]);
      successResponse(res, agents, 'Agents retrieved successfully', 200, {
        total,
        limit: options.limit,
        offset: options.offset || 0,
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get agent by ID
   */
  async findById(
    req: Request,
    res: Response,
    next: NextFunction,
  ): Promise<void> {
    try {
      const agent = await agentService.findById(req.params.id);
      successResponse(res, agent, 'Agent retrieved successfully');
    } catch (error) {
      next(error);
    }
  }

  /**
   * Update agent
   */
  async update(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const agent = await agentService.update(req.params.id, req.body);
      successResponse(res, agent, 'Agent updated successfully');
    } catch (error) {
      next(error);
    }
  }

  /**
   * Delete department
   */
  async delete(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      await agentService.delete(req.params.id);
      res.status(204).send();
    } catch (error) {
      next(error);
    }
  }
}

export default new AgentController();
