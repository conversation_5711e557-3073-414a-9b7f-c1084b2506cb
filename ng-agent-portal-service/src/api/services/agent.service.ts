import Agent from '@db/models/agent.model';
import {UpdateAgentDto} from '@dtos/agent.dto';
import {BadRequestError, ConflictError, NotFoundError} from '@utils/errors';
import {Transaction, FindOptions} from 'sequelize';
import agentRepository from '@db/repositories/agent.repository';
import departmentRepository from '@db/repositories/department.repository';

class AgentService {
  /**
   * Create a new agent
   * @param data - Agent data
   * @param transaction - Optional transaction
   * @returns Promise with created agent
   */
  async create(
    data: any,
    transaction?: Transaction,
  ): Promise<Agent> {
    // Check if a agent with the same email/mobile already exists
    // const existingAgent = //keycloak api call
    // if (existingAgent) {
    //   throw new ConflictError('Agent with this email already exists');
    // }
    const { name, email, mobile, departmentName } = data;
    const keycloakData = { name, email, mobile }
    // call keycloak api to create user and extract id from response
    const department = await departmentRepository.findOne({where: {name: departmentName},
      transaction,})
      // add necessary fields to keycloak here and rest to our db
    const departmentId = department ? department.id : null;  
    const dbData = {
      kcId : 'keycloak-id', // replace with actual keycloak id
      departmentId,
      availability: 'offline', // default value
      isActive: false, // default value 
    }
    return agentRepository.create(dbData, {transaction});
  }

  /**
   * Get all agents with filtering
   * @param options - Find options
   * @returns Promise with agent array
   */
  async findAll(options?: FindOptions): Promise<Agent[]> {
    return agentRepository.findAll(options);
  }

  /**
   * Get agent by ID
   * @param id - Agent ID
   * @param options - Find options
   * @returns Promise with agent
   */
  async findById(id: string, options?: FindOptions): Promise<Agent> {
      const agent = await agentRepository.findById(id, options);
      if (!agent) {
        throw new NotFoundError('Agent not found');
      }
      return agent;
  }

  /**
   * Update Agent
   * @param id - Agent ID
   * @param data - Update data
   * @param transaction - Optional transaction
   * @returns Promise with updated agent
   */
  async update(
    id: string,
    data: any,
    transaction?: Transaction,
  ): Promise<Agent> {

      const agent = await agentRepository.findById(id, {transaction});
      if (!agent) {
        throw new NotFoundError('Agent not found');
      }

      // Check if email is being updated and if it already exists
      // if (data.email && data.email !== agent.email) {
      //   const existingDepartment = await agentRepository.findOne({
      //     where: {name: data.name},
      //     paranoid: false, // Check even soft-deleted records
      //     transaction,
      //   });

      //   if (existingDepartment) {
      //     throw new ConflictError('Agent with this email already exists');
      //   }
      // }

      await agentRepository.update(id, data, {transaction});
      return agent;
    
  }

  /**
   * Delete agent
   * @param id - Agent ID
   * @param transaction - Optional transaction
   * @returns Promise with void
   */
  async delete(id: string, transaction?: Transaction): Promise<void> {
      const agent = await agentRepository.findById(id, {transaction});
      if (!agent) {
        throw new NotFoundError('Agent not found');
      }
      await agentRepository.delete(id, {transaction});
      // call keycloak api to delete user
  }

  /**
     * Count agents with filtering
     * @param options - Find options
     * @returns Promise with count
     */
    async count(options?: FindOptions): Promise<number> {
      return agentRepository.count(options);
    }

}

export default new AgentService();
