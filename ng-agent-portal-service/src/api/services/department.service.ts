import {Transaction, FindOptions} from 'sequelize';
import Department from '@db/models/department.model';
import {UpdateDepartmentDto} from '@dtos/department.dto';
import departmentRepository from '@db/repositories/department.repository';
import {BadRequestError, ConflictError, NotFoundError} from '@utils/errors';

/**
 * Service for department operations
 */
class DepartmentService {
  /**
   * Create a new department
   * @param data - Department data
   * @param transaction - Optional transaction
   * @returns Promise with created department
   */
  async create(data: any, transaction?: Transaction): Promise<Department> {
    // Check if a department with the same name already exists
    const existingDepartment = await departmentRepository.findOne({
      where: {name: data.name},
      paranoid: false, // Check even soft-deleted records
      transaction,
    });

    if (existingDepartment) {
      throw new ConflictError('Department with this name already exists');
    }
    return departmentRepository.create(data, {transaction});
  }

  /**
   * Get all departments with filtering
   * @param options - Find options
   * @returns Promise with departments array
   */
  async findAll(options?: FindOptions): Promise<Department[]> {
    return departmentRepository.findAll(options);
  }

  /**
   * Get department by ID
   * @param id - Department ID
   * @param options - Find options
   * @returns Promise with department
   */
  async findById(id: string, options?: FindOptions): Promise<Department> {
    const department = await departmentRepository.findById(id, options);

    if (!department) {
      throw new NotFoundError('Department not found');
    }

    return department;
  }

  /**
   * Update department
   * @param id - Department ID
   * @param data - Update data
   * @param transaction - Optional transaction
   * @returns Promise with updated department
   */
  async update(
    id: string,
    data: UpdateDepartmentDto,
    transaction?: Transaction,
  ): Promise<Department> {
    const department = await departmentRepository.findById(id, {transaction});

    if (!department) {
      throw new NotFoundError('Department not found');
    }

    // Check if name is being updated and if it already exists
    if (data.name && data.name !== department.name) {
      const existingDepartment = await departmentRepository.findOne({
        where: {name: data.name},
        paranoid: false, // Check even soft-deleted records
        transaction,
      });

      if (existingDepartment) {
        throw new ConflictError('Department with this name already exists');
      }
    }
    return departmentRepository.update(id, data, {transaction});
  }

  /**
   * Delete department
   * @param id - Department ID
   * @param transaction - Optional transaction
   * @returns Promise with void
   */
  async delete(id: string, transaction?: Transaction): Promise<void> {
    const department = await this.findById(id, {transaction});

    if (!department) {
      throw new NotFoundError('Department not found');
    }

    if (department.isDefault) {
      throw new BadRequestError('Cannot delete the default department');
    }
    return departmentRepository.delete(id, {transaction});
  }

  /**
   * Get default department
   * @returns Promise with default department or null
   */
  async getDefaultDepartment(): Promise<Department | null> {
    return departmentRepository.getDefaultDepartment();
  }

  /**
   * Count departments with filtering
   * @param options - Find options
   * @returns Promise with count
   */
  async count(options?: FindOptions): Promise<number> {
    return departmentRepository.count(options);
  }
}

export default new DepartmentService();
