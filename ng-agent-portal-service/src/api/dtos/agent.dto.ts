import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>E<PERSON>,
} from 'class-validator';

/**
 * @swagger
 * components:
 *   schemas:
 *     CreateAgentDto:
 *       type: object
 *       required:
 *         - name
 *         - mobileNumber
 *         - email
 *         - departmentName
 *       properties:
 *         name:
 *           type: string
 *           maxLength: 100
 *           description: Agent name
 *         mobileNumber:
 *           type: string
 *           pattern: '^[0-9]{10}$'
 *           description: 10-digit mobile number (no country code)
 *         email:
 *           type: string
 *           format: email
 *           description: Unique email ID
 *         departmentName:
 *           type: string
 *           description: Agent's department name
 *       example:
 *         name: <PERSON>
 *         mobileNumber: "9876543210"
 *         email: <EMAIL>
 *         departmentName: "Support"
 * 
 *     UpdateAgentDto:
 *       type: object
 *       properties:
 *         name:
 *           type: string
 *           maxLength: 100
 *           description: Agent name
 *         mobileNumber:
 *           type: string
 *           pattern: '^[0-9]{10}$'
 *           description: 10-digit mobile number (no country code)
 *         email:
 *           type: string
 *           format: email
 *           description: Unique email ID
 *         departmentName:
 *           type: string
 *           description: Agent's department name
 *       example:
 *         name: Acme Ivy
 *         mobileNumber: "9926543210"
 *         email: <EMAIL>
 *         departmentName: "Enquiries"
 * 
 *     AgentResponseDto:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           format: uuid
 *         name:
 *           type: string
 *         email:
 *           type: string
 *         mobileNumber:
 *           type: string
 *         departmentName:
 *           type: string
 *         createdAt:
 *           type: string
 *           format: date-time
 *         updatedAt:
 *           type: string
 *           format: date-time
 *       example:
 *         id: "fcb92c1a-d4a5-4c58-b3ee-2fdadf8cb189"
 *         name: "John Doe"
 *         mobileNumber: "9876543210"
 *         email: "<EMAIL>"
 *         availability: "offline"
 *         isActive: true
 *         createdAt: "2024-06-01T12:00:00Z"
 *         updatedAt: "2024-06-01T12:00:00Z"
 */

export class CreateAgentDto {
  @IsString()
  @IsNotEmpty({ message: 'Please enter a valid agent name.' })
  @MaxLength(100, { message: 'Agent name cannot exceed 100 characters.' })
  @Matches(/^[A-Za-z\s]+$/, {
    message: 'Please enter a valid agent name.',
  })
  name!: string;

  @IsString()
  @IsNotEmpty({ message: 'Please enter a valid 10-digit mobile number.' })
  @Matches(/^\d{10}$/, {
    message: 'Please enter a valid 10-digit mobile number.',
  })
  mobileNumber!: string;

  @IsString()
  @IsNotEmpty({ message: 'Please enter a valid email address.' })
  @IsEmail({}, { message: 'Please enter a valid email address.' })
  email!: string;

  @IsString()
  departmentName?: string;
}

export class UpdateAgentDto {
  @IsOptional()
  @IsString()
  @IsNotEmpty({ message: 'Please enter a valid agent name.' })
  @MaxLength(100, { message: 'Agent name cannot exceed 100 characters.' })
  @Matches(/^[A-Za-z\s]+$/, {
    message: 'Please enter a valid agent name.',
  })
  name?: string;

  @IsOptional()
  @IsString()
  @Matches(/^\d{10}$/, {
    message: 'Please enter a valid 10-digit mobile number.',
  })
  mobileNumber?: string;

  @IsOptional()
  @IsString()
  @IsEmail({}, { message: 'Please enter a valid email address.' })
  email?: string;

  @IsOptional()
  @IsString()
  departmentName?: string;
}
