import {
  Model,
  ModelStatic,
  FindOptions,
  CreateOptions,
  UpdateOptions,
  DestroyOptions,
  WhereOptions,
  BulkCreateOptions,
  InstanceUpdateOptions,
} from 'sequelize';
import {HttpError} from '@utils/errors';

/**
 * Generic base repository for CRUD operations
 * @template T - Sequelize Model type
 */
export class BaseRepository<T extends Model> {
  protected model: ModelStatic<T>;

  constructor(model: ModelStatic<T>) {
    this.model = model;
  }

  async findAll(options?: FindOptions): Promise<T[]> {
    console.log({options});
    return this.model.findAll(options);
  }

  async findById(
    id: string | number,
    options?: FindOptions,
  ): Promise<T | null> {
    return this.model.findByPk(id, options);
  }

  async findOne(options?: FindOptions): Promise<T | null> {
    return this.model.findOne(options);
  }

  async count(options?: FindOptions): Promise<number> {
    const countOptions = {...options};
    delete countOptions.limit;
    delete countOptions.offset;
    return this.model.count(countOptions);
  }

  async create(
    data: T['_creationAttributes'],
    options?: CreateOptions,
  ): Promise<T> {
    return this.model.create(data, options);
  }

  async bulkCreate(
    data: T['_creationAttributes'][],
    options?: BulkCreateOptions,
  ): Promise<T[]> {
    return this.model.bulkCreate(data, options);
  }

  async update(
    id: string | number,
    data: Partial<T['_attributes']>,
    options?: InstanceUpdateOptions,
  ): Promise<T> {
    const instance = await this.findById(id, options);
    if (!instance) {
      throw new HttpError(404, `${this.model.name} not found`);
    }
    return instance.update(data, options);
  }

  async delete(id: string | number, options?: DestroyOptions): Promise<void> {
    const instance = await this.findById(id, options);
    if (!instance) {
      throw new HttpError(404, `${this.model.name} not found`);
    }
    await instance.destroy(options);
  }

  async bulkUpdate(
    data: Partial<T['_attributes']>,
    where: WhereOptions,
    options?: UpdateOptions,
  ): Promise<[number]> {
    return this.model.update(data, {...options, where});
  }

  async bulkDelete(
    where: WhereOptions,
    options?: DestroyOptions,
  ): Promise<number> {
    return this.model.destroy({...options, where});
  }
}
