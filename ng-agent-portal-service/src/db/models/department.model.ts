import {Model, DataTypes, Optional} from 'sequelize';
import sequelize from '@db/index';

// Department attributes interface
export interface DepartmentAttributes {
  id: string;
  name: string;
  isDefault: boolean;
  createdAt: Date;
  updatedAt: Date;
  deletedAt?: Date;
}

// Department creation attributes interface (optional fields for creation)
export type DepartmentCreationAttributes = Optional<
  DepartmentAttributes,
  'id' | 'createdAt' | 'updatedAt' | 'deletedAt'
>;

// Department model class
class Department extends Model<
  DepartmentAttributes,
  DepartmentCreationAttributes
> {
  declare id: string;
  declare name: string;
  declare isDefault: boolean;
  declare createdAt: Date;
  declare updatedAt: Date;
  declare deletedAt?: Date;

  // Define associations
  static associate(_models: any): void {
    Department.hasMany(_models.Agent, {
    foreignKey: 'departmentId',
    as: 'agents',
  });
  }
}

// Initialize Department model
Department.init(
  {
    id: {
      type: DataTypes.UUIDV4,
      primaryKey: true,
      defaultValue: DataTypes.UUIDV4,
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true,
    },
    isDefault: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    deletedAt: {
      type: DataTypes.DATE,
      allowNull: true,
    },
  },
  {
    sequelize,
    modelName: 'Department',
    tableName: 'departments',
    timestamps: true,
    paranoid: true, // Enable soft delete
  },
);

export default Department;
