import {Model, DataTypes, Optional} from 'sequelize';
import sequelize from '@db/index';

export interface AgentAttributes {
  id: string;
  kcId: string;
  departmentId: string | null;
  availability: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  deletedAt?: Date;
}

export type AgentCreationAttributes = Optional<
  AgentAttributes,
  'id' | 'createdAt' | 'updatedAt' | 'deletedAt'
>;

class Agent extends Model<AgentAttributes, AgentCreationAttributes> {
  declare id: string;
  declare kcId: string;
  declare name?: string;
  declare email?: string;
  declare mobile?: string;
  declare departmentId: string | null;
  declare availability: string;
  declare isActive: boolean;
  declare createdAt: Date;
  declare updatedAt: Date;
  declare deletedAt?: Date;

  static associate(_models: any): void {
    Agent.belongsTo(_models.Department, {
      foreignKey: 'departmentId',
      as: 'department',
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
    });
  }
}

Agent.init(
  {
    id: {
      type: DataTypes.UUID,
      primaryKey: true,
      defaultValue: DataTypes.UUIDV4,
    },
    kcId: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true,
    },
    departmentId: {
      type: DataTypes.UUID,
      allowNull: true,
    },
    availability: {
      type: DataTypes.STRING,
      allowNull: false,
      defaultValue: 'offline',
      validate: {
        isIn: [['offline', 'online', 'away', 'busy']],
      },
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    deletedAt: {
      type: DataTypes.DATE,
      allowNull: true,
    },
  },
  {
    sequelize,
    modelName: 'Agent',
    tableName: 'agents',
    timestamps: true,
    paranoid: true,
  },
);

export default Agent;
