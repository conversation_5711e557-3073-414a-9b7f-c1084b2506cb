{"name": "ng-agent-portal-service", "version": "1.0.0", "description": "Agent portal microservice for nGage platform", "main": "dist/server.js", "scripts": {"dev": "ts-node-dev --respawn --transpile-only -r tsconfig-paths/register src/server.ts", "build": "tsc", "prestart": "npm run build", "start": "node -r module-alias/register dist/server.js", "clean": "rm -rf dist", "rebuild": "npm run clean && npm run build", "migrate": "sequelize-cli db:migrate", "migrate:undo": "sequelize-cli db:migrate:undo", "seed": "sequelize-cli db:seed:all", "lint": "eslint . --ext .ts", "lint:fix": "eslint . --ext .ts --fix", "format": "prettier --write \"src/**/*.ts\"", "test": "jest --detect<PERSON><PERSON><PERSON><PERSON><PERSON> --forceExit", "prepare": "husky install", "commit": "cz"}, "keywords": ["agent", "portal", "express", "typescript", "sequelize", "microservice"], "author": "", "license": "ISC", "dependencies": {"class-transformer": "^0.5.1", "class-validator": "^0.14.0", "cors": "^2.8.5", "dotenv": "^16.0.3", "express": "^4.18.2", "express-rate-limit": "^6.7.0", "helmet": "^6.1.5", "jsonwebtoken": "^9.0.0", "jwks-rsa": "^3.2.0", "module-alias": "^2.2.3", "morgan": "^1.10.0", "mysql2": "^3.14.1", "pg": "^8.10.0", "pg-hstore": "^2.3.4", "reflect-metadata": "^0.1.13", "sequelize": "^6.31.0", "supertest": "^7.1.1", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^4.6.2", "tsconfig-paths": "^4.2.0", "uuid": "^11.1.0", "winston": "^3.8.2"}, "devDependencies": {"@commitlint/cli": "^16.3.0", "@commitlint/config-conventional": "^16.2.4", "@types/cors": "^2.8.13", "@types/express": "^4.17.17", "@types/jest": "^29.5.1", "@types/jsonwebtoken": "^9.0.2", "@types/morgan": "^1.9.4", "@types/node": "^18.16.0", "@types/supertest": "^6.0.3", "@types/swagger-jsdoc": "^6.0.1", "@types/swagger-ui-express": "^4.1.3", "@typescript-eslint/eslint-plugin": "^5.59.1", "@typescript-eslint/parser": "^5.59.1", "commitizen": "^4.3.1", "cz-conventional-changelog": "^3.3.0", "cz-customizable": "^6.9.2", "eslint": "^8.39.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^4.2.1", "husky": "^7.0.4", "jest": "^29.5.0", "lerna": "^7.3.0", "nyc": "^15.1.0", "prettier": "^2.8.8", "run-s": "^0.0.0", "sequelize-cli": "^6.6.0", "sequelize-mock": "^0.10.2", "ts-jest": "^29.1.0", "ts-node": "^10.9.1", "ts-node-dev": "^2.0.0", "typescript": "^5.0.4"}, "config": {"commitizen": {"path": "./node_modules/cz-customizable"}, "cz-customizable": {"config": "./.cz-config.cjs"}}, "_moduleAliases": {"@controllers": "dist/api/controllers", "@middlewares": "dist/api/middlewares", "@dtos": "dist/api/dtos", "@routes": "dist/api/routes", "@services": "dist/api/services", "@config": "dist/config", "@db": "dist/db", "@types": "dist/types", "@utils": "dist/utils"}}