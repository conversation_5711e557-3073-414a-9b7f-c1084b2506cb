variables:
  IMAGE_TAG: ''
  SONAR_PROJECT_NAME: 'MCS_CPAAS_8x_RM_Agent-Portal-UI'
  HARBOR_IMAGE: $HARBOR_REGISTRY_BASE_URL/$HARBOR_REGISTRY_REPO/$CI_PROJECT_TITLE
  COLON: ':'

stages:
  - build
  - code_quality
  - package
  - sast_scan
#   - deploy_tag
#   - build_notify
#   - deploy
#   - functional_test
#   - deploy_notify

npm_build:
  variables:
    GIT_STRATEGY: clone
  stage: build
  tags:
    - automation-runner
  allow_failure: false
  before_script:
    - echo "Fixing all permissions"
    - sudo chown -R gitlab-runner:gitlab-runner .
    - sudo chmod -R u+w .
  script:
    - npm -v
    - node -v
    - npm i
    - npm run prettier:cli
    - npm run lint
    - npm run build
  after_script:
    - echo "${CI_JOB_STATUS}" > build_status
  artifacts:
    paths:
      - build_status

unit_test:
  variables:
    GIT_STRATEGY: none
  stage: code_quality
  tags:
    - automation-runner
  allow_failure: true
  needs: ['npm_build']
  script:
    - echo "Unit Tests"
    - npm run test:coverage
    - cp -r ./node_modules/
  #    - nyc --reporter=lcov --reporter=text-summary npm start
  #    - nyc --reporter=lcov npm test
  after_script:
    - echo "${CI_JOB_STATUS}" > utest_status
  artifacts:
    when: always
    paths:
      - utest_status
      - coverage
    reports:
      junit:
        - coverage/lcov-report/index.html

sonarqube:
  variables:
    GIT_STRATEGY: none
  stage: code_quality
  tags:
    - automation-runner
  needs: ['npm_build']
  allow_failure: true
  script:
    #- mvn clean install
    - echo "Sonarqube to run here"
    - curl -X POST "http://************:8080/job/MCS_CPAAS_8x_RM_Agent-Portal-UI/build?token=Agent-Portal-UI"
  after_script:
    - echo "${CI_JOB_STATUS}" > sonar_status
  artifacts:
    paths:
      - sonar_status

build_docker_image:
  variables:
    GIT_STRATEGY: none
  stage: package
  tags:
    - automation-runner
  needs: ['npm_build', 'sonarqube', 'unit_test']
  allow_failure: true
  before_script:
    - >
      if [ "${CI_COMMIT_TAG}" == "" ]; then
        IMAGE_TAG="${CI_COMMIT_SHORT_SHA}"
      else
        IMAGE_TAG="${CI_COMMIT_TAG}"
      fi
    - echo "${IMAGE_TAG}"
  script:
    - PROJECT_TITLE=agent-portal-ui;
    - TAG_NAME=agentportaluitag;
    - TYPE=Node;
    - /bin/sh -x $STATIC_DELIVERABLES/package.sh $PROJECT_TITLE ${IMAGE_TAG} ${CI_COMMIT_REF_NAME} $TAG_NAME $TYPE ;

  after_script:
    #- docker push $IMAGE_NAME
    - echo "${CI_JOB_STATUS}" > docker_build_status
  artifacts:
    paths:
      - docker_build_status

fortify_scan:
  stage: sast_scan
  tags:
    - automation-runner
  allow_failure: true
  variables:
    GIT_STRATEGY: none
  script:
    - service=mbs_ngage_ng_agent_portal_ui
    - SOURCE_PATH="sf/chatbot/ng-agent-portal-ui"
    - REPORT_NAME=ng-agent-portal-ui-report
    - version=1.1
    - /bin/sh -x $STATIC_DELIVERABLES/fortify-scan.sh $SOURCE_PATH $service $REPORT_NAME $version 
    # Generate artifacts URL and pass to notification script
 #   - ARTIFACTS_URL="${CI_PROJECT_URL}/-/jobs/${CI_JOB_ID}/artifacts/browse/reports/"
# - /bin/sh -x $STATIC_DELIVERABLES/fortify-notify.sh $service $CI_PIPELINE_URL $CI_PIPELINE_ID "$ARTIFACTS_URL"
  artifacts:
    paths:
      - reports/
    expire_in: 1 week
# deploy_tag:
#   stage: deploy_tag
#   only:
#     - ngage-dev-eng
#   tags:
#     - automation-runner
#   variables:
#     GIT_STRATEGY: clone
#   needs: ['build_docker_image']
#   before_script:
#     - IMAGE_TAG="${CI_COMMIT_SHORT_SHA}"
#     - echo "${IMAGE_TAG}"
#     - service=accountsservicetag;
#     - /bin/sh -x $STATIC_DELIVERABLES/multi-deploy.sh $service ${IMAGE_TAG} ;
#   script:
#     - >
#       echo "tag deployed successfully"

# build_notify:
#   stage: build_notify
#   tags:
#     - automation-runner
#   variables:
#     GIT_STRATEGY: none
#   allow_failure: false
#   script:
#     - echo "Package created"
#     - '[[ -f build_status ]] && BUILD_STATUS=$(cat build_status)'
#     - '[[ -f docker_build_status ]] && IMAGE_STATUS=$(cat docker_build_status)'
#     - '[[ -f sonar_status ]] && SONAR_STATUS=$(cat sonar_status)'
#     - PROJECT_TITLE=accounts-service;
#     - /bin/sh -x $STATIC_DELIVERABLES/build-notify.sh $PROJECT_TITLE ${CI_PIPELINE_URL} ${CI_PIPELINE_ID} $BUILD_STATUS $IMAGE_STATUS $SONAR_STATUS ;

# dev_deploy:
#   variables:
#     GIT_STRATEGY: none
#   stage: deploy
#   tags:
#     - automation-runner
#   needs: ['npm_build', 'sonarqube', 'unit_test', 'build_docker_image', 'build_notify']
#   allow_failure: true
#   before_script:
#     - echo "Before Script"
#   script:
#     - echo "K8 Deployment"
#   after_script:
#     - echo "${CI_JOB_STATUS}" > deploy_status
#   artifacts:
#     paths:
#       - deploy_status

# functional_test:
#   variables:
#     GIT_STRATEGY: none
#   stage: functional_test
#   tags:
#     - automation-runner
#   needs: ['build_notify', 'dev_deploy', 'unit_test']
#   allow_failure: true
#   before_script:
#     - echo "Before Script"
#   script:
#     - echo "Functional Testing"
#   after_script:
#     - echo "${CI_JOB_STATUS}" > fTest_status
#   artifacts:
#     paths:
#       - fTest_status

# deploy_notify:
#   stage: deploy_notify
#   tags:
#     - automation-runner
#   variables:
#     GIT_STRATEGY: none
#   allow_failure: true
#   script:
#     - echo "Package created"
#     - '[[ -f deploy_status ]] && DEPLOY_STATUS=$(cat deploy_status)'
#     - '[[ -f fTest_status ]] && FTEST_STATUS=$(cat fTest_status)'
#     - PROJECT_TITLE=account-service;
#     - /bin/sh -x $STATIC_DELIVERABLES/mail-generate.sh $PROJECT_TITLE ${CI_PIPELINE_URL} ${CI_PIPELINE_ID} $DEPLOY_STATUS $FTEST_STATUS ;
