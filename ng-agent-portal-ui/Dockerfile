# --- Stage 1: Build Vite React app ---
FROM node:20-alpine AS builder

WORKDIR /app
COPY . .

RUN npm install
RUN npm run build

# --- Stage 2: Serve with NGINX ---
FROM nginx:alpine

# Copy built frontend to nginx html dir
COPY --from=builder /app/dist /usr/share/nginx/html

# Copy config injector script
COPY inject-config.sh /app/inject-config.sh
RUN chmod +x /app/inject-config.sh

# ✅ Fix: Set permissions for Kubernetes non-root user (UID 1001)
RUN chown -R 1001:1001 /usr/share/nginx/html && \
    chmod -R g+w /usr/share/nginx/html

# ✅ Keep default nginx user (will be overridden by K8s `runAsUser: 1001`)
USER 1001

# Set entrypoint to run the config injector and start nginx
ENTRYPOINT ["/app/inject-config.sh"]
