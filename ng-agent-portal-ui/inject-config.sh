#!/bin/sh

CONFIG_PATH="/usr/share/nginx/html/config.json"
INDEX_PATH="/usr/share/nginx/html/index.html"

if [ -f "$CONFIG_PATH" ]; then
  echo "Injecting runtime config..."

  # Safely encode <PERSON><PERSON><PERSON> and inject using JS
  BASE64_ENCODED=$(base64 "$CONFIG_PATH" | tr -d '\n')

  # Replace placeholder in index.html
  sed -i "s|__APP_CONFIG_PLACEHOLDER__|window.__APP_CONFIG__ = JSON.parse(atob('$BASE64_ENCODED'));|" "$INDEX_PATH"

  echo "Config injected."
else
  echo "No config.json found to inject."
fi

# Start NGINX
exec nginx -g "daemon off;"
