import {Suspense} from 'react';
import {Route, Routes} from 'react-router-dom';
import NotFound from '../../Pages/NotFound/NotFound';
import mainLayoutRouteConfig from './mainLayoutRouteConfig';
import {SidebarProvider} from '../../components/ui/sidebar';
import {AppSidebar} from '../../components/AppSidebar/AppSidebar';
import {SideBarToggleBtn, TopBar} from '../../components';
import {MESSAGES} from '@/constants/messages.constant';
import {useTranslation} from 'react-i18next';
import AuthorizedRoute from '@/components/AuthorizedRoute';
import {AuthGuard} from '@/components/AuthGuard';
import RedirectHandler from '@/components/RedirectHandler';

const MainLayout = () => {
  const {t} = useTranslation();

  return (
    <SidebarProvider
      style={
        {
          '--sidebar-width-icon': '4.5rem',
          '--sidebar-width': '14rem',
        } as React.CSSProperties
      }
    >
      <AppSidebar />
      <div className="flex-1 flex flex-col">
        <TopBar />
        <SideBarToggleBtn />
        <main className="flex-1 p-6 flex">
          <Suspense fallback={t(MESSAGES.GLOBAL_LOADING_FALLBACK)}>
            <Routes>
              <Route path="/" element={<RedirectHandler />} />
              {mainLayoutRouteConfig.map(({path, component: Component, requiredPermissions}) => (
                <Route
                  key={path}
                  path={path}
                  element={
                    <AuthGuard>
                      <AuthorizedRoute element={<Component />} requiredPermissions={requiredPermissions} />
                    </AuthGuard>
                  }
                />
              ))}
              <Route path="*" element={<NotFound />} />
            </Routes>
          </Suspense>
        </main>
      </div>
    </SidebarProvider>
  );
};

export default MainLayout;
