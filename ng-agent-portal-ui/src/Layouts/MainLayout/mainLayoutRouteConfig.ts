import {lazy} from 'react';
import {RouteConstant} from '../../constants';
import {PermissionsEnum} from '@/enums';

const TeamsPage = lazy(() => import('@/Pages/Teams/TeamsPage'));
const HomePage = lazy(() => import('@/Pages/Home/Home'));
const AddAgentPage = lazy(() => import('@/Pages/Agents/AddAgentPage'));
const InboxPage = lazy(() => import('@/Pages/Inbox/Inbox'));
const SettingsPage = lazy(() => import('@/Pages/Settings/Settings'));
const BusinessHoursPage = lazy(() => import('@/Pages/Settings/ChildrenPages/BusinessHours'));
const CustomHoursPage = lazy(() => import('@/Pages/Settings/ChildrenPages/CustomBusinessHoursPanel'));
const RoutingRulesPage = lazy(() => import('@/Pages/Settings/ChildrenPages/RoutingRules'));
const ForbiddenPage = lazy(() => import('@/Pages/Forbidden/Forbidden'));

const mainLayoutRouteConfig = [
  {
    path: RouteConstant.HOME,
    component: HomePage,
    requiredPermissions: [], // Open for all authenticated users
  },
  {
    path: RouteConstant.TEAMS,
    component: TeamsPage,
    requiredPermissions: [PermissionsEnum.READ_DEPARTMENT, PermissionsEnum.READ_AGENT],
  },
  {
    path: RouteConstant.ADD_AGENT,
    component: AddAgentPage,
    requiredPermissions: [PermissionsEnum.CREATE_AGENT],
  },
  {
    path: RouteConstant.INBOX,
    component: InboxPage,
    requiredPermissions: [], //  inbox is accessible to all agents
  },
  {
    path: RouteConstant.SETTINGS,
    component: SettingsPage,
    requiredPermissions: [], // Top-level settings navigation — can be open or secured generically
  },
  {
    path: RouteConstant.BUSINESS_HOURS,
    component: BusinessHoursPage,
    requiredPermissions: [PermissionsEnum.READ_DEPARTMENT],
  },
  {
    path: RouteConstant.ADD_BUSINESS_HOURS,
    component: CustomHoursPage,
    requiredPermissions: [PermissionsEnum.UPDATE_DEPARTMENT],
  },
  {
    path: RouteConstant.EDIT_BUSINESS_HOURS,
    component: CustomHoursPage,
    requiredPermissions: [PermissionsEnum.UPDATE_DEPARTMENT],
  },
  {
    path: RouteConstant.SETTINGS_ROUTING_RULES,
    component: RoutingRulesPage,
    requiredPermissions: [PermissionsEnum.UPDATE_DEPARTMENT],
  },
  {
    path: RouteConstant.FORBIDDEN,
    component: ForbiddenPage,
    requiredPermissions: [],
  },
];

export default mainLayoutRouteConfig;
