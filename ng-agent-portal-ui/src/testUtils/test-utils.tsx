// test-utils.tsx or setupTests.ts
import {render} from '@testing-library/react';
import {Provider} from 'react-redux';
import {SnackbarProvider} from 'notistack';
import type {ReactElement} from 'react';
import type {AppStore} from '@/redux/store';
import {store as defaultStore} from '@/redux/store';
import {MemoryRouter} from 'react-router-dom';

export function renderWithStore(ui: ReactElement, store: AppStore = defaultStore) {
  return render(
    <Provider store={store}>
      <SnackbarProvider>{ui}</SnackbarProvider>
    </Provider>,
  );
}

export function renderWithRouter(ui: ReactElement) {
  return render(<MemoryRouter>{ui}</MemoryRouter>);
}

export function renderWithProviders(ui: ReactElement, store: AppStore = defaultStore) {
  return render(
    <Provider store={store}>
      <MemoryRouter>{ui}</MemoryRouter>
    </Provider>,
  );
}
