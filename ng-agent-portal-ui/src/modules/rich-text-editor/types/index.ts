import type {Mark} from 'prosemirror-model';
import {EditorState, Transaction} from 'prosemirror-state';
import {EditorView} from 'prosemirror-view';

export type RTEditorState = {state: EditorState; instance: EditorInstance};
interface RTECommonProp {
  content: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  isToolbar?: boolean;
  selectedFiles?: File[];
  onFileRemove?: (index: number) => void;
  readOnly?: boolean;
  onEditorState?: (args: RTEditorState) => void;
}

export type RichTextEditorProps =
  | (Partial<RTECommonProp> & {
      readOnly: true;
    })
  | RTECommonProp;

export interface EditorInstance {
  view: EditorView; // Plain EditorView without custom methods
  state: EditorState;
  getContent: () => string;
  setContent: (content: string) => void;
  focus: () => void;
  destroy: () => void;
  insertLink: (href: string, title?: string) => void;
  toggleSuperscript: () => void; // Moved here
  toggleSubscript: () => void; // Moved here
}

export interface MenuItemConfig {
  title: string;
  icon: string;
  command: (state: EditorState, dispatch?: CommandDispatch) => boolean;
  active?: (state: EditorState) => boolean;
}

export type CommandDispatch = (tr: Transaction) => void;

export type NodeMark = Mark & {
  attrs: {
    href?: string;
    title?: string;
  };
};
