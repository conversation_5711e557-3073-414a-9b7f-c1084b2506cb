/* ProseMirror Editor Styles */
.ProseMirror {
  outline: none;
  position: relative;
    line-height: 1.5; /* Ensure proper line spacing for visibility */

}

.ProseMirror[data-placeholder]:empty::before {
  content: attr(data-placeholder);
  color: #9ca3af; /* text-gray-400 */
  pointer-events: none;
  position: absolute;
}

/* prosemirror.css */
.ProseMirror sup {
  vertical-align: super;
  font-size: 0.8em;
}

.ProseMirror sub {
  vertical-align: sub; /* Ensure subscript is below baseline */
  font-size: 0.8em; /* Adjust size as needed */
}

button sup,
button sub {
  margin-left: 2px; /* Small spacing for clarity */
}

.ProseMirror h1 {
  font-size: 1.875rem; /* text-3xl = 30px */
  font-weight: 700;    /* font-bold */
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
}

.ProseMirror h2 {
  font-size: 1.5rem;   /* text-2xl = 24px */
  font-weight: 700;
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
}

.ProseMirror h3 {
  font-size: 1.25rem;  /* text-xl = 20px */
  font-weight: 700;
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
}

.ProseMirror p {
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
}

.ProseMirror ul {
  padding-left: 1.5rem; /* pl-6 = 24px */
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
  list-style-type: disc;
  list-style-position: outside;
}

.ProseMirror ol {
  padding-left: 1.5rem;
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
  list-style-type: decimal;
  list-style-position: outside;
}

.ProseMirror li {
  margin-top: 0.25rem;
  margin-bottom: 0.25rem;
}

.ProseMirror blockquote {
  border-left: 4px solid #d1d5db; /* border-gray-300 */
  padding-left: 1rem;
  margin-top: 1rem;
  margin-bottom: 1rem;
  font-style: italic;
  color: #4b5563; /* text-gray-600 */
}

.ProseMirror pre {
  background-color: #f3f4f6; /* bg-gray-100 */
  border-radius: 0.375rem; /* rounded-md = 6px */
  padding: 1rem;
  margin-top: 1rem;
  margin-bottom: 1rem;
  overflow-x: auto;
}

.ProseMirror code {
  background-color: #f3f4f6; /* bg-gray-100 */
  padding: 0.125rem 0.25rem; /* px-1 py-0.5 */
  border-radius: 0.25rem; /* rounded = 4px */
  font-size: 0.875rem; /* text-sm */
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
}

.ProseMirror pre code {
  background-color: transparent;
  padding: 0;
}

.ProseMirror u {
  text-decoration: underline;
}

.ProseMirror strong {
  font-weight: 700;
}

.ProseMirror em {
  font-style: italic;
}
