import {cn} from '@/lib/utils';
import type {Command} from 'prosemirror-state';
import React from 'react';

interface MenuItemProps {
  title: string;
  command: Command;
  isActive?: boolean;
  children: React.ReactNode;
  onExecute: (command: Command) => void;
}

const MenuItem: React.FC<MenuItemProps> = ({title, command, isActive = false, children, onExecute}) => (
  <button
    type="button"
    title={title}
    className={cn(
      `px-2 py-1 rounded text-sm font-medium transition-colors text-gray-700 ${
        isActive ? 'bg-blue-100' : 'hover:bg-gray-200'
      } disabled:opacity-50 disabled:cursor-not-allowed`,
    )}
    onClick={() => onExecute(command)}
  >
    {children}
  </button>
);

export default MenuItem;
