import {describe, it, expect, vi, beforeEach, beforeAll} from 'vitest';
import {render, screen, fireEvent} from '@testing-library/react';
import RichTextEditor from '../RichTextEditor';

beforeAll(() => {
  global.URL.createObjectURL = vi.fn(() => 'mocked-url');
});
vi.mock('../hooks/useEditor', () => ({
  useEditor: vi.fn(() => [vi.fn(), {view: {}}, {}]),
}));

vi.mock('./MenuBar', () => ({
  __esModule: true,
  default: () => <div data-testid="menu-bar">MenuBar</div>,
}));

const mockImageFile = new File(['dummy'], 'test-image.png', {type: 'image/png'});
const mockDocFile = new File(['dummy'], 'test-doc.pdf', {type: 'application/pdf'});

describe('RichTextEditor', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders editor container', () => {
    render(<RichTextEditor content="Test" onChange={() => {}} />);
    expect(screen.getByTestId('rte-container')).toBeInTheDocument();
  });

  it('renders image file preview with correct details', () => {
    render(<RichTextEditor content="" onChange={() => {}} selectedFiles={[mockImageFile]} onFileRemove={() => {}} />);

    expect(screen.getByTestId('file-preview-image')).toBeInTheDocument();
    expect(screen.getByTestId('file-name')).toHaveTextContent('test-image.png');
    expect(screen.getByTestId('file-size')).toHaveTextContent('5.00 B');
    expect(screen.getByTestId('remove-file-button')).toBeInTheDocument();
  });

  it('renders non-image file preview with correct details', () => {
    render(<RichTextEditor content="" onChange={() => {}} selectedFiles={[mockDocFile]} onFileRemove={() => {}} />);

    expect(screen.getByTestId('file-preview-doc')).toBeInTheDocument();
    expect(screen.getByTestId('file-name')).toHaveTextContent('test-doc.pdf');
    expect(screen.getByTestId('file-size')).toHaveTextContent('5.00 B');
    expect(screen.getByTestId('remove-file-button')).toBeInTheDocument();
  });

  it('triggers onFileRemove callback on remove button click', () => {
    const handleRemove = vi.fn();
    render(<RichTextEditor content="" onChange={() => {}} selectedFiles={[mockDocFile]} onFileRemove={handleRemove} />);

    fireEvent.click(screen.getByTestId('remove-file-button'));
    expect(handleRemove).toHaveBeenCalledWith(0);
  });

  it('renders toolbar when not readOnly', () => {
    render(<RichTextEditor content="test" onChange={() => {}} />);
    expect(screen.getByTestId('menu-bar')).toBeInTheDocument();
  });

  it('hides toolbar when readOnly is true', () => {
    render(<RichTextEditor content="test" onChange={() => {}} readOnly />);
    expect(screen.queryByTestId('menu-bar')).not.toBeInTheDocument();
  });

  it('hides file preview in readOnly mode', () => {
    render(<RichTextEditor content="" onChange={() => {}} selectedFiles={[mockImageFile]} readOnly />);
    expect(screen.queryByTestId('file-preview-image')).not.toBeInTheDocument();
  });
});
