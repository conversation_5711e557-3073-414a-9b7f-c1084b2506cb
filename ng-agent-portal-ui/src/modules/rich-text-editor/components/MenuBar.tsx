import React from 'react';
import type {Command, EditorState} from 'prosemirror-state';
import {EditorView} from 'prosemirror-view';
import {commands, isMarkActive} from '../utils/commands';
import {schema} from '../utils/schema';
import MenuItem from './MenuItem';

interface MenuBarProps {
  editorState: EditorState;
  editorView: EditorView;
}

interface MenuItemConfig {
  title: string;
  command: Command;
  content: React.ReactNode | ((state: EditorState) => React.ReactNode);
  isActive?: (state: EditorState) => boolean;
}

const menuItems: (MenuItemConfig | 'separator')[] = [
  {
    title: 'Bold (Ctrl+B)',
    command: commands.toggleBold,
    content: <strong className="font-bold">B</strong>,
    isActive: state => isMarkActive(state, schema.marks.strong),
  },
  {
    title: 'Italic (Ctrl+I)',
    command: commands.toggleItalic,
    content: <em className="italic">I</em>,
    isActive: state => isMarkActive(state, schema.marks.em),
  },
  {
    title: 'Underline',
    command: commands.toggleUnderline,
    content: <u className="underline">U</u>,
    isActive: state => isMarkActive(state, schema.marks.underline),
  },
  {
    title: 'Strike Through',
    command: commands.toggleStrike,
    content: <span className="line-through">S</span>,
    isActive: state => isMarkActive(state, schema.marks.strike),
  },
  'separator',
  {
    title: 'Highlight',
    command: commands.toggleHighlight,
    content: (
      <span className="relative inline-block">
        <span>A</span>
        <span className="absolute bottom-0 left-0 h-[3px] w-full bg-yellow-200 rounded-sm"></span>
      </span>
    ),
    isActive: state => isMarkActive(state, schema.marks.highlight),
  },
  {
    title: 'Superscript/Subscript (Ctrl+Shift+^/_)',
    command: commands.toggleSupSub,
    content: (state: EditorState) => {
      const hasSuperscript = isMarkActive(state, schema.marks.superscript);
      const hasSubscript = isMarkActive(state, schema.marks.subscript);
      return (
        <span className="text-sm inline-flex items-baseline">
          <span className='text-[0.8em]'>A</span>A{hasSubscript ? <sub>a</sub> : hasSuperscript ? <sup>a</sup> : ''}
        </span>
      );
    },
    isActive: state => isMarkActive(state, schema.marks.superscript) || isMarkActive(state, schema.marks.subscript),
  },
];

const MenuBar: React.FC<MenuBarProps> = ({editorState, editorView}) => {
  const executeCommand = (command: Command) => {
    command(editorState, editorView.dispatch);
    editorView.focus();
  };

  return (
    <div
      data-testid="menu-bar"
      className="flex flex-wrap justify-center items-center py-2 w-3/12 shadow-md gap-0.5 rounded-lg shadow-gray-200 z-50"
    >
      {menuItems.map((item, index) => {
        if (item === 'separator') {
          return <div key={index} className="w-px h-6 bg-gray-300 mx-1" />;
        }

        return (
          <MenuItem
            key={index}
            title={item.title}
            command={item.command}
            isActive={item.isActive ? item.isActive(editorState) : false}
            onExecute={executeCommand}
          >
            {typeof item.content === 'function' ? item.content(editorState) : item.content}
          </MenuItem>
        );
      })}
    </div>
  );
};

export default MenuBar;
