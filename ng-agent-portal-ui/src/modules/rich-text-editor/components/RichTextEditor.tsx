import React from 'react';
import {useEditor} from '../hooks/useEditor';
import MenuBar from './MenuBar';
import type {RichTextEditorProps} from '../types';
import '../styles/prosemirror.css';
import {File, X} from 'lucide-react';
import {cn} from '@/lib/utils';
import {formatFileSize} from '../utils/filesize.utils';
import {useTranslation} from 'react-i18next';

const RichTextEditor: React.FC<RichTextEditorProps> = ({
  content = '',
  onChange,
  placeholder = 'Write Message..',
  className = '',
  selectedFiles = [],
  onFileRemove,
  onEditorState,
  readOnly = false,
  isToolbar = true,
}) => {
  const [editorRef, editorInstance, editorState] = useEditor({
    content,
    onChange,
    placeholder,
    readOnly,
    onEditorState,
  });
  const {i18n} = useTranslation();
  const direction = typeof i18n.dir === 'function' ? i18n.dir() : 'ltr';
  return (
    <div
      data-testid="rte-container"
      className={cn(
        'flex flex-col gap-3 overflow-hidden',
        !readOnly && 'border bg-white border-gray-300 rounded-lg p-2 px-2',
        className,
      )}
    >
      {/* File Preview only for compose mode */}
      {!readOnly && selectedFiles.length > 0 && (
        <div className="flex flex-wrap gap-2" data-testid="file-preview-container">
          {selectedFiles.map((file, index) => (
            <div key={index} className="flex justify-between items-center w-80 h-14 py-1 px-3 bg-gray-100 rounded">
              {file.type.startsWith('image/') ? (
                <div className="flex justify-start items-center gap-2" data-testid="file-preview-image">
                  <img
                    src={URL.createObjectURL(file)}
                    alt={file.name}
                    className="h-8 w-8 object-cover rounded border border-gray-400"
                  />
                  <div className="flex flex-col gap-1">
                    <span data-testid="file-name" className="text-sm font-medium truncate max-w-32">
                      {file.name}
                    </span>
                    <span data-testid="file-size" className="text-xs text-gray-500">
                      {formatFileSize(file.size)}
                    </span>
                  </div>
                </div>
              ) : (
                <div className="flex items-center gap-1" data-testid="file-preview-doc">
                  <span className="w-8 h-8 flex items-center justify-center text-sm rounded">
                    <File className="w-7 h-7" />
                  </span>
                  <div className="flex flex-col gap-1">
                    <span data-testid="file-name" className="text-sm font-medium truncate max-w-32">
                      {file.name}
                    </span>
                    <span data-testid="file-size" className="text-xs text-gray-500">
                      {formatFileSize(file.size)}
                    </span>
                  </div>
                </div>
              )}

              {onFileRemove && (
                <button
                  type="button"
                  className="text-gray-400 cursor-pointer"
                  onClick={() => onFileRemove(index)}
                  data-testid="remove-file-button"
                >
                  <X className="w-6 h-6" />
                </button>
              )}
            </div>
          ))}
        </div>
      )}

      {/* Toolbar */}
      {!readOnly && editorState && editorInstance && isToolbar && (
        <MenuBar editorState={editorState} editorView={editorInstance.view} />
      )}

      {/* ProseMirror Editor Container */}
      <div
        ref={editorRef}
        className={cn(
          'text-sm z-0 py-2 overflow-auto prose prose-sm max-w-none no-scrollbar',
          readOnly ? 'bg-transparent w-auto h-auto px-1 py-0 m-0' : 'w-8/12 max-h-28 h-full bg-white',
        )}
        style={{direction}}
      />
    </div>
  );
};

export default RichTextEditor;
