import {RICHTEXTEDITOR, SIZE_UNITS} from './constant';

const BYTE_UNIT = RICHTEXTEDITOR.size;

export const formatFileSize = (sizeInBytes: number): string => {
  if (typeof sizeInBytes !== 'number' || sizeInBytes < 0) {
    return '0 B';
  }

  const units = Object.values(SIZE_UNITS);
  let size = sizeInBytes;
  let unitIndex = 0;

  while (size >= BYTE_UNIT && unitIndex < units.length - 1) {
    size /= BYTE_UNIT;
    unitIndex++;
  }

  const precision = [0, 1, 2, 2][unitIndex] || 2;

  return `${size.toFixed(precision)} ${units[unitIndex]}`;
};
