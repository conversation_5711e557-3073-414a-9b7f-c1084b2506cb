'use client';

import React from 'react';
import {useNavigate} from 'react-router-dom';
import {Card, CardContent} from '@/components/ui/card';
import {useTranslation} from 'react-i18next';
import {RouteConstant} from '@/constants';
import BusinessHoursIcon from '@/assets/settingsPage/BusinessHours.svg';
import RoutingRulesIcon from '@/assets/settingsPage/RoutingRules.svg';
import ChatInteractionControlsIcon from '@/assets/settingsPage/ChatInteraction.svg';
import LLMConfigurationIcon from '@/assets/settingsPage/LlmConfig.svg';
import CannedResponsesIcon from '@/assets/settingsPage/CannedResponses.svg';

interface SettingItem {
  id: string;
  title: string;
  description: string;
  image: string;
  route: string;
}

const settings: SettingItem[] = [
  {
    id: 'routing-rules',
    title: 'SETTINGS_ROUTING_TITLE',
    description: 'SETTINGS_ROUTING_DESCRIPTION',
    image: RoutingRulesIcon,
    route: RouteConstant.SETTINGS_ROUTING_RULES,
  },
  {
    id: 'business-hours',
    title: 'SETTINGS_BUSINESS_HOURS_TITLE',
    description: 'SETTINGS_BUSINESS_HOURS_DESCRIPTION',
    image: BusinessHoursIcon,
    route: RouteConstant.BUSINESS_HOURS,
  },
  {
    id: 'chat-interaction-controls',
    title: 'SETTINGS_INTERACTIONS_TITLE',
    description: 'SETTINGS_INTERACTIONS_DESCRIPTION',
    image: ChatInteractionControlsIcon,
    route: RouteConstant.SETTINGS_CHAT_INTERACTION_CONTROLS,
  },
  {
    id: 'llm-configuration',
    title: 'SETTINGS_LLM_TITLE',
    description: 'SETTINGS_LLM_DESCRIPTION',
    image: LLMConfigurationIcon,
    route: RouteConstant.SETTINGS_LLM_CONFIGURATION,
  },
  {
    id: 'canned-responses',
    title: 'SETTINGS_CANNED_RESPONSES_TITLE',
    description: 'SETTINGS_CANNED_RESPONSES_DESCRIPTION',
    image: CannedResponsesIcon,
    route: RouteConstant.SETTINGS_CANNED_RESPONSES,
  },
];

const SettingsPage: React.FC = () => {
  const navigate = useNavigate();
  const {t} = useTranslation();

  const handleNavigate = (route: string) => {
    // Intentionally not awaiting navigate
    void navigate(route); // NOSONAR
  };

  return (
    <div className="w-full">
      <h2 className="text-xl font-medium mb-4">{t('SETTINGS')}</h2>
      <div className="px-16 py-6 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 font-poppins ">
        {settings.map(setting => (
          <Card
            key={setting.id}
            className="cursor-pointer transition-shadow hover:shadow-md h-32 border-gray-200"
            onClick={() => handleNavigate(setting.route)}
            data-testid="settings-card"
          >
            <CardContent className="p-4 flex flex-col gap-4 h-full" data-testid="settings-card-content">
              <div className="flex ">
                <div className="w-full">
                  <h3 className="font-normal mb-2 text-[14px]">{t(setting.title)}</h3>
                  <p className="text-muted-foreground text-[12px]">{t(setting.description)}</p>
                </div>
                <div className="flex items-start justify-end w-50">
                  <img
                    src={setting.image}
                    alt={t(setting.title)}
                    className="w-24 h-20 object-contain"
                    data-testid={`settings-image-${setting.id}`}
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
};

export default SettingsPage;
