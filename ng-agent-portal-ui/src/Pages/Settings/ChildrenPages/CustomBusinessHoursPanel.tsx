import {CommonBreadcrumb} from '@/components/BreadCrumbs/CommonBreadCrumbs';
import {MESSAGES} from '@/constants';
import {useTranslation} from 'react-i18next';
import {useLocation} from 'react-router-dom';
import {useState, useEffect, useCallback, useMemo} from 'react';
import {Sunrise} from 'lucide-react';
import {SlotDuration, type CustomTimeRangeError, type TimeSlotError} from '../components';
import {Input} from '@/components/ui/input';
import moment from 'moment-timezone';
import {useGetDepartmentsQuery} from '@/redux/departments/departmentSlice';
import {
  useCreateBusinessHourMutation,
  useGetBusinessHoursQuery,
  useUpdateBusinessHourMutation,
} from '@/redux/businessHour/businessHour.slice';
import {BusinessHourCategory} from '@/enums/bussiness-hour.enum';
import {useForm, Controller, type FieldErrors} from 'react-hook-form';
import {zodResolver} from '@hookform/resolvers/zod';
import {
  discriminatedBusinessHourSchema,
  type DiscriminatedBusinessHourValues,
  type CustomBusinessHourValues,
  type BusinessHourValues,
  type UnifiedCustomBusinessHourValues,
} from '@/validations/businessHourSchema';
import {useCustomToast, useToast} from '@/hooks/use-toast';
import type {ApiError, ApiResponse} from '@/types/api.type';
import {ToastColor} from '@/types';
import {cn} from '@/lib/utils';
import {FloatingField} from '@/components/FloatingLabel';
import { Button } from '@/components/ui';

const RADIO_BUTTON_COMMON_CLASSES = `
  appearance-none w-5 h-5 rounded-full border-2
  checked:border-[var(--primary-accent)] checked:bg-white
  checked:before:content-[''] checked:before:block
  checked:before:w-2.5 checked:before:h-2.5
  checked:before:rounded-full checked:before:bg-[var(--primary-accent)]
  checked:before:mx-auto checked:before:my-auto
  relative before:absolute before:inset-0
`;

const CustomBusinessHoursPanel = () => {
  interface LocationState {
    id?: string;
    slotName?: string;
    name?: string;
    message?: string;
    timezone?: string;
    isDefault?: boolean;
    department?: {id: string; value: string}[];
    category?: BusinessHourCategory;
    timeRanges?: {from: string; to: string; id: string}[];
    customSlots?: Slot[];
  }
  const location = useLocation();
  const state = location.state as LocationState | undefined;
  const isAddMode = location.pathname.endsWith('/add');
  const {t} = useTranslation();
  const title = isAddMode ? t(MESSAGES.ADD_CUSTOM_HOURS) : t(MESSAGES.EDIT_CUSTOM_HOURS);
  const timezones = moment.tz.names();

  const defaultCategory = state?.category ?? BusinessHourCategory.EVERYDAY;
  const [scheduleType, setScheduleType] = useState(defaultCategory);

  const {data: departments} = useGetDepartmentsQuery();
  const [createBusinessHour] = useCreateBusinessHourMutation();
  const [updateBusinessHour] = useUpdateBusinessHourMutation();
  const {refetch} = useGetBusinessHoursQuery();
  const {toast} = useToast();
  const {customToast} = useCustomToast();

  const [focusStates, setFocusStates] = useState({
    name: false,
    message: false,
  });

  const handleFocus = (field: keyof typeof focusStates) => {
    setFocusStates(prev => ({...prev, [field]: true}));
  };

  const handleBlur = (field: keyof typeof focusStates) => {
    setFocusStates(prev => ({...prev, [field]: false}));
  };

  const shouldFloat = (field: keyof typeof focusStates) => {
    if (field === 'message') {
      return focusStates[field] || !!watch('message');
    }
    if (field === 'name') {
      return focusStates[field] || !!watch('name');
    }
    return focusStates[field];
  };

  // schema and useForm setup must come after scheduleType is set
  const schema = discriminatedBusinessHourSchema(t);

  interface Slot {
    id: string;
    day: string;
    open: string;
    close: string;
  }

  const prepareInitialSlots = (slotsFromServer: Slot[]) => {
    return slotsFromServer.map(slot => ({
      id: slot.id,
      day: slot.day,
      open: slot.open,
      close: slot.close,
    }));
  };

  const initialSlots = useMemo(
    () => (!isAddMode && state?.customSlots ? prepareInitialSlots(state.customSlots) : undefined),
    [isAddMode, state?.customSlots],
  );

  type CustomBusinessHourFormValues = DiscriminatedBusinessHourValues;

  const {
    control,
    setValue,
    register,
    handleSubmit,
    reset,
    watch,
    clearErrors,
    trigger,
    formState: {errors, isValid},
  } = useForm<CustomBusinessHourFormValues>({
    resolver: zodResolver(schema),
    mode: 'onChange', 
    defaultValues:
      scheduleType === BusinessHourCategory.CUSTOM
        ? {
            name: '',
            message: '',
            timezone: moment.tz.guess(),
            department: [],
            customTimeRanges: [],
            category: BusinessHourCategory.CUSTOM,
          }
        : {
            name: '',
            message: '',
            timezone: moment.tz.guess(),
            department: [],
            category: BusinessHourCategory.EVERYDAY,
            timeRanges: [{from: '09:00', to: '17:00', id: Date.now().toString()}],
          },
  });

  useEffect(() => {
    if (isAddMode) {
      const allDays = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
      const defaultTimeSlots = [{from: '09:00', to: '17:00', id: Date.now().toString(), errorMessage: ''}];
      const newDaily = allDays.map(day => ({
        day,
        selected: true,
        timeSlots: defaultTimeSlots.map(slot => ({...slot, id: Date.now().toString() + day})),
      }));
      setValue('customTimeRanges', newDaily);
    } else if (state) {
      const matchedTimezone = moment.tz.names().find(tz => tz.toLowerCase() === (state.timezone ?? '').toLowerCase());
      const detectedCategory = state.category ?? BusinessHourCategory.EVERYDAY;
      setScheduleType(detectedCategory);

      const baseState = {
        name: state.name ?? '',
        message: state.message ?? '',
        timezone: matchedTimezone ?? moment.tz.guess(),
        department: state.department ?? [],
      };

      if (detectedCategory === BusinessHourCategory.CUSTOM) {
        let weekdayPayload: CustomBusinessHourValues = [];
        if (initialSlots) {
          const allDays = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
          weekdayPayload = allDays.map(day => {
            const daySlots = initialSlots
              .filter(slot => slot.day === day)
              .sort((a, b) => a.open.localeCompare(b.open));
            return {
              day,
              selected: daySlots.length > 0,
              timeSlots:
                daySlots.length > 0
                  ? daySlots.map(slot => ({
                      id: slot.id,
                      from: slot.open,
                      to: slot.close,
                      errorMessage: '',
                    }))
                  : [{from: '09:00', to: '17:00', id: Date.now().toString() + day, errorMessage: ''}],
            };
          });
        }
        reset({
          ...baseState,
          category: BusinessHourCategory.CUSTOM,
          customTimeRanges: weekdayPayload,
        });
      } else {
        reset({
          ...baseState,
          category: BusinessHourCategory.EVERYDAY,
          timeRanges:
            state.timeRanges?.sort((a, b) => a.from.localeCompare(b.from)) ?? [
              {from: '09:00', to: '17:00', id: Date.now().toString()},
            ],
        });
      }
    }
  }, [isAddMode, state, reset, initialSlots, setValue]);

  // Updated getBusinessHourPayload as per requirements
  function getBusinessHourPayload(data: CustomBusinessHourFormValues) {
    if (data.category === BusinessHourCategory.CUSTOM) {
      return {
        name: data.name,
        timezone: data.timezone,
        category: BusinessHourCategory.CUSTOM,
        message: data.message,
        departments: data.department.map(dept => dept.id),
        slots: (data.customTimeRanges || [])
          .filter(day => day.selected)
          .map(day => ({
            day: day.day,
            ranges: day.timeSlots.map(slot => ({
              open: slot.from,
              close: slot.to,
            })),
          })),
      };
    }

    return {
      name: data.name,
      timezone: data.timezone,
      category: BusinessHourCategory.EVERYDAY,
      message: data.message,
      departments: data.department.map(dept => dept.id),
      slots: [
        {
          day: 'Everyday',
          ranges: data.timeRanges.map(range => ({
            open: range.from,
            close: range.to,
          })),
        },
      ],
    };
  }

  const onSubmit = async (data: CustomBusinessHourFormValues) => {
    if (isAddMode) {
      try {
        await createBusinessHour({...getBusinessHourPayload(data)}).unwrap();
        void refetch();

        customToast({
          title: '',
          description: t('BUSINESS_HOURS_ADDED_SUCCESSFULLY'),
          anchorOrigin: {vertical: 'top', horizontal: 'center'},
          color: ToastColor.Success,
        });
        window.history.back();
      } catch (error) {
        const message = ((error as ApiResponse<unknown>).data as ApiError)?.message;
        toast({
          title: 'Error',
          description: message ?? t('SOMETHING_WENT_WRONG'),
          color: ToastColor.Error,
        });
      }
    } else {
      try {
        await updateBusinessHour({
          id: state?.id ?? '',
          businessHour: getBusinessHourPayload(data),
        }).unwrap();
        void refetch();

        customToast({
          title: '',
          description: t('CHANGES_SAVED_SUCCESSFULLY'),
          anchorOrigin: {vertical: 'top', horizontal: 'center'},
          color: ToastColor.Success,
        });
        window.history.back();
      } catch (error) {
        const message = ((error as ApiResponse<unknown>).data as ApiError)?.message;
        toast({
          title: 'Error',
          description: message ?? t('SOMETHING_WENT_WRONG'),
          color: ToastColor.Error,
        });
      }
    }
  };

  return (
    <div className="w-full">
      {/* Breadcrumb */}
      <CommonBreadcrumb
        items={[
          {label: t(MESSAGES.SIDEBAR_MENU_SETTINGS), href: '/agent/settings'},
          {label: t(MESSAGES.BUSINESS_HOURS), href: '/agent/settings/business-hours'},
          {label: isAddMode ? t(MESSAGES.ADD) : t(MESSAGES.EDIT)},
        ]}
      />
      <div className="min-h-screen pt-7.5 pb-8 flex justify-center items-start px-4 md:px-10">
        <div className="w-full max-w-337.5 bg-white rounded-xl shadow p-6 space-y-6">
          {/* Title */}
          <div className="h-6 text-left text-base leading-6 font-medium text-black tracking-normal">{title}</div>

          <form onSubmit={e => void handleSubmit(onSubmit)(e)}>
            {/* Timezone */}
            <div className="flex gap-4 w-full">
              {/* Only show Name input if not default */}
              {
                <div className="mb-1">
                  <div className="relative">
                    <div
                      className={cn(
                        'absolute inset-0 pointer-events-none border rounded-md ',
                        shouldFloat('name') ? 'border-primary' : 'border-input',
                        errors.name && 'border-red-500',
                      )}
                    >
                      <span
                        className={cn(
                          'absolute px-1 bg-white text-xs transition-all duration-200 z-10',
                          shouldFloat('name')
                            ? '-top-2 left-3 text-xxs text-primary'
                            : 'top-1/2 left-3 -translate-y-1/2 text-[color:var(--color-placeholders)]',
                          errors.name &&
                            (shouldFloat('name') ? 'text-red-500' : 'text-[color:var(--color-placeholders)]'),
                        )}
                      >
                        {t('NAME')}
                      </span>
                    </div>
                    <Input
                      id="name"
                      {...register('name')}
                      className="w-103 h-11 pl-2.5 pr-10 text-sm leading-5 border border-gray-300 rounded-sm focus-visible:border-gray-300 focus-visible:ring-0 flex items-center"
                      onFocus={() => handleFocus('name')}
                      onBlur={() => handleBlur('name')}
                      data-testid="Name"
                      disabled={state?.isDefault}
                    />
                  </div>
                  {errors.name && typeof errors.name.message === 'string' && (
                    <p className="text-xs text-red-500 mt-1 ml-1">{errors.name.message}</p>
                  )}
                </div>
              }
              <div className="relative">
                <Controller
                  name="timezone"
                  control={control}
                  render={({field}) => (
                    <FloatingField
                      label={t('TIMEZONE')}
                      as="select"
                      value={field.value}
                      onChange={e => {
                        if (
                          (typeof e === 'string' && e.length === 0) ||
                          (typeof e === 'object' && e.target.value.length === 0)
                        ) {
                          return;
                        }

                        if (typeof e === 'string') {
                          field.onChange(e);
                        } else {
                          field.onChange(e.target.value);
                        }
                      }}
                      className="w-103 h-16 px-4 py-5.5 text-sm"
                      disabled={false}
                      options={timezones.map(tz => ({
                        id: tz,
                        label: `(${moment.tz(tz).format('Z')}) ${tz}`,
                        value: tz,
                      }))}
                      data-testid="timezone-select"
                    />
                  )}
                />
                {errors.timezone && typeof errors.timezone.message === 'string' && (
                  <span className="text-red-500 text-xs">{errors.timezone.message}</span>
                )}
                {/* Clock Icon */}
                <span className="absolute right-5.5 top-4 text-gray-400 pointer-events-none bg-white">
                  <Sunrise className="text-gray-600 w-3.5 h-3.5" />
                </span>
              </div>
            </div>

            {/* Radio buttons */}
            <div className="flex space-x-6 mt-4">
              {/* Same Everyday */}
              <label className="flex items-center space-x-2 cursor-pointer">
                <input
                  type="radio"
                  name="schedule"
                  value="EVERYDAY"
                  checked={scheduleType === BusinessHourCategory.EVERYDAY}
                  onChange={() => {
                    setScheduleType(BusinessHourCategory.EVERYDAY);
                    setValue('category', BusinessHourCategory.EVERYDAY);
                    clearErrors('customTimeRanges');
                    void trigger('timeRanges');
                    const existingTimeRanges = watch('timeRanges');
                    if (!existingTimeRanges || existingTimeRanges.length === 0) {
                      setValue('timeRanges', [{from: '09:00', to: '17:00', id: Date.now().toString()}], {
                        shouldValidate: true,
                      });
                    }
                  }}
                  className={`${RADIO_BUTTON_COMMON_CLASSES} border-[var(--primary-accent)]`}
                />
                <span className="text-sm leading-5 font-[Poppins] text-[var(-text-label)]">
                  {t(MESSAGES.SAME_EVERYDAY)}
                </span>
              </label>

              {/* Custom */}
              <label className="flex items-center space-x-2 cursor-pointer">
                <input
                  type="radio"
                  name="schedule"
                  value="CUSTOM"
                  checked={scheduleType === BusinessHourCategory.CUSTOM}
                  onChange={() => {
                    setScheduleType(BusinessHourCategory.CUSTOM);
                    setValue('category', BusinessHourCategory.CUSTOM);
                    clearErrors('timeRanges');
                    void trigger('customTimeRanges');
                    const existingCustomRanges = watch('customTimeRanges');
                    if (!existingCustomRanges || existingCustomRanges.length === 0) {
                      const allDays = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
                      const defaultTimeSlots = [
                        {from: '09:00', to: '17:00', id: Date.now().toString(), errorMessage: ''},
                      ];
                      const newDaily = allDays.map(day => ({
                        day,
                        selected: true,
                        timeSlots: defaultTimeSlots.map(slot => ({...slot, id: Date.now().toString() + day})),
                      }));
                      setValue('customTimeRanges', newDaily, {shouldValidate: true});
                    }
                  }}
                  className={`${RADIO_BUTTON_COMMON_CLASSES} border-[var(--color-vertical-divider)]`}
                />
                <span className="text-sm leading-5 font-[Poppins] text-[var(-text-label)]">{t(MESSAGES.CUSTOM)}</span>
              </label>
            </div>

            {/* Placeholder Text */}
            <p className="text-sm text-gray-400 mt-2.5 mb-5">
              {scheduleType === BusinessHourCategory.EVERYDAY
                ? t('SET_SAME_WORKING_HOURS')
                : t('SET_CUSTOM_WORKING_HOURS')}
            </p>

            <SlotDuration
              showWeekdays={scheduleType !== BusinessHourCategory.EVERYDAY}
              timeRanges={watch('timeRanges') ?? [{from: '09:00', to: '17:00', id: Date.now().toString()}]}
              onTimeRangesChange={useCallback(
                ranges => setValue('timeRanges', ranges, {shouldValidate: true}),
                [setValue],
              )}
              onWeekdayRangesChange={useCallback(
                (weekdayData: CustomBusinessHourValues) => {
                  setValue('customTimeRanges', weekdayData, {shouldValidate: true});
                },
                [setValue],
              )}
              customTimeRanges={watch('customTimeRanges')}
              customTimeRangesErrors={
                scheduleType === BusinessHourCategory.CUSTOM
                  ? ((errors as FieldErrors<UnifiedCustomBusinessHourValues>)
                      .customTimeRanges as unknown as CustomTimeRangeError[])
                  : undefined
              }
              timeRangesErrors={
                scheduleType === BusinessHourCategory.EVERYDAY
                  ? ((errors as FieldErrors<BusinessHourValues>).timeRanges as unknown as TimeSlotError[])
                  : undefined
              }
            />
            <div className="mt-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">{t(MESSAGES.DEPARTMENT)}</label>
              <p className="text-sm text-gray-500 mb-4">{t(MESSAGES.TIME_SLOTS_ASSIGNED_TO_DEPARTMENTS)}</p>
              <div className="relative w-103">
                <Controller
                  name="department"
                  control={control}
                  render={({field}) => (
                    <FloatingField
                      label={t('SELECT_DEPARTMENTS')}
                      as="multi-select"
                      value={field.value} // array of selected options
                      onChange={value => {
                        field.onChange(value);
                      }}
                      options={
                        departments?.data.map(dept => ({
                          id: dept.id,
                          label: dept.name,
                          value: dept.name,
                        })) ?? []
                      }
                      disabled={false}
                      data-testid="department-multiselect"
                    />
                  )}
                />
                {errors.department && typeof errors.department.message === 'string' && (
                  <span className="text-red-500 text-xs">{errors.department.message}</span>
                )}
              </div>
            </div>

            <div className="mt-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">{t(MESSAGES.ADD_MESSAGE)}</label>
              <p className="text-sm text-gray-500 mb-2">{t(MESSAGES.ADD_MESSAGE_DESCRIPTION)}</p>
            </div>

            <div className="relative w-full max-w-124 h-20 mt-5">
              <div
                className={cn(
                  'absolute inset-0 pointer-events-none border rounded-md',
                  shouldFloat('message') ? 'border-primary' : 'border-input',
                  errors.message && 'border-red-500',
                )}
              >
                <span
                  className={cn(
                    'absolute px-1 bg-white text-xs transition-all duration-200 z-10',
                    shouldFloat('message')
                      ? '-top-2 left-3 text-xxs text-primary'
                      : 'top-3 left-3 text-[color:var(--color-placeholders)]',
                    errors.message &&
                      (shouldFloat('message') ? 'text-red-500' : 'text-[color:var(--color-placeholders)]'),
                  )}
                >
                  {t(MESSAGES.ADD_MESSAGE)}
                </span>
              </div>

              <textarea
                id="message"
                rows={4} // Or however many lines you prefer
                {...register('message')}
                // className="w-full resize-none px-3 py-3 text-sm bg-transparent border rounded-md border-gray-300 placeholder:opacity-0 focus:outline-none focus:ring-0 focus:border-transparent"
                className="w-full h-full px-3 pt-4 pb-2 bg-transparent outline-none resize-none border-none"
                onFocus={() => handleFocus('message')}
                onBlur={() => handleBlur('message')}
                data-testid="message"
              />
            </div>

            <div className="flex justify-end space-x-4 pt-2">
              {/* Cancel Button */}
              <button
                type="button"
                className="w-26 h-10 bg-white border border-[#D2D2D2] rounded-md text-[var(-text-label)] text-sm font-normal leading-5 tracking-normal cursor-pointer hover:bg-gray-100"
                onClick={() => {
                  // Navigate back to the business hours page
                  window.history.back();
                }}
              >
                {t(MESSAGES.CANCEL)}
              </button>

              {/* Add Button */}
              <Button
                type="submit"
                disabled={!isValid}
                className="w-26 h-10 bg-[var(--primary-accent)] rounded-md text-white text-sm font-normal leading-5 tracking-normal cursor-pointer hover:bg-[var(--primary-hover)]]"
              >
                {isAddMode ? t(MESSAGES.ADD) : t(MESSAGES.SAVE)}
              </Button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default CustomBusinessHoursPanel;
