import {Card} from '@/components/ui/card';
import {RadioGroup, RadioGroupItem} from '@/components/ui/radio-group';
import {Label} from '@/components/ui/label';
import {Input} from '@/components/ui/input';
import {CommonBreadcrumb} from '@/components/BreadCrumbs/CommonBreadCrumbs';
import {useTranslation} from 'react-i18next';
import {MESSAGES} from '@/constants';
import {Info} from 'lucide-react';

const RoutingRules: React.FC = () => {
  const {t} = useTranslation();
  return (
    <div className="min-h-screen">
      <div className="pr-4 pb-0">
        <CommonBreadcrumb
          items={[
            {label: t(MESSAGES.SIDEBAR_MENU_SETTINGS), href: '/agent/settings'},
            {label: t(MESSAGES.ROUTING_RULES)},
          ]}
          data-testid="breadcrumb"
        />
      </div>
      <div className="mt-7.5 md:ml-27.5 w-245">
        <h2 className="text-base font-medium leading-6.5 text-black mb-4 w-245">{t(MESSAGES.SET_UP_ROUTING_RULES)}</h2>
        <p className="text-sm text-muted-foreground">{t(MESSAGES.SET_UP_ROUTING_RULES_DESCRIPTION)}</p>

        <div className="pt-8">
          <RadioGroup defaultValue="simultaneous" className="space-y-4" data-testid="radio-group">
            {/* Simultaneous Routing */}
            <Card className="p-4 border-0 shadow-l h-96 w-5xl" data-testid="card">
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="simultaneous" id="simultaneous" />

                <Label htmlFor="simultaneous" className="text-neutral-600 font-regular">
                  {t(MESSAGES.SIMULTANEOUS_ROUTING)}
                </Label>
              </div>
              <div className="pt-2.5">
                <p className="text-sm text-muted-foreground">{t(MESSAGES.SIMULTANEOUS_ROUTING_DESCRIPTION)}</p>
              </div>
              <div className="flex items-center gap-2 pt-4">
                <p className="text-sm text-neutral-600">{t(MESSAGES.REQUEST_TIMEOUT)}</p>
                <Info className="h-4 w-4 text-[color:var(--color-icons)]" data-testid="info-icon" />
              </div>
              <div className="flex items-center gap-2 pt-2">
                <Input id="timeout" type="number" min={10} max={300} defaultValue={60} className="w-26 h-9 text-sm" />
                <span className="text-sm text-neutral-600">{t(MESSAGES.SECONDS)}</span>
              </div>
            </Card>

            {/* Department-based Routing */}
            <Card className="p-4 border-0 shadow-l h-96 w-5xl" data-testid="card">
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="department" id="department" />
                <Label htmlFor="department" className="text-neutral-600 font-regular">
                  {t(MESSAGES.DEPARTMENT_BASED_ROUTING)}
                </Label>
              </div>
            </Card>
          </RadioGroup>
        </div>
      </div>
    </div>
  );
};

export default RoutingRules;
