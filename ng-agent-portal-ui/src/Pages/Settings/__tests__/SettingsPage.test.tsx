import {describe, it, expect, vi, beforeEach} from 'vitest';
import {render, screen, fireEvent} from '@testing-library/react';
import SettingsPage from '@/Pages/Settings/Settings';
import {RouteConstant} from '@/constants';

// Mock assets and constants

vi.mock('@/assets/settingsPage/BusinessHours.svg', () => ({
  default: () => <svg data-testid="business-hours-image" />,
}));

vi.mock('@/assets/settingsPage/RoutingRules.svg', () => ({
  default: () => <svg data-testid="routing-rules-image" />,
}));

vi.mock('@/assets/settingsPage/ChatInteraction.svg', () => ({
  default: () => <svg data-testid="chat-interaction-image" />,
}));

vi.mock('@/assets/settingsPage/LlmConfig.svg', () => ({
  default: () => <svg data-testid="llm-config-image" />,
}));

vi.mock('@/assets/settingsPage/CannedResponses.svg', () => ({
  default: () => <svg data-testid="canned-responses-image" />,
}));

vi.mock('@/constants', () => ({
  RouteConstant: {
    SETTINGS_ROUTING_RULES: '/settings/routing-rules',
    SETTINGS_BUSINESS_HOURS: '/settings/business-hours',
    SETTINGS_CHAT_INTERACTION_CONTROLS: '/settings/chat-interaction-controls',
    SETTINGS_LLM_CONFIGURATION: '/settings/llm-configuration',
    SETTINGS_CANNED_RESPONSES: '/settings/canned-responses',
  },
}));

// Mock Card and CardContent components
vi.mock('@/components/ui/card', () => ({
  Card: ({children, ...props}: any) => (
    <div data-testid="settings-card" {...props}>
      {children}
    </div>
  ),
  CardContent: ({children, ...props}: any) => (
    <div data-testid="settings-card-content" {...props}>
      {children}
    </div>
  ),
}));

// Mock useNavigate
const mockNavigate = vi.fn();
vi.mock('react-router-dom', async importOriginal => {
  const actual = await importOriginal<any>();
  return {
    ...actual,
    useNavigate: () => mockNavigate,
  };
});

// Mock useTranslation
const mockT = vi.fn((key: string) => key);
vi.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: mockT,
  }),
}));

describe('SettingsPage', () => {
  beforeEach(() => {
    mockNavigate.mockClear();
    mockT.mockClear();
  });

  it('renders the settings page title', () => {
    render(<SettingsPage />);
    expect(screen.getByText('SETTINGS')).toBeInTheDocument();
  });

  it('renders all setting cards with correct titles and descriptions', () => {
    render(<SettingsPage />);
    // There are 5 settings
    expect(screen.getAllByTestId('settings-card')).toHaveLength(5);

    // Check for each setting's title and description
    expect(screen.getByText('SETTINGS_ROUTING_TITLE')).toBeInTheDocument();
    expect(screen.getByText('SETTINGS_ROUTING_DESCRIPTION')).toBeInTheDocument();
    expect(screen.getByText('SETTINGS_BUSINESS_HOURS_TITLE')).toBeInTheDocument();
    expect(screen.getByText('SETTINGS_BUSINESS_HOURS_DESCRIPTION')).toBeInTheDocument();
    expect(screen.getByText('SETTINGS_INTERACTIONS_TITLE')).toBeInTheDocument();
    expect(screen.getByText('SETTINGS_INTERACTIONS_DESCRIPTION')).toBeInTheDocument();
    expect(screen.getByText('SETTINGS_LLM_TITLE')).toBeInTheDocument();
    expect(screen.getByText('SETTINGS_LLM_DESCRIPTION')).toBeInTheDocument();
    expect(screen.getByText('SETTINGS_CANNED_RESPONSES_TITLE')).toBeInTheDocument();
    expect(screen.getByText('SETTINGS_CANNED_RESPONSES_DESCRIPTION')).toBeInTheDocument();
  });

  it('renders images for each setting', () => {
    render(<SettingsPage />);
    expect(screen.getByTestId('settings-image-business-hours')).toBeInTheDocument();
    expect(screen.getByTestId('settings-image-routing-rules')).toBeInTheDocument();
    expect(screen.getByTestId('settings-image-chat-interaction-controls')).toBeInTheDocument();
    expect(screen.getByTestId('settings-image-llm-configuration')).toBeInTheDocument();
    expect(screen.getByTestId('settings-image-canned-responses')).toBeInTheDocument();
  });

  it('navigates to the correct route when a card is clicked', () => {
    render(<SettingsPage />);
    const cards = screen.getAllByTestId('settings-card');

    fireEvent.click(cards[0]);
    expect(mockNavigate).toHaveBeenCalledWith(RouteConstant.SETTINGS_ROUTING_RULES);

    fireEvent.click(cards[1]);
    expect(mockNavigate).toHaveBeenCalledWith(RouteConstant.BUSINESS_HOURS);

    fireEvent.click(cards[2]);
    expect(mockNavigate).toHaveBeenCalledWith(RouteConstant.SETTINGS_CHAT_INTERACTION_CONTROLS);

    fireEvent.click(cards[3]);
    expect(mockNavigate).toHaveBeenCalledWith(RouteConstant.SETTINGS_LLM_CONFIGURATION);

    fireEvent.click(cards[4]);
    expect(mockNavigate).toHaveBeenCalledWith(RouteConstant.SETTINGS_CANNED_RESPONSES);
  });
});
