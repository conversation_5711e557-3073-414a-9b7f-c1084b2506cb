import {render, screen, fireEvent} from '@testing-library/react';
import {<PERSON>rowserRouter} from 'react-router-dom';
import {ConfiguredSlots} from '../components/ConfiguredSlots';
import {RouteConstant} from '@/constants';
import {vi, describe, beforeEach, test, expect} from 'vitest';
import {Provider} from 'react-redux';
import {store} from '@/redux/store';

import {BusinessHourCategory} from '@/enums/bussiness-hour.enum';
import type {IBusinessHour} from '@/types/businessHour.type';

// Mock the useNavigate hook
const mockedUseNavigate = vi.fn();
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useNavigate: () => mockedUseNavigate,
  };
});

describe('ConfiguredSlots Component', () => {
  beforeEach(() => {
    // Clear all mocks before each test
    mockedUseNavigate.mockClear();
  });

  test('navigates to edit business hours page on pencil icon click', () => {
    const businessHour: IBusinessHour = {
      id: '1',
      name: 'Test Slot',
      category: BusinessHourCategory.EVERYDAY, // or BusinessHourCategory.EVERYDAY if it's an enum
      timezone: 'UTC',
      isDefault: false,
      slots: [],
      departments: [],
      message: '',
    };

    render(
      <Provider store={store}>
        <BrowserRouter>
          <ConfiguredSlots businessHours={[businessHour]} refetch={vi.fn()} />
        </BrowserRouter>
      </Provider>,
    );
    const pencilIcon = screen.getAllByTestId('pencil-icon')[0];
    fireEvent.click(pencilIcon);

    expect(mockedUseNavigate).toHaveBeenCalledTimes(1);
    expect(mockedUseNavigate).toHaveBeenCalledWith(RouteConstant.EDIT_BUSINESS_HOURS, expect.any(Object));
  });
});
