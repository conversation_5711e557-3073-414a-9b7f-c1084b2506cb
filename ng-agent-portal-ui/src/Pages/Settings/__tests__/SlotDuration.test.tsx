import {render, screen, fireEvent} from '@testing-library/react';
import {SlotDuration} from '../components/SlotDuration';
import {TimeSlotRow} from '../components/TimeSlotRow';
import {WeekdayTimeSlot} from '../components/WeekdayTimeSlot';
import {vi, describe, test, expect, beforeEach} from 'vitest';

// Mock child components
vi.mock('../components/TimeSlotRow', () => ({
  TimeSlotRow: vi.fn(({onAddRow, onRemoveRow, slotIndex, totalSlots, canAdd, canRemove}) => (
    <div data-testid={`time-slot-row-${slotIndex}`}>
      Time Slot Row {slotIndex}
      {canAdd && slotIndex === totalSlots - 1 && (
        <button onClick={onAddRow} data-testid={`add-row-button-${slotIndex}`}>
          Add
        </button>
      )}
      {canRemove && (
        <button onClick={onRemoveRow} data-testid={`remove-row-button-${slotIndex}`}>
          Remove
        </button>
      )}
    </div>
  )),
}));

vi.mock('../components/WeekdayTimeSlot', () => ({
  WeekdayTimeSlot: vi.fn(({dayData, dayIndex, handleAddRow, handleRemoveRow}) => (
    <div data-testid={`weekday-time-slot-${dayData.day}`}>
      {dayData.day}
      <button onClick={() => handleAddRow(dayIndex)} data-testid={`add-weekday-row-button-${dayIndex}`}>
        Add Weekday
      </button>
      <button onClick={() => handleRemoveRow(0, dayIndex)} data-testid={`remove-weekday-row-button-${dayIndex}`}>
        Remove Weekday
      </button>
    </div>
  )),
}));

// ---- REQUIRED DEFAULT PROPS ----
const defaultTimeRanges = [{from: '09:00', to: '17:00', id: '1', errorMessage: ''}];
const defaultCustomTimeRanges = [
  'Monday',
  'Tuesday',
  'Wednesday',
  'Thursday',
  'Friday',
  'Saturday',
  'Sunday',
].map(day => ({
  day,
  selected: true,
  timeSlots: [{from: '09:00', to: '17:00', id: `1-${day}`, errorMessage: ''}],
}));
const defaultOnTimeRangesChange = vi.fn();
const defaultOnWeekdayRangesChange = vi.fn();

describe('SlotDuration Component', () => {
  beforeEach(() => {
    (TimeSlotRow as any).mockClear();
    (WeekdayTimeSlot as any).mockClear();
    defaultOnTimeRangesChange.mockClear();
    defaultOnWeekdayRangesChange.mockClear();
  });

  test('renders TimeSlotRow when showWeekdays is false (default)', () => {
    render(<SlotDuration timeRanges={defaultTimeRanges} onTimeRangesChange={defaultOnTimeRangesChange} />);
    expect(TimeSlotRow).toHaveBeenCalledTimes(1);
    expect(WeekdayTimeSlot).not.toHaveBeenCalled();
    expect(screen.getByTestId('time-slot-row-0')).toBeInTheDocument();
  });

  test('renders WeekdayTimeSlot when showWeekdays is true', () => {
    render(
      <SlotDuration
        showWeekdays={true}
        timeRanges={[]}
        onTimeRangesChange={defaultOnTimeRangesChange}
        customTimeRanges={defaultCustomTimeRanges}
        onWeekdayRangesChange={defaultOnWeekdayRangesChange}
      />,
    );
    expect(WeekdayTimeSlot).toHaveBeenCalledTimes(7); // For each day of the week
    expect(TimeSlotRow).not.toHaveBeenCalled();
    expect(screen.getByTestId('weekday-time-slot-Monday')).toBeInTheDocument();
    expect(screen.getByTestId('weekday-time-slot-Sunday')).toBeInTheDocument();
  });

  test('does not add more than 3 time slot rows (non-weekday mode)', () => {
    render(<SlotDuration timeRanges={defaultTimeRanges} onTimeRangesChange={defaultOnTimeRangesChange} />);
    fireEvent.click(screen.getByTestId('add-row-button-0')); // Add 2nd slot
    expect(screen.queryByTestId('time-slot-row-3')).not.toBeInTheDocument();
  });

  test('removes a time slot row when "Remove" button is clicked (non-weekday mode)', () => {
    render(
      <SlotDuration
        timeRanges={[
          {from: '09:00', to: '12:00', id: '1', errorMessage: ''},
          {from: '13:00', to: '17:00', id: '2', errorMessage: ''},
        ]}
        onTimeRangesChange={defaultOnTimeRangesChange}
      />,
    );
    fireEvent.click(screen.getByTestId('remove-row-button-0')); // Remove the first slot
    expect(screen.getByTestId('time-slot-row-0')).toBeInTheDocument();
  });

  test('does not remove the last time slot row (non-weekday mode)', () => {
    render(<SlotDuration timeRanges={defaultTimeRanges} onTimeRangesChange={defaultOnTimeRangesChange} />);
    expect(screen.queryByTestId('remove-row-button-0')).not.toBeInTheDocument();
    expect(screen.getByTestId('time-slot-row-0')).toBeInTheDocument();
  });

  test('adds a new time slot for a specific weekday when "Add Weekday" button is clicked', () => {
    render(
      <SlotDuration
        showWeekdays={true}
        timeRanges={[]}
        onTimeRangesChange={defaultOnTimeRangesChange}
        customTimeRanges={defaultCustomTimeRanges}
        onWeekdayRangesChange={defaultOnWeekdayRangesChange}
      />,
    );
    fireEvent.click(screen.getByTestId('add-weekday-row-button-0')); // Add slot for Monday
    expect(defaultOnWeekdayRangesChange).toHaveBeenCalled();
  });

  test('removes a time slot for a specific weekday when "Remove Weekday" button is clicked', () => {
    const customTimeRangesWithTwoSlots = JSON.parse(JSON.stringify(defaultCustomTimeRanges));
    customTimeRangesWithTwoSlots[0].timeSlots.push({
      from: '18:00',
      to: '20:00',
      id: '2-Monday',
      errorMessage: '',
    });

    render(
      <SlotDuration
        showWeekdays={true}
        timeRanges={[]}
        onTimeRangesChange={defaultOnTimeRangesChange}
        customTimeRanges={customTimeRangesWithTwoSlots}
        onWeekdayRangesChange={defaultOnWeekdayRangesChange}
      />,
    );
    fireEvent.click(screen.getByTestId('remove-weekday-row-button-0')); // Remove a slot for Monday
    expect(defaultOnWeekdayRangesChange).toHaveBeenCalled();
  });
});
