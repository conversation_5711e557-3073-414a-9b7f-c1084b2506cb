import {render, screen, fireEvent} from '@testing-library/react';
import {WeekdayTimeSlot} from '../components/WeekdayTimeSlot';
import {TimeSlotRow} from '../components/TimeSlotRow';

import {vi, describe, test, expect, beforeEach, type Mock} from 'vitest';

// Mock the TimeSlotRow component
vi.mock('../components/TimeSlotRow', () => ({
  TimeSlotRow: vi.fn(({slot, slotIndex, onAddRow, onRemoveRow}) => (
    <div data-testid={`mock-time-slot-row-${slotIndex}`}>
      <span>
        {slot.from} - {slot.to}
      </span>
      <button onClick={onAddRow} data-testid={`add-button-${slotIndex}`}>
        Add
      </button>
      <button onClick={onRemoveRow} data-testid={`remove-button-${slotIndex}`}>
        Remove
      </button>
    </div>
  )),
}));

describe('WeekdayTimeSlot Component', () => {
  const mockHandleAddRow = vi.fn();
  const mockHandleRemoveRow = vi.fn();

  const defaultDayData = {
    day: 'Monday',
    selected: true,
    timeSlots: [{from: '09:00 AM', to: '05:00 PM', id: '1', errorMessage: ''}],
  };

  beforeEach(() => {
    mockHandleAddRow.mockClear();
    mockHandleRemoveRow.mockClear();
    (TimeSlotRow as Mock).mockClear();
  });

  test('renders the weekday and a checkbox', () => {
    render(
      <WeekdayTimeSlot
        dayData={defaultDayData}
        dayIndex={0}
        handleAddRow={mockHandleAddRow}
        handleRemoveRow={mockHandleRemoveRow}
        handleTimeChange={() => {}}
        handleDayToggle={() => {}}
      />,
    );

    expect(screen.getByText('Monday')).toBeInTheDocument();
    expect(screen.getByRole('checkbox')).toBeInTheDocument();
  });

  test('renders TimeSlotRow for each time slot', () => {
    const dayDataWithMultipleSlots = {
      day: 'Tuesday',
      selected: true,
      timeSlots: [
        {from: '08:00 AM', to: '12:00 PM', id: '1', errorMessage: ''},
        {from: '01:00 PM', to: '05:00 PM', id: '2', errorMessage: ''},
      ],
    };

    render(
      <WeekdayTimeSlot
        dayData={dayDataWithMultipleSlots}
        dayIndex={1}
        handleAddRow={mockHandleAddRow}
        handleRemoveRow={mockHandleRemoveRow}
        handleTimeChange={() => {}}
        handleDayToggle={() => {}}
      />,
    );

    expect(TimeSlotRow).toHaveBeenCalledTimes(2);
    expect(screen.getByTestId('mock-time-slot-row-0')).toBeInTheDocument();
    expect(screen.getByTestId('mock-time-slot-row-1')).toBeInTheDocument();
  });

  test('passes correct props to TimeSlotRow for adding a row', () => {
    render(
      <WeekdayTimeSlot
        dayData={defaultDayData}
        dayIndex={0}
        handleAddRow={mockHandleAddRow}
        handleRemoveRow={mockHandleRemoveRow}
        handleTimeChange={() => {}}
        handleDayToggle={() => {}}
      />,
    );

    const addButton = screen.getByTestId('add-button-0');
    fireEvent.click(addButton);

    expect(mockHandleAddRow).toHaveBeenCalledTimes(1);
    expect(mockHandleAddRow).toHaveBeenCalledWith(0); // dayIndex should be passed
  });

  test('passes correct props to TimeSlotRow for removing a row', () => {
    render(
      <WeekdayTimeSlot
        dayData={defaultDayData}
        dayIndex={0}
        handleAddRow={mockHandleAddRow}
        handleRemoveRow={mockHandleRemoveRow}
        handleTimeChange={() => {}}
        handleDayToggle={() => {}}
      />,
    );

    const removeButton = screen.getByTestId('remove-button-0');
    fireEvent.click(removeButton);

    expect(mockHandleRemoveRow).toHaveBeenCalledTimes(1);
    expect(mockHandleRemoveRow).toHaveBeenCalledWith(0, 0); // slotIndex and dayIndex should be passed
  });

  test('TimeSlotRow receives canAdd and canRemove props correctly', () => {
    const dayDataSingleSlot = {
      day: 'Wednesday',
      selected: true,
      timeSlots: [{from: '09:00 AM', to: '05:00 PM', id: '1', errorMessage: ''}],
    };
    render(
      <WeekdayTimeSlot
        dayData={dayDataSingleSlot}
        dayIndex={0}
        handleAddRow={mockHandleAddRow}
        handleRemoveRow={mockHandleRemoveRow}
        handleTimeChange={() => {}}
        handleDayToggle={() => {}}
      />,
    );

    expect(TimeSlotRow).toHaveBeenCalledWith(
      expect.objectContaining({
        canAdd: true, // Only one slot, so can add
        canRemove: false, // Only one slot, so cannot remove
        onAddRow: expect.any(Function),
        onRemoveRow: expect.any(Function),
        slot: expect.any(Object),
        slotIndex: expect.any(Number),
        totalSlots: expect.any(Number),
      }),
      undefined, // Expect the second argument to be undefined
    );

    const dayDataMultipleSlots = {
      day: 'Thursday',
      selected: true,
      timeSlots: [
        {from: '08:00 AM', to: '12:00 PM', id: '1', errorMessage: ''},
        {from: '01:00 PM', to: '05:00 PM', id: '2', errorMessage: ''},
      ],
    };
    render(
      <WeekdayTimeSlot
        dayData={dayDataMultipleSlots}
        dayIndex={0}
        handleAddRow={mockHandleAddRow}
        handleRemoveRow={mockHandleRemoveRow}
        handleTimeChange={() => {}}
        handleDayToggle={() => {}}
      />,
    );

    expect(TimeSlotRow).toHaveBeenCalledWith(
      expect.objectContaining({
        canAdd: true, // Two slots, can add one more
        canRemove: true, // Two slots, can remove
        onAddRow: expect.any(Function),
        onRemoveRow: expect.any(Function),
        slot: expect.any(Object),
        slotIndex: expect.any(Number),
        totalSlots: expect.any(Number),
      }),
      undefined, // Expect the second argument to be undefined
    );

    const dayDataMaxSlots = {
      day: 'Friday',
      selected: true,
      timeSlots: [
        {from: '08:00 AM', to: '10:00 AM', id: '1', errorMessage: ''},
        {from: '10:00 AM', to: '12:00 PM', id: '2', errorMessage: ''},
        {from: '01:00 PM', to: '03:00 PM', id: '3', errorMessage: ''},
      ],
    };
    render(
      <WeekdayTimeSlot
        dayData={dayDataMaxSlots}
        dayIndex={0}
        handleAddRow={mockHandleAddRow}
        handleRemoveRow={mockHandleRemoveRow}
        handleTimeChange={() => {}}
        handleDayToggle={() => {}}
      />,
    );

    expect(TimeSlotRow).toHaveBeenCalledWith(
      expect.objectContaining({
        canAdd: false, // Three slots, cannot add more
        canRemove: true, // Three slots, can remove
        onAddRow: expect.any(Function),
        onRemoveRow: expect.any(Function),
        slot: expect.any(Object),
        slotIndex: expect.any(Number),
        totalSlots: expect.any(Number),
      }),
      undefined, // Expect the second argument to be undefined
    );
  });
});
