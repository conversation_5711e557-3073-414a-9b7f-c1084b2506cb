import {TimeSlotRow} from './TimeSlotRow';
import {WeekdayTimeSlot} from './WeekdayTimeSlot';
import { useTranslation } from 'react-i18next';

export interface TimeSlotError {
  from?: {message?: string};
  to?: {message?: string};
}

export interface CustomTimeRangeError {
  timeSlots?: TimeSlotError[];
}

export const SlotDuration = ({
  showWeekdays = false,
  timeRanges,
  onTimeRangesChange,
  onWeekdayRangesChange,
  customTimeRanges,
  customTimeRangesErrors,
  timeRangesErrors,
}: {
  showWeekdays?: boolean;
  timeRanges: {from: string; to: string; id: string; errorMessage?: string}[];
  onTimeRangesChange: (ranges: {from: string; to: string; id: string; errorMessage?: string}[]) => void;
  onWeekdayRangesChange?: (
    weekdayData: {
      day: string;
      selected: boolean;
      timeSlots: {from: string; to: string; id?: string; errorMessage?: string}[];
    }[],
  ) => void;
  customTimeRanges?: {
    day: string;
    selected: boolean;
    timeSlots: {from: string; to: string; id?: string; errorMessage?: string}[];
  }[];
  customTimeRangesErrors?: CustomTimeRangeError[];
  timeRangesErrors?: TimeSlotError[];
}) => {
  const {t} = useTranslation();

  function addOneHour(time: string) {
    const [h, m] = time.split(':').map(Number);
    const newH = h + 1;
    return `${newH.toString().padStart(2, '0')}:${m.toString().padStart(2, '0')}`;
  }

  const handleAddRow = (dayIndex?: number) => {
    if (showWeekdays && dayIndex !== undefined && onWeekdayRangesChange) {
      const newDailyRanges = [...(customTimeRanges ?? [])];
      const dayData = newDailyRanges[dayIndex];
      if (dayData.timeSlots.length < 3) {
        const lastSlot = dayData.timeSlots[dayData.timeSlots.length - 1];
        const newFrom = lastSlot.to;
        const newTo = addOneHour(newFrom);
        const newId = Date.now().toString();
        dayData.timeSlots.push({from: newFrom, to: newTo, id: newId, errorMessage: ''});
        onWeekdayRangesChange(newDailyRanges);
      }
    } else if (!showWeekdays && timeRanges.length < 3) {
      const lastSlot = timeRanges[timeRanges.length - 1];
      const newFrom = lastSlot.to;
      const newTo = addOneHour(newFrom);
      const newId = Date.now().toString();
      onTimeRangesChange([...timeRanges, {from: newFrom, to: newTo, id: newId, errorMessage: ''}]);
    }
  };

  const handleRemoveRow = (indexToRemove: number, dayIndex?: number) => {
    if (showWeekdays && dayIndex !== undefined && onWeekdayRangesChange) {
      const newDailyRanges = [...(customTimeRanges ?? [])];
      const dayData = newDailyRanges[dayIndex];
      if (dayData.timeSlots.length > 1) {
        dayData.timeSlots.splice(indexToRemove, 1);
        onWeekdayRangesChange(newDailyRanges);
      }
    } else if (!showWeekdays && timeRanges.length > 1) {
      onTimeRangesChange(timeRanges.filter((_, i) => i !== indexToRemove));
    }
  };

  const handleTimeChange = (index: number, field: 'from' | 'to', value: string, dayIndex?: number) => {
    if (showWeekdays && dayIndex !== undefined && onWeekdayRangesChange) {
      const newDailyRanges = [...(customTimeRanges ?? [])];
      const dayData = newDailyRanges[dayIndex];
      const newTimeSlots = [...dayData.timeSlots];
      newTimeSlots[index] = {...newTimeSlots[index], [field]: value};
      newDailyRanges[dayIndex] = {...dayData, timeSlots: newTimeSlots};
      onWeekdayRangesChange(newDailyRanges);
    } else {
      const newTimeRanges = [...timeRanges];
      newTimeRanges[index] = {...newTimeRanges[index], [field]: value};
      onTimeRangesChange(newTimeRanges);
    }
  };

  let alertShown = false;
  const handleDayToggle = (index: number) => {
    if (onWeekdayRangesChange) {
      const newDailyRanges = [...(customTimeRanges ?? [])];
      const currentlySelected = newDailyRanges.filter(d => d.selected);
      if (newDailyRanges[index].selected && currentlySelected.length === 1) {
        if (!alertShown) {
          alert(t('PLEASE_SELECT_AT_LEAST_ONE_DAY'));
          alertShown = true;
          setTimeout(() => {
            alertShown = false;
          }, 1000); // reset after 1s
        }
        return;
      }
      newDailyRanges[index] = {...newDailyRanges[index], selected: !newDailyRanges[index].selected};
      onWeekdayRangesChange(newDailyRanges);
    }
  };

  return (
    <div
      className={`space-y-4 px-3 py-6 border border-gray-200 rounded-2xl ${showWeekdays ? 'w-[700px]' : 'w-124'} flex flex-col justify-center h-auto`}
    >
      {showWeekdays
        ? customTimeRanges?.map((dayData, dayIndex) => (
            <WeekdayTimeSlot
              key={dayData.day}
              dayData={dayData}
              dayIndex={dayIndex}
              handleAddRow={handleAddRow}
              handleRemoveRow={handleRemoveRow}
              handleTimeChange={handleTimeChange}
              handleDayToggle={handleDayToggle}
              timeSlotErrors={customTimeRangesErrors?.[dayIndex]?.timeSlots}
            />
          ))
        : timeRanges?.map((timeRange, i) => (
            <div key={timeRange.id} className="space-y-1">
              <TimeSlotRow
                key={timeRange.id}
                slot={timeRange}
                slotIndex={i}
                totalSlots={timeRanges.length}
                onAddRow={() => handleAddRow()}
                onRemoveRow={() => handleRemoveRow(i)}
                onTimeChange={handleTimeChange}
                canAdd={timeRanges.length < 3}
                canRemove={timeRanges.length > 1}
                errorMessage={timeRangesErrors?.[i]?.from?.message ?? timeRangesErrors?.[i]?.to?.message}
              />
            </div>
          ))}
    </div>
  );
};
