import {SquarePlus, Trash2} from 'lucide-react';
import TimePicker from '@/components/TimePicker';

interface TimeSlotRowProps {
  slot: {from: string; to: string; id?: string};
  slotIndex: number;
  totalSlots: number;
  onAddRow: () => void;
  onRemoveRow: () => void;
  onTimeChange: (slotIndex: number, field: 'from' | 'to', value: string) => void;
  canAdd: boolean;
  canRemove: boolean;
  errorMessage?: string;
}

export const TimeSlotRow = ({
  slot,
  slotIndex,
  totalSlots,
  onAddRow,
  onRemoveRow,
  onTimeChange,
  canAdd,
  canRemove,
  errorMessage,
}: TimeSlotRowProps) => {

  return (
    <>
      <div className="flex items-center justify-between pl-2">
        <div className="flex items-center space-x-2">
          <TimePicker
            value={slot.from}
            format="12"
            onChange={value => {
              onTimeChange(slotIndex, 'from', value);
            }}
          />

          <span className="text-[18px] text-gray-700 px-1">-</span>

          <TimePicker
            value={slot.to}
            format="12"
            onChange={value => {
              onTimeChange(slotIndex, 'to', value);
            }}
          />
        </div>

        <div className="w-8 h-8.5 flex items-center justify-center">
          {canAdd && slotIndex === totalSlots - 1 ? (
            <SquarePlus data-testid="SquarePlusIcon" className="w-5 h-5 text-[var(--primary)] cursor-pointer" onClick={onAddRow} />
          ) : (
            <Trash2
              data-testid="Trash2Icon"
              className={`w-5 h-5 text-gray-500 ${canRemove ? 'cursor-pointer' : 'opacity-50 cursor-not-allowed'}`}
              onClick={() => canRemove && onRemoveRow()}
            />
          )}
        </div>
      </div>

      {errorMessage && (
        <p className="text-xs text-red-500 mt-1 ml-1 text-left">{errorMessage}</p>
      )}
    </>
  );
};
