import {useState} from 'react';
import {getTimings} from './ConfiguredSlots';
import type { Slot } from '@/types/businessHour.type';
import type { BusinessHourCategory } from '@/enums/bussiness-hour.enum';

const MAX_PREVIEW_LENGTH = 80; // or whatever fits your design

interface BusinessHourTimingProps {
  category: BusinessHourCategory;
  slots: Slot[];
  timezone: string;
}

function BusinessHourTiming({category, slots, timezone}: Readonly<BusinessHourTimingProps>) {
  const [expanded, setExpanded] = useState(false);
  const fullTiming = getTimings(category, slots, timezone);

  // Determine if "VIEW MORE" is needed
  const needsViewMore = fullTiming.length > MAX_PREVIEW_LENGTH;

  const preview = needsViewMore && !expanded ? fullTiming.slice(0, MAX_PREVIEW_LENGTH) + '...' : fullTiming;

  return (
    <span>
      {preview}
      {needsViewMore && !expanded && (
        <button
          type="button"
          style={{color: '#496FDB', marginLeft: 8, background: 'none', border: 'none', padding: 0, cursor: 'pointer'}}
          onClick={() => setExpanded(true)}
        >
          VIEW MORE
        </button>
      )}
      {needsViewMore && expanded && (
        <button
          type="button"
          style={{color: '#496FDB', marginLeft: 8, background: 'none', border: 'none', padding: 0, cursor: 'pointer'}}
          onClick={() => setExpanded(false)}
        >
          VIEW LESS
        </button>
      )}
    </span>
  );
}

export default BusinessHourTiming;
