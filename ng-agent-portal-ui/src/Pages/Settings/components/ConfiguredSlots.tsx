import {useEffect, useState} from 'react';
import {Card} from '@/components/ui/card';
import {Pencil, Trash2} from 'lucide-react';
import {useNavigate} from 'react-router-dom';
import {RouteConstant} from '@/constants';
import type {IBusinessHour, Slot} from '@/types/businessHour.type';
import {formatTime} from '@/utils/formatTime/formatTime';
import {useDeleteBusinessHourMutation} from '@/redux/businessHour/businessHour.slice';
import {BusinessHourCategory} from '@/enums/bussiness-hour.enum';

import {GenericConfirmDialog} from '@/components/GenericConfirmDialog';
import {ToastColor} from '@/types';
import {useToast} from '@/hooks';
import type {ApiError, ApiResponse} from '@/types/api.type';
import BusinessHourTiming from './BusinessHourTiming';
import {useTranslation} from 'react-i18next';
import moment from 'moment-timezone';
import SuccessModal from '@/components/modals/SuccessModal';
interface ConfiguredSlotsProps {
  businessHours: IBusinessHour[];
  refetch: () => void;
}

const DAYS_ORDER = ['Everyday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];

function groupAndFormatSlots(slots: Slot[]) {
  const grouped: Record<string, {open: string; close: string; openRaw: string}[]> = {};

  for (const slot of slots) {
    grouped[slot.day] ??= [];
    grouped[slot.day].push({
      open: formatTime(slot.open),
      close: formatTime(slot.close),
      openRaw: slot.open,
    });
  }

  // Sort time ranges within each day
  Object.keys(grouped).forEach(day => {
    grouped[day].sort((a, b) => a.openRaw.localeCompare(b.openRaw));
  });

  // Create a new grouped object with days in the desired order
  const orderedGrouped: typeof grouped = {};
  for (const day of DAYS_ORDER) {
    if (grouped[day]) {
      orderedGrouped[day] = grouped[day];
    }
  }

  return orderedGrouped;
}

export function getTimings(category: BusinessHourCategory, slots: Slot[], timezone: string): string {
  const abbreviation = moment.tz(timezone).format('z');

  switch (category) {
    case BusinessHourCategory.EVERYDAY: {
      const firstSlot = slots.reduce(
        (earliest, slot) => (!earliest || slot.open.localeCompare(earliest.open) < 0 ? slot : earliest),
        undefined as (typeof slots)[0] | undefined,
      );

      const dayLabel = firstSlot?.day ?? 'Day';
      const timeRanges = [...slots]
        .sort((a, b) => a.open.localeCompare(b.open))
        .map(slot => `${formatTime(slot.open)} to ${formatTime(slot.close)}`);
      return `${dayLabel} – ${timeRanges.join(', ')} (${abbreviation})`;
    }
    case BusinessHourCategory.CUSTOM: {
      const groupedSlots = groupAndFormatSlots(slots);
      return (
        Object.entries(groupedSlots)
          .map(([day, slots], index) => {
            const ranges = slots.map(slot => `${formatTime(slot.open)} to ${formatTime(slot.close)}`).join(', ');
            return `${index > 0 ? ' | ' : ''}${day} – ${ranges}`;
          })
          .join('') + ` (${abbreviation})`
      );
    }
    default:
      return '';
  }
}

function getSortedTimeRanges(slots: Slot[] | undefined) {
  if (!slots) return [];

  const toMinutes = (time: string) => {
    const [h, m] = time.split(':').map(Number);
    return h * 60 + m;
  };

  return slots
    .map((s, index) => ({
      from: s.open,
      to: s.close,
      id: `${index}`,
    }))
    .sort((a, b) => toMinutes(a.from) - toMinutes(b.from) || toMinutes(a.to) - toMinutes(b.to));
}

export const ConfiguredSlots = ({businessHours, refetch}: ConfiguredSlotsProps) => {
  const navigate = useNavigate();

  const {t} = useTranslation();

  const [isModalOpen, setIsModalOpen] = useState(false);
  const [deletedBusinessHour, setDeletedBusinessHour] = useState<IBusinessHour | null>(null);

  const [slots, setSlots] = useState(businessHours);
  const [showSuccessMessage, setShowSuccessMessage] = useState(false);

  const {toast} = useToast();

  useEffect(() => {
    setSlots(businessHours);
  }, [businessHours]);

  const [deleteBusinessHour] = useDeleteBusinessHourMutation();

  const handleDelete = async (id: string) => {
    try {
      await deleteBusinessHour({id}).unwrap();
      refetch(); // Refetch the list after deletion
      setShowSuccessMessage(true)
    } catch (error) {
      const message = ((error as ApiResponse<unknown>).data as ApiError)?.message;
      toast({
        title: 'Error',
        description: message ?? t('FAILED_TO_DELETE_BUSINESS_HOUR'),
        color: ToastColor.Error,
      });
    }
  };

  function cn(...classes: (string | false | null | undefined)[]): string {
    return classes.filter(Boolean).join(' ');
  }
  return (
    <>
      {slots.map(slot => (
        <Card key={slot.id} className="p-4 pr-6 relative w-245 border-[var(--color-border-input)] mb-4">
          <div className="flex flex-col text-sm leading-5 space-y-1">
            <h4 className="font-medium text-[var(--text-label)]">{slot.name}</h4>
            <p className="font-normal text-[var(--text-label)] pr-30">
              <span className="text-[var(--color-muted-text)]">Timing: </span>
              <BusinessHourTiming category={slot.category} slots={slot.slots || []} timezone={slot.timezone} />
            </p>
            <p className="font-normal text-[var(--color-tab-text-default)]">
              <span className="text-[var(--color-muted-text)]">Departments: </span>
              {slots.length > 1
                ? slot.departments?.length > 1
                  ? slot.departments.map(dep => dep.name).join(', ')
                  : slot.departments?.[0]?.name || ''
                : t('All')}
            </p>
          </div>
          <div className={cn('absolute top-5 right-5 flex space-x-3')}>
            <Pencil
              data-testid="pencil-icon"
              className="h-3.5 w-3.5 text-[var(--color-tab-text-default)] cursor-pointer"
              onClick={() => {
                const timeRanges =
                  slot.category === BusinessHourCategory.EVERYDAY ? getSortedTimeRanges(slot.slots) : undefined;
                void navigate(RouteConstant.EDIT_BUSINESS_HOURS, {
                  state: {
                    id: slot.id,
                    name: slot.name,
                    message: slot.message,
                    timezone: slot.timezone,
                    isDefault: slot.isDefault,
                    category: slot.category,
                    department:
                      slot.departments?.map(dep => ({
                        id: dep.id,
                        value: dep.name,
                      })) ?? [],
                    timeRanges: timeRanges,
                    customSlots:
                      slot.slots?.map(s => ({
                        day: s.day,
                        open: s.open,
                        close: s.close,
                      })) ?? [],
                  },
                });
              }}
            />
            <Trash2
              data-testid="trash2-icon"
              className={cn(
                'h-3.5 w-3.5',
                slot.isDefault === true
                  ? 'text-gray-300 cursor-not-allowed'
                  : 'text-[var(--color-tab-text-default)] cursor-pointer',
              )}
              onClick={() => {
                if (slot.isDefault) return;
                setIsModalOpen(true);
                setDeletedBusinessHour(slot);
              }}
              aria-disabled={slot.id === 'default'}
              tabIndex={slot.id === 'default' ? -1 : 0}
            />
          </div>
        </Card>
      ))}
      <GenericConfirmDialog
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        headerTitle={t('CONFIRM_DELETE')}
        confirmButtonText={t('YES_DELETE')}
        cancelButtonText={t('NO_CANCEL')}
        showTextArea={false}
        mainContentHeadline={t('CONFIRM_SLOT_DELETION')}
        onConfirm={() => {
          setIsModalOpen(false);
          void handleDelete(deletedBusinessHour?.id ?? '');
        }}
      />

      {showSuccessMessage && (
        <SuccessModal
          isOpen={showSuccessMessage}
          onClose={() => setShowSuccessMessage(false)}
          message={`${deletedBusinessHour?.name} ${t('BUSINESS_HOUR_DELETED')}`}
        />
      )}
    </>
  );
};
