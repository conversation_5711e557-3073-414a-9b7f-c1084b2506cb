'use client';

import {useMemo, useState} from 'react';
import {Button} from '@/components/ui/button';
import {Pencil, Trash2} from 'lucide-react';
import {GenericConfirmDialog} from '@/components/GenericConfirmDialog';
import {ToastColor, type IDepartment} from '@/types';
import {useDeleteDepartmentMutation} from '@/redux/departments/departmentSlice'; // adjust path if needed
import {useAuth, useToast} from '@/hooks';
import {useTranslation} from 'react-i18next';
import type {ApiError, ApiResponse} from '@/types/api.type';
import {MESSAGES} from '@/constants';
import {PermissionsEnum} from '@/enums';

interface Props {
  department: IDepartment;
  onEdit: (department: IDepartment) => void;
  refetch: () => void;
}

export const DepartmentActionsCell = ({department, onEdit, refetch}: Props) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [deleteDepartment, {isLoading}] = useDeleteDepartmentMutation();
  const {toast} = useToast();
  const {t} = useTranslation();
  const {user} = useAuth();

  const canEdit = useMemo(() => user?.permissions.includes(PermissionsEnum.UPDATE_AGENT), [user]);
  const canDelete = useMemo(() => user?.permissions.includes(PermissionsEnum.DELETE_AGENT), [user]);

  const handleConfirmDelete = async (_remark: string, data?: IDepartment) => {
    if (!data) return;
    try {
      await deleteDepartment({id: data.id}).unwrap();
      refetch();
    } catch (error) {
      console.error('Failed to delete department:', error);
      const message = ((error as ApiResponse<unknown>).data as ApiError)?.message;
      toast({
        title: 'Error',
        description: message ?? t('DEPARTMENT_DELETION_FAILED'),
        color: ToastColor.Error,
      });
    } finally {
      setIsModalOpen(false);
      toast({
        title: 'Success',
        description: t('DEPARTMENT_DELETION_SUCCESS'),
        color: ToastColor.Success,
      });
    }
  };

  return (
    <>
      <div className="flex items-center justify-center gap-0 h-full">
        {canEdit && (
          <Button variant="ghost" size="icon" aria-label="Edit" onClick={() => onEdit(department)}>
            <Pencil className="h-2 w-2 text-gray-500" />
          </Button>
        )}
        {canDelete && (
          <Button
            variant="ghost"
            size="icon"
            aria-label="Delete"
            onClick={() => setIsModalOpen(true)}
            disabled={isLoading}
          >
            <Trash2 className="h-2 w-2 text-gray-500" />
          </Button>
        )}
      </div>

      <GenericConfirmDialog
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        headerTitle="DELETE_DEPARTMENT"
        confirmButtonText="DELETE"
        cancelButtonText="CANCEL"
        showTextArea={false}
        mainContentHeadline={MESSAGES.DEPARTMENT_DELETE_CONFIRMATION}
        onConfirm={(remark, data) => void handleConfirmDelete(remark, data)}
        data={department}
      />
    </>
  );
};
