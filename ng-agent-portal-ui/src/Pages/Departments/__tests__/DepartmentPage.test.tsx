/* eslint-disable @typescript-eslint/no-explicit-any */
import {describe, it, expect, vi, beforeEach} from 'vitest';
import {screen, waitFor} from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import DepartmentPage from '../DepartmentPage';
import {useGetDepartmentsQuery, useCreateDepartmentMutation} from '../../../redux/departments/departmentSlice';
import {renderWithStore} from '@/testUtils/test-utils';

const mockDeleteDepartment = vi.fn();
// Mock the redux hooks
vi.mock('../../../redux/departments/departmentSlice', () => ({
  useGetDepartmentsQuery: vi.fn(),
  useCreateDepartmentMutation: vi.fn(),
  useUpdateDepartmentMutation: () => [vi.fn().mockResolvedValue({}), {isLoading: false}],
  useDeleteDepartmentMutation: () => [mockDeleteDepartment, {isLoading: false}],
}));

// Mock the toast hook
vi.mock('../../../hooks/use-toast', () => ({
  useToast: () => ({
    toast: vi.fn(),
  }),
}));

describe('DepartmentPage', () => {
  const mockDepartments = {
    data: [
      {
        id: '1',
        name: 'HR',
        isDefault: false,
        createdAt: '2023-01-01T00:00:00Z',
        updatedAt: '2023-01-01T00:00:00Z',
        deletedAt: null,
        agents: [
          {name: 'Olivia Martinez', email: '<EMAIL>'},
          {name: 'Noah Thompson', email: '<EMAIL>'},
        ],
      },
      {
        id: '2',
        name: 'IT',
        isDefault: false,
        createdAt: '2023-01-02T00:00:00Z',
        updatedAt: '2023-01-02T00:00:00Z',
        deletedAt: null,
        agents: [
          {name: 'Olivia Martinez', email: '<EMAIL>'},
          {name: 'Noah Thompson', email: '<EMAIL>'},
        ],
      },
    ],
    meta: {
      totalItems: 2,
      itemCount: 2,
      itemsPerPage: 10,
      totalPages: 1,
      currentPage: 1,
    },
  };

  const mockCreateDepartment = vi.fn();
  const mockRefetch = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
    // Setup default mocks
    (useGetDepartmentsQuery as any).mockReturnValue({
      data: mockDepartments,
      isLoading: false,
      refetch: mockRefetch,
    });

    (useCreateDepartmentMutation as any).mockReturnValue([mockCreateDepartment, {isLoading: false}]);
  });

  it('renders the department page with data table', () => {
    renderWithStore(<DepartmentPage />);

    // Check if the data table is rendered
    expect(screen.getByTestId('add-button')).toBeInTheDocument();

    // Check if departments are displayed
    expect(screen.getByText('HR')).toBeInTheDocument();
    expect(screen.getByText('IT')).toBeInTheDocument();
  });

  it('filters departments when searching', async () => {
    const user = userEvent.setup();
    renderWithStore(<DepartmentPage />);

    // Find the search input
    const searchInput = screen.getByTestId('Search');

    // Type in the search input
    await user.type(searchInput, 'HR');

    // Check if the filter options are updated
    await waitFor(() => {
      expect(useGetDepartmentsQuery).toHaveBeenCalled();
    });
  });

  it('opens the department form modal when add button is clicked', async () => {
    const user = userEvent.setup();
    renderWithStore(<DepartmentPage />);

    // Click the add department button
    const addButton = screen.getByTestId('add-button');
    await user.click(addButton);

    // Check if the modal is opened
    expect(addButton).toBeInTheDocument();
    expect(screen.getByTestId('Name')).toBeInTheDocument();
  });

  it('creates a new department when form is submitted', async () => {
    const user = userEvent.setup();
    mockCreateDepartment.mockResolvedValue({id: '3', name: 'Finance'});

    renderWithStore(<DepartmentPage />);

    // Open the modal
    const addButton = screen.getByTestId('add-button');
    await user.click(addButton);

    // Fill the form
    const nameInput = screen.getByTestId('Name');
    await user.type(nameInput, 'Finance');

    // Submit the form
    const saveButton = screen.getByText('ADD');
    await user.click(saveButton);

    // Check if createDepartment was called with correct data
    await waitFor(() => {
      expect(mockCreateDepartment).toHaveBeenCalledWith({name: 'Finance'});
    });
  });

  it('shows error when department creation fails', async () => {
    const user = userEvent.setup();
    mockCreateDepartment.mockRejectedValue(new Error('Failed to create department'));

    renderWithStore(<DepartmentPage />);

    // Open the modal
    const addButton = screen.getByTestId('add-button');
    await user.click(addButton);

    // Fill the form
    const nameInput = screen.getByTestId('Name');
    await user.type(nameInput, 'Finance');

    // Submit the form
    const saveButton = screen.getByText('ADD');
    await user.click(saveButton);

    // Check if error is shown
  });

  it('displays custom no results message when no departments are found', () => {
    // Mock empty departments response
    (useGetDepartmentsQuery as any).mockReturnValue({
      data: {data: []},
      isLoading: false,
      refetch: mockRefetch,
    });

    renderWithStore(<DepartmentPage />);

    // Check if custom no results message is displayed
    expect(screen.getByText('NO_SEARCH_MATCHED')).toBeInTheDocument();
    expect(screen.queryByText('NO_RESULTS')).not.toBeInTheDocument();
  });

  it('displays departments when data is available', () => {
    renderWithStore(<DepartmentPage />);

    // Check if departments are displayed and no results message is not shown
    expect(screen.getByText('HR')).toBeInTheDocument();
    expect(screen.getByText('IT')).toBeInTheDocument();
    expect(screen.queryByText('NO_SEARCH_MATCHED')).not.toBeInTheDocument();
    expect(screen.queryByText('NO_RESULTS')).not.toBeInTheDocument();
  });

  it('shows custom no results message when search returns no results', () => {
    // Mock empty search results from the start
    (useGetDepartmentsQuery as any).mockReturnValue({
      data: {data: []},
      isLoading: false,
      refetch: mockRefetch,
    });

    renderWithStore(<DepartmentPage />);

    // Check if custom no results message is displayed
    expect(screen.getAllByText('NO_SEARCH_MATCHED')[0]).toBeInTheDocument();
  });
});
