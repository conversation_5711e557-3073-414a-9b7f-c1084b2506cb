import {describe, it, expect, vi} from 'vitest';
import {departmentColumns} from '../DepartmentColumns';
import type {TFunction} from 'i18next';
import {mockUser} from '@/constants/test.constant';

describe('departmentColumns', () => {
  const mockT: (key: string) => string = key => key;
  const mockOnEdit = vi.fn();
  const mockRefetch = vi.fn();
  it('should define the correct columns', () => {
    const columns = departmentColumns(mockT as TFunction<'translation', undefined>, mockOnEdit, mockRefetch, mockUser);

    expect(columns).toHaveLength(4);

    expect(columns[0].id).toBe('name');
    expect(columns[0].header).toBe('NAME');

    expect(columns[1].id).toBe('agents');
    expect(columns[1].header).toBe('DEPARTMENT_AGENTS');
    expect(columns[1].cell).toBeDefined();

    expect(columns[2].id).toBe('createdAt');
    expect(columns[2].header).toBe('CREATED_ON');
    expect(columns[2].cell).toBeDefined();

    expect(columns[3].id).toBe('actions');
    expect(columns[3].header).toBe('ACTIONS');
    expect(columns[3].cell).toBeDefined();
  });

  it('should format createdAt date correctly', () => {
    const columns = departmentColumns(mockT as TFunction<'translation', undefined>, mockOnEdit, mockRefetch, mockUser);

    const createdAtColumn = columns[2];
    const cellFunction = createdAtColumn.cell;

    if (cellFunction && typeof cellFunction === 'function') {
      // Correctly mock the row object
      const mockRow = {
        getValue: () => '2023-05-15T10:30:00Z',
      };

      // Pass mock row to the cell function
      const result = cellFunction({row: mockRow} as any);

      // Check if the date is formatted correctly
      expect(result).toBe('15/05/2023');
    } else {
      expect(true).toBe(true);
    }
  });
});
