'use client';

import type {ColumnDef} from '@tanstack/react-table';
import {DepartmentActionsCell} from './DepartmentActionCell';
import type {IDepartment, IUserWithPermissions} from '../../types';
import {formatDate} from '@/utils';
import {DepartmentAgents} from './DepartmentAgents';
import type {TFunction} from 'i18next';
import {PermissionsEnum} from '@/enums';

export const departmentColumns = (
  t: TFunction<'translation', undefined>,
  onEdit: (department: IDepartment) => void,
  refetch: () => void,
  user: IUserWithPermissions | null,
): ColumnDef<IDepartment>[] => {
  const canEdit = user?.permissions.includes(PermissionsEnum.UPDATE_DEPARTMENT);
  const canDelete = user?.permissions.includes(PermissionsEnum.DELETE_DEPARTMENT);

  const columns: ColumnDef<IDepartment>[] = [
    {
      accessorKey: 'name',
      header: t('NAME'),
      id: 'name',
    },
    {
      accessorKey: 'agents',
      header: t('DEPARTMENT_AGENTS'),
      id: 'agents',
      cell: ({row}) => {
        return <DepartmentAgents agents={row.getValue('agents') ?? []} />;
      },
    },
    {
      accessorKey: 'createdAt',
      id: 'createdAt',
      header: t('CREATED_ON'),
      cell: ({row}) => formatDate(row.getValue('createdAt')),
    },
  ];

  if (canEdit || canDelete) {
    columns.push({
      id: 'actions',
      header: t('ACTIONS'),
      cell: ({row}) => <DepartmentActionsCell department={row.original} onEdit={onEdit} refetch={refetch} />,
    });
  }

  return columns;
};
