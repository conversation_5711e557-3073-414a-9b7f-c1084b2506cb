import {DataTable} from '../../components/data-table';
import {
  useCreateDepartmentMutation,
  useGetDepartmentsQuery,
  useUpdateDepartmentMutation,
  type DepartmentQueryOptions,
} from '@/redux/departments/departmentSlice';
import {departmentColumns} from './DepartmentColumns';
import {useState, useMemo} from 'react';
import type {SortItem} from '../../types/filter.types';
import {FilterOperator} from '../../types/filter.types';
import type {DepartmentFormValues} from '../../validations/departmentSchema';
import {DepartmentForm} from './DepartmentForm';
import SuccessModal from '../../components/modals/SuccessModal';
import {MESSAGES} from '../../constants';
import {useTranslation} from 'react-i18next';
import {ToastColor, type IDepartment} from '@/types';
import {useAuth, useDebounce, useToast} from '@/hooks';
import type {ApiError, ApiResponse} from '@/types/api.type';

const DEBOUNCE_DELAY = 300;

const DepartmentPage = () => {
  const {t} = useTranslation();
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
  const [successModalOpen, setSuccessModalOpen] = useState<boolean>(false);
  const [selectedDepartment, setSelectedDepartment] = useState<IDepartment | null>(null);

  const [createDepartment, {isLoading: isCreating}] = useCreateDepartmentMutation();

  const debouncedQuery = useDebounce(searchQuery, DEBOUNCE_DELAY);
  const {toast} = useToast();
  const {user} = useAuth();

  // Create filter options based on search query
  const filterOptions = useMemo<DepartmentQueryOptions>(() => {
    const order = [['createdAt', 'DESC']] as SortItem[];

    if (!debouncedQuery)
      return {
        order,
        includeAgents: true,
      };
    return {
      where: {
        name: {
          [FilterOperator.LIKE]: `%${debouncedQuery}%`,
        },
      },
      order,
      includeAgents: true,
    };
  }, [debouncedQuery]);

  const {data: departments, refetch, isLoading: isFetchingDepartments} = useGetDepartmentsQuery(filterOptions);
  const [updateDepartment, {isLoading: isUpdating}] = useUpdateDepartmentMutation();

  const handleSearch = (value: string) => {
    setSearchQuery(value);
  };

  const handleAddDepartment = () => {
    setSelectedDepartment(null);
    setIsModalOpen(true);
  };

  const handleEditDepartment = (department: IDepartment) => {
    setSelectedDepartment(department);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
  };

  const handleSubmitDepartment = async (values: DepartmentFormValues) => {
    try {
      if (selectedDepartment) {
        // Editing
        await updateDepartment({id: selectedDepartment.id, data: {name: values.name}}).unwrap();
        toast({
          title: t('SUCCESS'),
          description: t(MESSAGES.DEPARTMENT_UPDATED),
          color: ToastColor.Success,
        });
      } else {
        // Creating
        await createDepartment({name: values.name}).unwrap();
        setSuccessModalOpen(true);
      }
      // eslint-disable-next-line @typescript-eslint/no-floating-promises
      refetch();
      handleCloseModal();
    } catch (error) {
      const message = ((error as ApiResponse<unknown>).data as ApiError)?.message;
      toast({
        title: 'Error',
        description: message ?? t('DEPARTMENT_CREATION_FAILED'),
        color: ToastColor.Error,
      });
    }
  };

  return (
    <div className="w-full">
      <DataTable
        // eslint-disable-next-line @typescript-eslint/no-misused-promises
        columns={departmentColumns(t, handleEditDepartment, refetch, user)}
        data={departments?.data ?? []}
        buttonLabel={t('ADD_DEPARTMENT')}
        searchPlaceholder={t('SEARCH')}
        onSearch={handleSearch}
        onButtonClick={handleAddDepartment}
        isLoading={isFetchingDepartments}
        noResultsLabel={t('NO_SEARCH_MATCHED')}
      />
      <DepartmentForm
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        onSubmit={values => void handleSubmitDepartment(values)}
        isLoading={isCreating || isUpdating}
        defaultValues={selectedDepartment ? {name: selectedDepartment.name} : undefined}
      />
      <SuccessModal
        isOpen={successModalOpen}
        onClose={() => {
          setSuccessModalOpen(false);
        }}
        message={t(MESSAGES.DEPARTMENT_CREATED)}
      />
    </div>
  );
};

export default DepartmentPage;
