import {render, screen, fireEvent} from '@testing-library/react';
import {describe, test, expect, vi, beforeEach, afterEach} from 'vitest';
import {InboxSearch} from '../components/InboxSearch';
import {MESSAGES} from '@/constants/messages.constant';
import {MockApiTabContentDb} from '../mockData/mock-chat-content';

// Mock Input component
vi.mock('@/components/ui/input', () => ({
  Input: ({
    placeholder,
    className,
    value,
    onChange,
  }: {
    placeholder: string;
    className: string;
    value: string;
    onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  }) => (
    <input data-testid="mock-input" placeholder={placeholder} className={className} value={value} onChange={onChange} />
  ),
}));

// Mock Search icon from lucide-react
vi.mock('lucide-react', () => ({
  Search: ({className}: {className: string}) => <svg data-testid="mock-search-icon" className={className} />,
}));

// Mock useDebounce hook
vi.mock('@/hooks/useDebounce', () => ({
  useDebounce: (value: any) => value, // Immediately return the value for testing
}));

describe('InboxSearch', () => {
  const mockOnSearchResults = vi.fn();
  const activeTab = 'All Chats';

  beforeEach(() => {
    vi.useFakeTimers();
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.useRealTimers();
  });

  test('renders the search input with correct placeholder', () => {
    render(<InboxSearch onSearchResults={mockOnSearchResults} activeTab={activeTab} />);
    const inputElement = screen.getByPlaceholderText(MESSAGES.INBOX_SEARCH_PLACEHOLDER);
    expect(inputElement).toBeInTheDocument();
    expect(inputElement).toHaveAttribute('data-testid', 'mock-input');
  });

  test('renders the Search icon', () => {
    render(<InboxSearch onSearchResults={mockOnSearchResults} activeTab={activeTab} />);
    expect(screen.getByTestId('mock-search-icon')).toBeInTheDocument();
  });

  test('renders the main container div', () => {
    const {container} = render(<InboxSearch onSearchResults={mockOnSearchResults} activeTab={activeTab} />);
    // Check for the outermost div with specific classes
    expect(container.firstChild).toHaveClass('w-full h-[46px] border-b border-border');
  });

  test('calls onSearchResults with debounced value after delay', async () => {
    render(<InboxSearch onSearchResults={mockOnSearchResults} activeTab={activeTab} />);
    const inputElement = screen.getByTestId('mock-input');

    // Initial call with empty search term
    vi.advanceTimersByTime(300);
    // Determine the expected initial result based on mock data
    const initialChatList = MockApiTabContentDb.find(res => res.tabName === activeTab)?.chatList;
    const expectedInitialResult = initialChatList === undefined ? null : initialChatList; // Explicitly handle undefined to null
    expect(mockOnSearchResults).toHaveBeenCalledWith(expectedInitialResult);
    mockOnSearchResults.mockClear(); // Clear initial call

    fireEvent.change(inputElement, {target: {value: 'test'}});

    vi.advanceTimersByTime(300); // Advance time by debounce delay

    // Expect onSearchResults to be called with the filtered results for 'test'
    const filteredChatList = MockApiTabContentDb.find(res => res.tabName === activeTab)?.chatList.filter(chat =>
      chat.recepientName.toLowerCase().includes('test'),
    );
    const expectedFilteredResult = filteredChatList === undefined ? null : filteredChatList; // Explicitly handle undefined to null
    expect(mockOnSearchResults).toHaveBeenCalledWith(expectedFilteredResult);
  });

  test('calls onSearchResults with all chats when search term is empty', async () => {
    render(<InboxSearch onSearchResults={mockOnSearchResults} activeTab={activeTab} />);
    const inputElement = screen.getByTestId('mock-input');

    // Initial call with empty search term
    vi.advanceTimersByTime(300);
    mockOnSearchResults.mockClear(); // Clear initial call

    fireEvent.change(inputElement, {target: {value: 'some_value'}});
    vi.advanceTimersByTime(300);
    mockOnSearchResults.mockClear(); // Clear previous calls

    fireEvent.change(inputElement, {target: {value: ''}});
    vi.advanceTimersByTime(300);

    const allChats = MockApiTabContentDb.find(res => res.tabName === activeTab)?.chatList ?? null;
    expect(mockOnSearchResults).toHaveBeenCalledWith(allChats);
  });

  test('updates search term state on input change', () => {
    render(<InboxSearch onSearchResults={mockOnSearchResults} activeTab={activeTab} />);
    const inputElement = screen.getByTestId('mock-input') as HTMLInputElement;

    fireEvent.change(inputElement, {target: {value: 'new search'}});
    expect(inputElement.value).toBe('new search');
  });
});
