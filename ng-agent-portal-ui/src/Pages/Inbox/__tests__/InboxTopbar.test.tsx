import {render, screen} from '@testing-library/react';
import {describe, test, expect, vi, beforeEach} from 'vitest';
import {InboxTopbar} from '../components/InboxTopbar';
import {InboxTabsEnum} from '@/enums/inbox-tabs.enum';
import {badgeColorMap} from '../constants/badge-classification.contant';
import type {InboxTopbarProps} from '../types/inbox-component-props';

vi.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => key,
    i18n: {
      dir: () => 'ltr', // Mock the dir function
    },
  }),
}));

// Mock the Badge component
vi.mock('@/components/ui/badge', () => ({
  Badge: ({children, className}: {children: React.ReactNode; className: string}) => (
    <span data-testid="mock-badge" className={className}>
      {children}
    </span>
  ),
}));

// Mock the TabsList and TabsTrigger components
vi.mock('@/components/ui/tab', async () => {
  const actual = await vi.importActual('@/components/ui/tab');
  return {
    ...actual,
    TabsList: ({children, className}: {children: React.ReactNode; className: string}) => (
      <div data-testid="mock-tabs-list" className={className}>
        {children}
      </div>
    ),
    TabsTrigger: ({value, children, className}: {value: string; children: React.ReactNode; className: string}) => (
      <button data-testid={`mock-tab-trigger-${value}`} className={className}>
        {children}
      </button>
    ),
  };
});

const mockTabsData: InboxTopbarProps['tabsData'] = [
  {tabId: 1, tabName: InboxTabsEnum.ACTIVE, chatCount: 5},
  {tabId: 2, tabName: InboxTabsEnum.QUEUED, chatCount: 0},
  {tabId: 3, tabName: InboxTabsEnum.ARCHIVED, chatCount: 10},
];

describe('InboxTopbar', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  test('renders TabsList component', () => {
    render(<InboxTopbar tabsData={mockTabsData} />);
    expect(screen.getByTestId('mock-tabs-list')).toBeInTheDocument();
  });

  test('renders correct number of TabsTrigger components', () => {
    render(<InboxTopbar tabsData={mockTabsData} />);
    const tabTriggers = screen.getAllByTestId(/mock-tab-trigger-/);
    expect(tabTriggers).toHaveLength(mockTabsData.length);
  });

  test('renders tab names correctly', () => {
    render(<InboxTopbar tabsData={mockTabsData} />);
    mockTabsData.forEach(tab => {
      expect(screen.getByText(tab.tabName)).toBeInTheDocument();
    });
  });

  test('renders Badge with chat count when chatCount > 0', () => {
    render(<InboxTopbar tabsData={mockTabsData} />);

    // Active tab with chatCount > 0
    const activeTabTrigger = screen.getByTestId(`mock-tab-trigger-${InboxTabsEnum.ACTIVE}`);
    expect(activeTabTrigger).toHaveTextContent('5');
    const activeBadge = activeTabTrigger.querySelector('[data-testid="mock-badge"]');
    expect(activeBadge).toBeInTheDocument();
    expect(activeBadge).toHaveTextContent('5');
    expect(activeBadge).toHaveClass(badgeColorMap[InboxTabsEnum.ACTIVE]);

    // Archived tab with chatCount > 0
    const archivedTabTrigger = screen.getByTestId(`mock-tab-trigger-${InboxTabsEnum.ARCHIVED}`);
    expect(archivedTabTrigger).toHaveTextContent('10');
    const archivedBadge = archivedTabTrigger.querySelector('[data-testid="mock-badge"]');
    expect(archivedBadge).toBeInTheDocument();
    expect(archivedBadge).toHaveTextContent('10');
    expect(archivedBadge).toHaveClass(badgeColorMap[InboxTabsEnum.ARCHIVED]);
  });

  test('handles empty tabsData gracefully', () => {
    render(<InboxTopbar tabsData={[]} />);
    expect(screen.getByTestId('mock-tabs-list')).toBeInTheDocument();
    expect(screen.queryAllByTestId(/mock-tab-trigger-/)).toHaveLength(0);
  });
});
