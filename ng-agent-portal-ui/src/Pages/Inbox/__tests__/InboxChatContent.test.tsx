import {screen, fireEvent} from '@testing-library/react';
import {describe, test, expect, vi, beforeEach} from 'vitest';
import {InboxChatContent} from '../components/InboxChatContent';
import {InboxChatSideProfile} from '../components/InboxChatSideProfile';
import {InboxTabsEnum} from '@/enums'; // Import InboxTabsEnum
import {renderWithStore} from '@/testUtils/test-utils';

vi.mock('react-i18next', async () => {
  const actual = await vi.importActual<any>('react-i18next');
  return {
    ...actual,
    useTranslation: () => ({
      t: (key: string) => key,
      i18n: {
        changeLanguage: () => Promise.resolve(),
        dir: () => 'ltr',
      },
    }),
    initReactI18next: {
      type: '3rdParty',
      init: () => {},
    },
  };
});

vi.mock('@/modules/rich-text-editor', () => ({
  RichTextEditor: ({onChange, content, placeholder}: any) => (
    <textarea
      data-testid="mock-rte"
      value={content}
      placeholder={placeholder}
      onChange={e => onChange(e.target.value)}
    />
  ),
}));

vi.mock('@/components/RichTextEditor', () => ({
  default: ({onChange, value}: {onChange: (val: string) => void; value: string}) => (
    <div>
      <textarea data-testid="mock-rte" value={value} onChange={e => onChange(e.target.value)} />
    </div>
  ),
}));
vi.mock('../mockData/mock-chat-content', () => ({
  messagesData: [
    {id: 1, text: 'Hello!', sender: 'left', time: '12:30 PM'},
    {id: 2, text: 'Hello! How can I assist you today?', sender: 'right', time: '12:35 PM'},
  ],
}));

// Mock child components and assets
vi.mock('@/components/ui/button', () => ({
  Button: ({children, variant, className}: {children: React.ReactNode; variant: string; className: string}) => (
    <button data-testid="mock-button" className={`${variant} ${className}`}>
      {children}
    </button>
  ),
}));

vi.mock('@/components/ui/avatar', () => ({
  Avatar: ({children, className}: {children: React.ReactNode; className: string}) => (
    <div data-testid="mock-avatar" className={className}>
      {children}
    </div>
  ),
  AvatarFallback: ({children, className}: {children: React.ReactNode; className: string}) => (
    <span data-testid="mock-avatar-fallback" className={className}>
      {children}
    </span>
  ),
}));

vi.mock('lucide-react', async () => {
  const actual = await vi.importActual('lucide-react');
  return {
    ...actual,
    Video: () => <svg data-testid="mock-video-icon" />,
    EllipsisVertical: () => <svg data-testid="mock-more-vertical-icon" />,
    PhoneCall: () => <svg data-testid="mock-phone-call-icon" />,
    ScreenShare: () => <svg data-testid="mock-screen-share-icon" />,
    TypeOutline: () => <svg data-testid="mock-type-outline-icon" />,
    Smile: () => <svg data-testid="mock-smile-icon" />,
    Link: () => <svg data-testid="mock-link-icon" />,
    Sparkles: () => <svg data-testid="mock-sparkles-icon" />,
    FilePlus2: () => <svg data-testid="mock-file-plus2-icon" />,
    Send: () => <svg data-testid="mock-send-icon" />,
  };
});

vi.mock('../../../assets/inbox-panel/send-message.svg', () => ({
  default: 'send-message.svg',
}));

// Mock InboxChatSideProfile and spy on its default export
vi.mock('../components/InboxChatSideProfile', () => ({
  InboxChatSideProfile: vi.fn(() => <div data-testid="mock-inbox-chat-side-profile" />),
}));

// Mock InboxChatEmojiModal to make it testable
vi.mock('../components/InboxChatEmojiModal', () => ({
  default: ({show, onClose, onEmojiClick}: any) =>
    show ? (
      <div data-testid="mock-emoji-modal">
        <button data-testid="mock-emoji" onClick={() => onEmojiClick({emoji: '😀'}, {})}>
          😀
        </button>
        <button data-testid="close-emoji-modal" onClick={onClose}>
          Close
        </button>
      </div>
    ) : null,
}));

describe('InboxChatContent', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  const mockChatContent = {
    tabId: 1,
    tabName: InboxTabsEnum.ACTIVE,
    chatList: [], // Provide a minimal chatList for the mock
  };

  test('renders chat header elements', () => {
    renderWithStore(<InboxChatContent chatContent={mockChatContent} />);
    expect(screen.getByText('Dave Grohl')).toBeInTheDocument();
    expect(screen.getByText('Amazon Ecom')).toBeInTheDocument();
    expect(screen.getByTestId('mock-avatar')).toBeInTheDocument();
    expect(screen.getByTestId('mock-avatar-fallback')).toHaveTextContent('DG');
    expect(screen.getByTestId('mock-phone-call-icon')).toBeInTheDocument();
    expect(screen.getByTestId('mock-video-icon')).toBeInTheDocument();
    expect(screen.getByTestId('mock-screen-share-icon')).toBeInTheDocument();
    expect(screen.getByRole('button', {name: 'INBOX_END_CHAT_BUTTON'})).toBeInTheDocument();
    expect(screen.getByTestId('mock-more-vertical-icon')).toBeInTheDocument();
  });

  test('renders chat messages section', () => {
    renderWithStore(<InboxChatContent chatContent={mockChatContent} />);
    const chatMessages = screen.getAllByTestId('chat-message');
    expect(chatMessages[0]).toHaveTextContent('Hello!');
    expect(chatMessages[1]).toHaveTextContent('Hello! How can I assist you today?');
    expect(screen.getByText('12:30 PM')).toBeInTheDocument();
    expect(screen.getByText('12:35 PM')).toBeInTheDocument();
  });

  test('renders chat input section with icons', () => {
    renderWithStore(<InboxChatContent chatContent={mockChatContent} />);
    expect(screen.getByPlaceholderText('CHAT_INPUT_PLACEHOLDER')).toBeInTheDocument();
    expect(screen.getByTestId('mock-sparkles-icon')).toBeInTheDocument();
    expect(screen.getByTestId('mock-type-outline-icon')).toBeInTheDocument();
    expect(screen.getByTestId('mock-smile-icon')).toBeInTheDocument();
    expect(screen.getByTestId('mock-file-plus2-icon')).toBeInTheDocument();
    expect(screen.getAllByTestId('mock-link-icon')[0]).toBeInTheDocument();
    expect(screen.getByTestId('mock-send-icon')).toBeInTheDocument();
  });

  test('renders InboxChatSideProfile component', () => {
    renderWithStore(<InboxChatContent chatContent={mockChatContent} />);
    expect(InboxChatSideProfile).toHaveBeenCalled(); // Assert that the mocked component was called
    expect(screen.getByTestId('mock-inbox-chat-side-profile')).toBeInTheDocument();
  });

  test('renders initial chat messages', () => {
    renderWithStore(<InboxChatContent chatContent={mockChatContent} />);
    const chatMessages = screen.getAllByTestId('chat-message');
    expect(chatMessages[0]).toHaveTextContent('Hello!');
    expect(chatMessages[1]).toHaveTextContent('Hello! How can I assist you today?');
  });

  test('covers handleSuggestionClick: sends suggestion and hides suggestions', () => {
    renderWithStore(<InboxChatContent chatContent={mockChatContent} />);
    // There should be suggestions visible
    const suggestions = screen.getAllByTestId('chat-suggestion');
    expect(suggestions.length).toBe(2);

    // Click the first suggestion
    fireEvent.click(suggestions[0]);

    // Suggestion should be added as the last message
    const chatMessages = screen.getAllByTestId('chat-message');
    expect(chatMessages[chatMessages.length - 1]).toHaveTextContent('Hello! How can I assist you today?');

    // Suggestions should now be hidden
    expect(screen.queryByTestId('chat-suggestion')).toBeNull();
  });

  test('does not send empty or whitespace-only message', () => {
    renderWithStore(<InboxChatContent chatContent={mockChatContent} />);
    const sendButton = screen.getByTestId('send-button');
    const initialMessages = screen.getAllByTestId('chat-message').length;

    // Try to send an empty message
    fireEvent.click(sendButton);
    expect(screen.getAllByTestId('chat-message').length).toBe(initialMessages);

    // Simulate whitespace input (if possible)
    // If you want to simulate whitespace, you may need to mock the inputValue state or the onChange handler.
    // For now, just click send again to simulate whitespace (since inputValue is still empty/whitespace)
    fireEvent.click(sendButton);
    expect(screen.getAllByTestId('chat-message').length).toBe(initialMessages);
  });

  test('covers handleSuggestionClick: sends suggestion and hides suggestions', () => {
    renderWithStore(<InboxChatContent chatContent={mockChatContent} />);
    // Suggestions should be visible
    const suggestions = screen.getAllByTestId('chat-suggestion');
    expect(suggestions.length).toBeGreaterThan(0);

    // Click the first suggestion
    fireEvent.click(suggestions[0]);

    // Suggestion should be added as the last message
    fireEvent.click(suggestions[0]);

    const chatMessages = screen.getAllByTestId('chat-message');
    expect(chatMessages[chatMessages.length - 1]).toHaveTextContent('Hello! How can I assist you today?');

    // Suggestions should now be hidden
    expect(screen.queryByTestId('chat-suggestion')).toBeNull();
  });

  test('shows emoji picker modal when emoji button is clicked', () => {
    renderWithStore(<InboxChatContent chatContent={mockChatContent} />);
    // Emoji picker should not be visible initially
    expect(screen.queryByTestId('mock-emoji-modal')).toBeNull();

    // Click the emoji button
    const emojiButton = screen.getByRole('button', {name: /open emoji picker/i});
    fireEvent.click(emojiButton);

    // Emoji picker modal should now be visible
    expect(screen.getByTestId('mock-emoji-modal')).toBeInTheDocument();
  });

  test('closes emoji picker modal and calls onEmojiClick', () => {
    renderWithStore(<InboxChatContent chatContent={mockChatContent} />);
    // Open emoji picker
    const emojiButton = screen.getByRole('button', {name: /open emoji picker/i});
    fireEvent.click(emojiButton);

    // Click emoji in modal
    const emoji = screen.getByTestId('mock-emoji');
    fireEvent.click(emoji);

    // Click close button in modal
    const closeBtn = screen.getByTestId('close-emoji-modal');
    fireEvent.click(closeBtn);

    // Modal should be closed
    expect(screen.queryByTestId('mock-emoji-modal')).toBeNull();
  });

  test('renders default editor initially', () => {
    renderWithStore(<InboxChatContent chatContent={mockChatContent} />);
    // Check for default editor
    expect(screen.getByPlaceholderText('CHAT_INPUT_PLACEHOLDER')).toBeInTheDocument();
  });
});
