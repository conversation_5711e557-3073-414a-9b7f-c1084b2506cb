import {render, screen} from '@testing-library/react';
import {describe, test, expect, vi} from 'vitest';
import {InboxPreviousConversation} from '../components/InboxPreviousConversation';
import {prevMessages} from '../mockData/mock-chat-content';

// Mock child components
vi.mock('../components/InboxMessages', () => ({
  InboxMessages: vi.fn(({messages}) => (
    <div data-testid="mock-inbox-messages" data-messages={JSON.stringify(messages)} />
  )),
}));

// Mock lucide-react Info icon
vi.mock('lucide-react', async () => {
  const actual = await vi.importActual('lucide-react');
  return {
    ...actual,
    Info: () => <svg data-testid="mock-info-icon" aria-label="Info" />,
  };
});

describe('InboxPreviousConversation', () => {
  test('renders header with Agent 1 and Info icon', () => {
    render(<InboxPreviousConversation />);
    expect(screen.getByText('Agent 1')).toBeInTheDocument();
    expect(screen.getByLabelText('Info')).toBeInTheDocument();
  });

  test('renders InboxMessages component with previousAgentMessages', () => {
    render(<InboxPreviousConversation />);
    expect(screen.getByTestId('mock-inbox-messages')).toBeInTheDocument();
    expect(screen.getByTestId('mock-inbox-messages')).toHaveAttribute('data-messages', JSON.stringify(prevMessages));
  });

  test('applies correct styling to the main container', () => {
    render(<InboxPreviousConversation />);
    const mainContainer = screen.getByTestId('inbox-previous-conversation-container');
    expect(mainContainer).toHaveClass(
      'mx-auto',
      'mt-4',
      'mb-4',
      'rounded-xl',
      'bg-white',
      'border',
      'border-[var(--color-silver-gray)]',
    );
  });

  test('applies correct styling to the header', () => {
    render(<InboxPreviousConversation />);
    const header = screen.getByText('Agent 1').parentElement;
    expect(header).toHaveClass(
      'flex',
      'items-center',
      'justify-between',
      'px-4',
      'pt-3',
      'pb-2',
      'bg-[var(--color-silver-gray-light)]',
      'h-[43px]',
      'rounded-t-xl',
    );
  });

  test('applies correct styling to the padding wrapper', () => {
    render(<InboxPreviousConversation />);
    const paddingWrapper = screen.getByTestId('mock-inbox-messages').parentElement;
    expect(paddingWrapper).toHaveClass(
      'bg-[var(--color-silver-gray-light)]',
      'm-2',
      'rounded-xl',
      'p-4',
      'space-y-4',
      'h-52',
      'overflow-y-auto',
    );
  });
});
