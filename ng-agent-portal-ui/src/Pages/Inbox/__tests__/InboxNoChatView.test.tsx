import {render, screen} from '@testing-library/react';
import {InboxNoChatView} from '../components/InboxNoChatView';
import {MESSAGES} from '@/constants';
import {describe, expect, test} from 'vitest';

describe('InboxNoChatView Component', () => {
  test('renders without crashing', () => {
    render(<InboxNoChatView />);
  });

  test('displays the correct title', () => {
    render(<InboxNoChatView />);
    const titleElement = screen.getByText(MESSAGES.NO_ACTIVE_CHAT_TITLE);
    expect(titleElement).toBeInTheDocument();
  });

  test('displays the correct description', () => {
    render(<InboxNoChatView />);
    const descriptionElement = screen.getByText(MESSAGES.NO_ACTIVE_CHAT_DESCRIPTION);
    expect(descriptionElement).toBeInTheDocument();
  });

  test('renders the NoChatsIcon', () => {
    render(<InboxNoChatView />);
    const imageElement = screen.getByAltText('No Chats');
    expect(imageElement).toBeInTheDocument();
  });

  test('has the data-testid attribute', () => {
    render(<InboxNoChatView />);
    const element = screen.getByTestId('inbox-no-chat-view');
    expect(element).toBeInTheDocument();
  });
});
