import {render, screen} from '@testing-library/react';
import {describe, test, expect} from 'vitest';
import {InboxMessages} from '../components/InboxMessages';
import type {Message} from '../types';

describe('InboxMessages', () => {
  const mockMessages: Message[] = [
    {id: 1, text: 'Hello!', sender: 'left', time: '10:00 AM'},
    {id: 2, text: 'Hi there!', sender: 'right', time: '10:05 AM'},
    {id: 3, text: 'How are you?', sender: 'left', time: '10:10 AM'},
  ];

  test('renders messages with correct alignment and bubble styling', () => {
    render(<InboxMessages messages={mockMessages} />);

    const leftContainers = screen.getAllByTestId('message-container-left');
    leftContainers.forEach(container => {
      expect(container).toHaveClass('items-start');
    });

    const rightContainers = screen.getAllByTestId('message-container-right');
    rightContainers.forEach(container => {
      expect(container).toHaveClass('items-end');
    });

    const leftBubbles = screen.getAllByTestId('message-bubble-left');
    leftBubbles.forEach(bubble => {
      expect(bubble).toHaveClass('bg-[var(--chat-bg-light)]', 'left-message-tail');
    });

    const rightBubbles = screen.getAllByTestId('message-bubble-right');
    rightBubbles.forEach(bubble => {
      expect(bubble).toHaveClass('bg-[var(--chat-bg-accent)]', 'right-message-tail');
    });
  });

  test('renders message text and time', () => {
    render(<InboxMessages messages={mockMessages} />);

    expect(screen.getByText('Hello!')).toBeInTheDocument();
    expect(screen.getByText('10:00 AM')).toBeInTheDocument();
    expect(screen.getByText('Hi there!')).toBeInTheDocument();
    expect(screen.getByText('10:05 AM')).toBeInTheDocument();
    expect(screen.getByText('How are you?')).toBeInTheDocument();
    expect(screen.getByText('10:10 AM')).toBeInTheDocument();
  });

  test('renders no messages when the messages array is empty', () => {
    render(<InboxMessages messages={[]} />);
    expect(screen.queryByTestId('chat-message')).not.toBeInTheDocument();
  });

  test('ensures full line coverage for InboxMessages component', () => {
    // This test implicitly covers all lines by rendering the component with various message types
    // and asserting on their presence and styling.
    render(<InboxMessages messages={mockMessages} />);

    // Check for the presence of all messages
    expect(screen.getByText('Hello!')).toBeInTheDocument();
    expect(screen.getByText('Hi there!')).toBeInTheDocument();
    expect(screen.getByText('How are you?')).toBeInTheDocument();

    // Check for the presence of all timestamps
    expect(screen.getByText('10:00 AM')).toBeInTheDocument();
    expect(screen.getByText('10:05 AM')).toBeInTheDocument();
    expect(screen.getByText('10:10 AM')).toBeInTheDocument();

    // Verify styling for left-aligned message
    const leftMessageText = screen.getByText('Hello!');
    const leftMessageContainer = leftMessageText.closest('.flex.flex-col');
    console.log('leftMessageContainer', leftMessageContainer);

    expect(leftMessageContainer).toHaveClass('flex flex-col gap-3 overflow-hidden');

    // Verify styling for right-aligned message
    const rightMessageText = screen.getByText('Hi there!');
    const rightMessageAlignmentContainer = rightMessageText.parentElement?.parentElement;
    expect(rightMessageAlignmentContainer).toHaveClass(
      'text-sm z-0 overflow-auto prose prose-sm max-w-none no-scrollbar bg-transparent w-auto h-auto py-0 m-0',
    );
  });
});
