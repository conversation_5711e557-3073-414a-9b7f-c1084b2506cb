import {describe, test, expect, vi} from 'vitest';
import {fireEvent, render, screen} from '@testing-library/react';
import ChatFileUploader from '../components/ChatFileUploader';

// Mock react-dropzone to simplify testing
vi.mock('react-dropzone', () => ({
  useDropzone: (opts: any) => ({
    getRootProps: () => ({
      onClick: opts.onDrop ? () => {} : undefined,
      tabIndex: 0,
      role: 'button',
    }),
    getInputProps: () => ({
      onChange: (e: any) => {
        if (opts.onDrop) {
          const files = e.target.files;
          opts.onDrop(Array.from(files));
        }
      },
      type: 'file',
    }),
  }),
}));

function createFile(name: string, type: string) {
  return new File(['dummy content'], name, {type});
}

describe('ChatFileUploader', () => {
  test('renders upload icon', () => {
    render(<ChatFileUploader onFileSelect={vi.fn()} />);
    expect(screen.getByTestId('file-upload-icon')).toBeInTheDocument();
  });

  test('calls onFileSelect when a file is selected', () => {
    const onFileSelect = vi.fn();
    render(<ChatFileUploader onFileSelect={onFileSelect} />);
    const input = screen.getByRole('button').querySelector('input[type="file"]');
    const file = createFile('test.txt', 'text/plain');
    fireEvent.change(input as HTMLInputElement, {
      target: {files: [file]},
    });
    expect(onFileSelect).toHaveBeenCalledWith(file);
  });

  test('accepts correct file types', () => {
    render(<ChatFileUploader onFileSelect={vi.fn()} />);
    const input = screen.getByRole('button').querySelector('input[type="file"]');
    expect(input).toHaveAttribute('type', 'file');
  });

  test('renders with correct classes', () => {
    render(<ChatFileUploader onFileSelect={vi.fn()} />);
    expect(screen.getByRole('button')).toHaveClass('flex', 'items-center', 'gap-2');
  });
});
