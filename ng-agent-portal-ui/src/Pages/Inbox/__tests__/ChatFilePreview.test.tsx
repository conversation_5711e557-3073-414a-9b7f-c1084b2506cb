import {describe, test, expect, vi, beforeEach} from 'vitest';
import {render, screen} from '@testing-library/react';
import ChatFilePreview from '../components/ChatFilePreview';
import {afterEach} from 'node:test';

function createFile(name: string, type: string) {
  return new File(['dummy content'], name, {type});
}

vi.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => key, // or return English directly if you want
  }),
}));

describe('ChatFilePreview', () => {
  beforeEach(() => {
    // Mock URL.createObjectURL
    global.URL.createObjectURL = vi.fn(() => 'blob:http://localhost/dummy-url') as any;
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  test('renders image preview with link', () => {
    const file = createFile('test.png', 'image/png');
    render(<ChatFilePreview file={file} fileType="image/png" fileName="test.png" />);
    const link = screen.getByTitle('OPEN_IMAGE_IN_NEW_TAB');
    expect(link).toHaveAttribute('href', 'blob:http://localhost/dummy-url');
    expect(screen.getByAltText('test.png')).toBeInTheDocument();
  });

  test('renders video preview with link', () => {
    const file = createFile('test.mp4', 'video/mp4');
    const {container} = render(<ChatFilePreview file={file} fileType="video/mp4" fileName="test.mp4" />);
    const link = screen.getByTitle('OPEN_VIDEO_IN_NEW_TAB');
    expect(link).toHaveAttribute('href', 'blob:http://localhost/dummy-url');
    // The fallback text is the translation key, as per the mock
    expect(screen.getByText('BROWSER_VIDEO_NOT_SUPPORT')).toBeInTheDocument();
    expect(container.querySelector('video')).toBeInTheDocument();
  });

  test('renders audio preview with link', () => {
    const file = createFile('test.mp3', 'audio/mp3');
    render(<ChatFilePreview file={file} fileType="audio/mp3" fileName="test.mp3" />);
    const link = screen.getByTitle('OPEN_AUDIO_IN_NEW_TAB');
    expect(link).toHaveAttribute('href', 'blob:http://localhost/dummy-url');
    expect(screen.getByText('BROWSER_AUDIO_NOT_SUPPORT')).toBeInTheDocument();
  });

  test('renders download link for other file types', () => {
    const file = createFile('test.pdf', 'application/pdf');
    render(<ChatFilePreview file={file} fileType="application/pdf" fileName="test.pdf" />);
    const link = screen.getByText('test.pdf');
    expect(link).toHaveAttribute('href', 'blob:http://localhost/dummy-url');
    expect(link).toHaveAttribute('download', 'test.pdf');
  });
});
