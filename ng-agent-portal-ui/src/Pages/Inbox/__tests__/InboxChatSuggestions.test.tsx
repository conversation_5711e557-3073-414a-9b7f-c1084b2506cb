import {render, screen, fireEvent} from '@testing-library/react';
import {describe, it, expect, vi, beforeEach} from 'vitest';
import {InboxChatSuggestion} from '../components/InboxChatSuggestion';

// Mock usage example (if you want to mock this component in another test file)
vi.mock('../components/InboxChatSuggestion', () => ({
  InboxChatSuggestion: ({suggestion, onSuggestionClick}: any) => (
    <div data-testid="mock-inbox-chat-suggestions">
      <button data-testid="chat-suggestion" onClick={() => onSuggestionClick(suggestion)}>
        {suggestion}
      </button>
    </div>
  ),
}));

describe('InboxChatSuggestions', () => {
  const suggestion = 'Suggestion 1';
  const onSuggestionClick = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders suggestions when show is true', () => {
    render(<InboxChatSuggestion suggestion={suggestion} onSuggestionClick={onSuggestionClick} />);
    const buttons = screen.getAllByTestId('chat-suggestion');
    expect(buttons[0]).toHaveTextContent('Suggestion 1');
  });

  it('calls onSuggestionClick when a suggestion is clicked', () => {
    render(<InboxChatSuggestion suggestion={suggestion} onSuggestionClick={onSuggestionClick} />);
    const buttons = screen.getAllByTestId('chat-suggestion');
    fireEvent.click(buttons[0]);
    expect(onSuggestionClick).toHaveBeenCalledWith('Suggestion 1');
  });

  it('mocked component renders as expected', () => {
    // This test demonstrates the mock usage above
    render(<InboxChatSuggestion suggestion={'Mock 1'} onSuggestionClick={onSuggestionClick} />);
    expect(screen.getByTestId('mock-inbox-chat-suggestions')).toBeInTheDocument();
    expect(screen.getByText('Mock 1')).toBeInTheDocument();
  });
});
