import {render, screen, fireEvent} from '@testing-library/react';
import {InboxLinkInsertModal} from '../components/InboxLinkInsertModal';
import {beforeEach, describe, expect, test, vi} from 'vitest';

describe('InboxLinkInsertModal', () => {
  const mockOnInsert = vi.fn();
  const mockOnClose = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  test('does not render when isOpen is false', () => {
    render(<InboxLinkInsertModal isOpen={false} onInsert={mockOnInsert} onClose={mockOnClose} />);
    expect(screen.queryByText(/display text/i)).toBeNull();
  });

  test('renders correctly when isOpen is true', () => {
    render(<InboxLinkInsertModal isOpen={true} onInsert={mockOnInsert} onClose={mockOnClose} />);
    expect(screen.getByTestId('display-text')).toBeInTheDocument();
    expect(screen.getByTestId('url-input')).toBeInTheDocument();
    expect(screen.getByText(/insert/i)).toBeInTheDocument();
    expect(screen.getByText(/cancel/i)).toBeInTheDocument();
  });

  test('disables INSERT button when fields are empty', () => {
    render(<InboxLinkInsertModal isOpen={true} onInsert={mockOnInsert} onClose={mockOnClose} />);
    const insertButton = screen.getByText(/insert/i);
    expect(insertButton).toHaveClass('cursor-not-allowed');
  });

  test('calls onInsert with url and displayText, then resets and closes modal', () => {
    render(<InboxLinkInsertModal isOpen={true} onInsert={mockOnInsert} onClose={mockOnClose} />);

    const displayTextInput = screen.getByTestId('display-text');
    const urlInput = screen.getByTestId('url-input');
    const insertButton = screen.getByText(/insert/i);

    fireEvent.change(displayTextInput, {target: {value: 'My Link'}});
    fireEvent.change(urlInput, {target: {value: 'https://example.com'}});

    fireEvent.click(insertButton);

    expect(mockOnInsert).toHaveBeenCalledWith('https://example.com', 'My Link');
    expect(mockOnClose).toHaveBeenCalled();
  });

  test('uses url as display text when displayText is empty', () => {
    render(<InboxLinkInsertModal isOpen={true} onInsert={mockOnInsert} onClose={mockOnClose} />);

    // Find all inputs
    const inputs = screen.getAllByRole('textbox');
    expect(inputs).toHaveLength(2); // First is displayText, second is url

    const urlInput = inputs[1]; // Second input is the URL field
    const insertButton = screen.getByText(/insert/i);

    fireEvent.change(urlInput, {target: {value: 'https://example.com'}});
    fireEvent.click(insertButton);

    expect(mockOnInsert).toHaveBeenCalledWith('https://example.com', 'https://example.com');
    expect(mockOnClose).toHaveBeenCalled();
  });

  test('calls onClose when CANCEL button is clicked', () => {
    render(<InboxLinkInsertModal isOpen={true} onInsert={mockOnInsert} onClose={mockOnClose} />);
    fireEvent.click(screen.getByText(/cancel/i));
    expect(mockOnClose).toHaveBeenCalled();
  });
});
