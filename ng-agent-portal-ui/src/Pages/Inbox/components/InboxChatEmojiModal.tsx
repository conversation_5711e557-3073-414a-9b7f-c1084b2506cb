import React, {useEffect, useRef} from 'react';
import EmojiPicker from 'emoji-picker-react';
import type {EmojiClickData} from 'emoji-picker-react';

interface InboxChatEmojiModalProps {
  show: boolean;
  onClose: () => void;
  onEmojiClick: (emojiData: EmojiClickData, event: MouseEvent) => void;
  toggleButtonRef?: React.RefObject<HTMLElement | null>;
}

const InboxChatEmojiModal: React.FC<InboxChatEmojiModalProps> = ({show, onClose, onEmojiClick, toggleButtonRef}) => {
  const modalRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const isClickOutsideModal = modalRef.current && !modalRef.current.contains(event.target as Node);
      const isClickOutsideToggle = toggleButtonRef?.current && !toggleButtonRef.current.contains(event.target as Node);

      if (isClickOutsideModal && isClickOutsideToggle) {
        onClose();
      }
    };

    if (show) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [show, onClose, toggleButtonRef]);

  if (!show) return null;

  return (
    <div className="fixed bottom-30 right-24 z-50">
      <div ref={modalRef} className="rounded-lg shadow-lg">
        <EmojiPicker onEmojiClick={onEmojiClick} height={350} width={350}/>
      </div>
    </div>
  );
};

export default InboxChatEmojiModal;
