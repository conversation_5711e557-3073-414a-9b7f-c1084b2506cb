import React from 'react';

interface ChatSuggestionsProps {
  suggestion: string;
  onSuggestionClick: (suggestion: string) => void;
}

export const InboxChatSuggestion: React.FC<ChatSuggestionsProps> = ({suggestion, onSuggestionClick}) => {
  if (suggestion.length === 0) return null;

  const handleClick = (suggestion: string) => () => {
    onSuggestionClick(suggestion);
  };

  return (
    <button
      key={suggestion}
      type="button"
      data-testid="chat-suggestion"
      className="border border-[var(--chat-suggestion-button-border)] 
          text-[var(--button-blue)] 
          rounded-full px-2 py-1 text-xs hover:bg-[var(--button-blue)]
          hover:text-white transition"
      onClick={handleClick(suggestion)}
    >
      {suggestion}
    </button>
  );
};
