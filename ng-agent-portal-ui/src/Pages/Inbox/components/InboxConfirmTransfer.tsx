import React from 'react';
import {MESSAGES} from '@/constants/messages.constant';
import {GenericConfirmDialog} from '@/components/GenericConfirmDialog';

interface InboxConfirmTransferProps {
  department: string | null;
  agent: string | null;
  onClose: () => void;
  onTransferSuccess: () => void;
}

export const InboxConfirmTransfer: React.FC<InboxConfirmTransferProps> = ({
  department,
  agent,
  onClose,
  onTransferSuccess,
}) => {
  const handleConfirmTransfer = (remark: string) => {
    // Logic to handle the transfer with department, agent, and remark
    console.log('Transfer confirmed:', {department, agent, remark});
    onClose();
    onTransferSuccess();
  };

  return (
    <GenericConfirmDialog
      isOpen={true}
      onClose={onClose}
      headerTitle={MESSAGES.CONFIRM_TRANSFER}
      confirmButtonText={MESSAGES.PROCEED}
      cancelButtonText={MESSAGES.CANCEL}
      onConfirm={handleConfirmTransfer}
      showTextArea={true}
      textAreaPlaceholder={MESSAGES.ENTER_OPTIONAL_REMARKS}
      data={{department, agent}}
    />
  );
};
