import {<PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>rk<PERSON>, Send, Link as LinkIcon} from 'lucide-react';
import {InboxChatContentHeader, InboxChatSideProfile, InboxPreviousConversation, InboxMessages} from '.';
import InboxChatTransferCard, {type ButtonProps} from './InboxChatTransferCard';
import {InboxChatSuggestion} from './InboxChatSuggestion';
import {useMemo, useRef, useState, useEffect} from 'react';
import type {IInboxChatContent, Message} from '../types';
import {ChatInputMode} from '../types';
import {InboxChatTransferStatus, InboxTabsEnum} from '@/enums';
import InboxChatEmojiModal from './InboxChatEmojiModal';
import type {EmojiClickData} from 'emoji-picker-react';
import {Button} from '@/components/ui';
import {messagesData} from '../mockData/mock-chat-content';
import {RichTextEditor, type EditorInstance} from '@/modules/rich-text-editor';
import {GenAIEditor} from './editors/GenAIEditor';
import {cn} from '@/lib/utils';
import {getTransferCardConfig} from '../constants';
import {ChatTransferLbl, MESSAGES} from '@/constants';
import {useTranslation} from 'react-i18next';
import ChatFileUploader from './ChatFileUploader';
import {socket} from '@/socket';
import {useGetMessagesByChatIdQuery, useAcceptChatTransferMutation} from '@/redux/chat/chatSlice';
import {InboxLinkInsertModal} from './InboxLinkInsertModal';

const suggestions = ['Hello! How can I assist you today?', 'Hello there!'];
type InboxChatContentProps = {
  chatContent: IInboxChatContent;
};
interface EditorRef {
  instance: EditorInstance;
}

export const InboxChatContent: React.FC<InboxChatContentProps> = ({chatContent}) => {
  const toggleButtonRef = useRef<HTMLButtonElement | null>(null);
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const [inputValue, setInputValue] = useState('');
  const [showSuggestions, setShowSuggestions] = useState(true);
  const [messages, setMessages] = useState<Message[]>(messagesData);
  const [ticketId, setTicketId] = useState('');
  const [chatId, setChatId] = useState('');
  const [showLinkPopover, setShowLinkPopover] = useState(false);
  const {t} = useTranslation();
  const TransferCardConfig = getTransferCardConfig(t);
  const [inputMode, setInputMode] = useState<ChatInputMode>(ChatInputMode.DEFAULT);
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const editorRef = useRef<EditorRef | null>(null);
  const modeComponentMap: Record<ChatInputMode, React.ReactNode> = useMemo(
    () => ({
      [ChatInputMode.DEFAULT]: (
        <RichTextEditor
          content={inputValue}
          onChange={setInputValue}
          placeholder={t(MESSAGES.CHAT_INPUT_PLACEHOLDER)}
          onEditorState={state => (editorRef.current = state)}
          className="w-full"
          isToolbar={false}
          selectedFiles={selectedFiles}
          onFileRemove={index => setSelectedFiles(prev => prev.filter((_, i) => i !== index))}
        />
      ),
      [ChatInputMode.RICH_TEXT]: (
        <RichTextEditor
          content={inputValue}
          onChange={setInputValue}
          placeholder={t(MESSAGES.CHAT_INPUT_PLACEHOLDER)}
          className="w-full"
          isToolbar={true}
          selectedFiles={selectedFiles}
          onFileRemove={index => setSelectedFiles(prev => prev.filter((_, i) => i !== index))}
        />
      ),
      [ChatInputMode.GEN_AI]: <GenAIEditor />,
    }),
    [inputValue, selectedFiles],
  );
  //will be moved in new file

  const messageAdapter = (message: Message[]) => {
    return message.map(msg => ({
      id: msg.id,
      text: msg.text,
      sender: msg.sender ?? (msg.senderId === `agent-dummy-id` ? 'right' : 'left'),
      time:
        msg.time ??
        new Date(String(msg.createdOn)).toLocaleTimeString([], {
          hour: '2-digit',
          minute: '2-digit',
          hour12: true,
        }),
    }));
  };
  const {data: chatMessagesData} = useGetMessagesByChatIdQuery(chatId, {
    skip: !chatId,
  });

  useEffect(() => {
    if (chatMessagesData) {
      const formattedMessages = messageAdapter(
        chatMessagesData.data?.length ? [...messagesData, ...chatMessagesData.data] : messagesData,
      );
      setMessages(formattedMessages as Message[]);
    } else {
      setMessages(messageAdapter(messagesData) as Message[]);
    }
  }, [chatMessagesData]);

  const handleSend = (msg?: string) => {
    const message = msg ?? inputValue;
    if (!message.trim() && selectedFiles.length === 0) return;

    if (socket) {
      const messageData = {
        ticketId,
        text: message,
        senderId: `agent-dummy-id`,
        senderType: 'agent',
      };
      console.log('Emitting sendMessage with data agent:', messageData);
      socket.emit('sendMessage', messageData);
    }

    const newMessage: Message = {
      id: messages.length + 1,
      text: message, // Send the raw HTML including the link
      sender: 'right',
      time: new Date().toLocaleTimeString([], {hour: '2-digit', minute: '2-digit'}),
    };

    if (selectedFiles.length) {
      newMessage.file = selectedFiles[0];
    }

    setMessages(messageAdapter([...messages, newMessage]) as Message[]);
    setInputValue('');
    setSelectedFiles([]);
  };

  const handleSuggestionClick = (suggestion: string) => {
    handleSend(suggestion);
    setShowSuggestions(false);
  };

  const handleEmojiClick = (emojiData: EmojiClickData) => {
    setInputValue(prev => prev + emojiData.emoji);
  };

  const toggleTextMode = (mode: ChatInputMode) => {
    setInputMode(inputMode !== mode ? mode : ChatInputMode.DEFAULT);
  };

  const [showTransferCard, setShowTransferCard] = useState(true);
  const [transferCardStatus, setTransferCardStatus] = useState<InboxChatTransferStatus>(
    InboxChatTransferStatus.HANDLING,
  );

  const transferTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const handleChatTransfer = () => {
    setShowTransferCard(true);
    setTransferCardStatus(InboxChatTransferStatus.HANDLING);
    if (transferTimeoutRef.current) {
      clearTimeout(transferTimeoutRef.current);
    }
    transferTimeoutRef.current = setTimeout(() => {
      setTransferCardStatus(InboxChatTransferStatus.TIMEOUT);
    }, 2500);
  };

  const cancelTransfer = () => {
    if (transferTimeoutRef.current) {
      clearTimeout(transferTimeoutRef.current);
      transferTimeoutRef.current = null;
    }
    setShowTransferCard(false);
  };
  const [acceptChatTransfer] = useAcceptChatTransferMutation();

  const AcceptTransfer = async () => {
    if (transferTimeoutRef.current) {
      clearTimeout(transferTimeoutRef.current);
      transferTimeoutRef.current = null;
    }
    setShowTransferCard(false);
    try {
      const chatObj = await acceptChatTransfer({ticketId}).unwrap();
      setChatId(chatObj.data.chatId);
      console.log('Transfer accepted successfully', chatObj);
      if (socket) {
        console.log(`Emitting joinChannel with ticketId: ${ticketId}`);
        socket.emit('joinChannel', ticketId);
      }
    } catch (error) {
      console.error('Error accepting transfer:', error);
    }
  };

  useEffect(() => {
    return () => {
      if (transferTimeoutRef.current) {
        clearTimeout(transferTimeoutRef.current);
      }
    };
  }, []);

  useEffect(() => {
    if (socket) {
      const handleReceiveMessage = (message: {text: string; senderId: string}) => {
        console.log('Received message:', message);
        if (message.senderId !== `agent-dummy-id`) {
          const newMessage: Message = {
            id: messages.length + 1,
            text: message.text,
            sender: message.senderId.startsWith('agent') ? 'right' : 'left',
            time: new Date().toLocaleTimeString([], {hour: '2-digit', minute: '2-digit'}),
          };
          setMessages(prevMessages => messageAdapter([...prevMessages, newMessage]) as Message[]);
        }
      };

      socket.on('receiveMessage', handleReceiveMessage);

      return () => {
        socket.off('receiveMessage', handleReceiveMessage);
      };
    }
  }, [messages]);

  const handleTransferButtons: () => ButtonProps[] = () => {
    switch (transferCardStatus) {
      case InboxChatTransferStatus.TRANSFERRING:
        return [
          {
            label: t(ChatTransferLbl.CHAT_REQUEST_TRANSFER_CANCEL_BUTTON),
            onClick: cancelTransfer,
            className: 'text-primary',
            variant: 'outline',
          },
        ];
      case InboxChatTransferStatus.HANDLING:
        return [
          {
            label: t(ChatTransferLbl.CHAT_REQUEST_RECEIVED_DECLINE_BUTTON),
            onClick: cancelTransfer,
            className: 'text-primary',
            variant: 'outline',
          },
          {
            label: t(t(ChatTransferLbl.CHAT_REQUEST_RECEIVED_ACCEPT_BUTTON)),
            onClick: () => {
              void AcceptTransfer();
            },
            variant: 'primary',
          },
        ];
      case InboxChatTransferStatus.TIMEOUT:
        return [
          {
            label: t(ChatTransferLbl.CHAT_REQUEST_TIMEOUT_CONTINUE_BUTTON),
            onClick: cancelTransfer,
            variant: 'primary',
          },
          {
            label: t(ChatTransferLbl.CHAT_REQUEST_TIMEOUT_RETRY_BUTTON),
            onClick: handleChatTransfer,
            className: 'text-primary',
            variant: 'outline',
          },
        ];
      default:
        return [];
    }
  };
  const handleLinkInsert = (url: string, displayText: string) => {
    if (!editorRef.current?.instance) return;

    editorRef.current.instance.insertLink(url, displayText);

    const linkContent = `<a href="${url}" target="_blank">${displayText}</a>`;
    setInputValue(linkContent);
  };

  const handleLinkClick = () => {
    setShowLinkPopover(true);
  };

  return (
    <div className="flex-1 flex flex-row overflow-hidden flex-grow">
      <div className="flex-1 flex flex-col overflow-y-auto">
        <InboxChatContentHeader handleTransferCard={handleChatTransfer} />
        <div className="flex-1 p-5 overflow-auto">
          {chatContent.tabName === InboxTabsEnum.QUEUED && <InboxPreviousConversation />}
          <InboxMessages messages={messages} />
        </div>
        {showTransferCard && chatContent.tabName !== InboxTabsEnum.QUEUED && (
          <div className="py-4 px-6">
            {/** Mock ticekt connection */}
            <div className="flex flex-col gap-2">
              <input
                type="text"
                value={ticketId}
                onChange={e => setTicketId(e.target.value)}
                placeholder="Enter Ticket ID"
                className="border p-2 rounded"
              />
              <InboxChatTransferCard
                {...TransferCardConfig[transferCardStatus]}
                buttons={handleTransferButtons()}
                footerContent={''}
              />
            </div>
          </div>
        )}
        {showSuggestions && (
          <div className="flex gap-2 px-5 pb-2 justify-end">
            {suggestions.map(suggestion => (
              <InboxChatSuggestion key={suggestion} suggestion={suggestion} onSuggestionClick={handleSuggestionClick} />
            ))}
          </div>
        )}
        <div className="p-4">
          <div className="relative mb-[18px] transition-all duration-300">
            <div className="w-full">{modeComponentMap[inputMode]}</div>
            <div className="absolute bottom-3 right-4 flex items-center gap-0.5">
              <Button
                variant="ghost"
                onClick={() => toggleTextMode(ChatInputMode.GEN_AI)}
                data-testid="gen-ai-button"
                className={cn(
                  'text-[var(--accent-purple)] w-10 h-10',
                  inputMode === ChatInputMode.GEN_AI && 'bg-blue-100',
                )}
              >
                <Sparkles className="text-[var(--sparkle-gradient)] w-5 h-5" />
              </Button>
              <Button
                variant="ghost"
                onClick={() => toggleTextMode(ChatInputMode.RICH_TEXT)}
                data-testid="rich-text-icon"
                className={cn(
                  'text-[var(--text-gray-color)] w-10 h-10',
                  inputMode === ChatInputMode.RICH_TEXT && 'bg-blue-100',
                )}
              >
                <TypeOutline className="text-[var(--text-gray-color)] w-5 h-5" />
              </Button>
              <div className="h-4 w-px bg-gray-200 mx-1" />
              <button
                ref={toggleButtonRef}
                type="button"
                onClick={() => setShowEmojiPicker(!showEmojiPicker)}
                className={cn(
                  'w-10 h-10 flex items-center justify-center rounded-md cursor-pointer',
                  showEmojiPicker && 'bg-blue-100',
                )}
                aria-label="Open emoji picker"
              >
                <Smile className="text-[var(--text-gray-color)] w-5 h-5" />
              </button>
              <button
                data-testid="file-button"
                className="w-10 h-10 rounded-sm focus:bg-blue-100 hover:bg-blue-100 flex items-center justify-center cursor-pointer"
                type="button"
              >
                <ChatFileUploader onFileSelect={file => setSelectedFiles(prev => [...prev, file])} />
              </button>
              <button
                data-testid="link-button"
                className="w-10 h-10 rounded-sm focus:bg-blue-100 hover:bg-blue-100 flex items-center justify-center cursor-pointer"
                type="button"
                onClick={handleLinkClick}
              >
                <LinkIcon className="text-[var(--text-gray-color)] w-5 h-5" />
              </button>
              <InboxLinkInsertModal
                isOpen={showLinkPopover}
                onClose={() => setShowLinkPopover(false)}
                onInsert={handleLinkInsert}
              />
              <div className="h-4 w-px bg-gray-200 mx-1" />
              <button
                data-testid="send-button"
                className="w-8 h-8 bg-[var(--button-blue)] rounded-sm flex items-center justify-center cursor-pointer"
                onClick={() => handleSend()}
                type="button"
              >
                <Send className="w-5.5 h-5.5 object-contain text-white" />
              </button>
            </div>
          </div>
        </div>
        {showEmojiPicker && (
          <InboxChatEmojiModal
            show={showEmojiPicker}
            onClose={() => setShowEmojiPicker(false)}
            onEmojiClick={handleEmojiClick}
            toggleButtonRef={toggleButtonRef}
          />
        )}
      </div>
      <InboxChatSideProfile />
    </div>
  );
};
