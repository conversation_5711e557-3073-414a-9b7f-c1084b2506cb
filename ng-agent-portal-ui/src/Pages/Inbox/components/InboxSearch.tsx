import {useCallback, useEffect, useState} from 'react';

import {Input} from '@/components/ui/input';
import {Search} from 'lucide-react';
import {useTranslation} from 'react-i18next';
import {MESSAGES} from '@/constants/messages.constant';
import {MockApiTabContentDb} from '../mockData/mock-chat-content';
import type {IInboxChatList} from '../types';
import {useDebounce} from '@/hooks';

type InboxSearchProps = {
  onSearchResults: (results: IInboxChatList[] | null) => void;
  activeTab: string;
};

export const InboxSearch: React.FC<InboxSearchProps> = ({onSearchResults, activeTab}) => {
  const {t} = useTranslation();
  const [searchTerm, setSearchTerm] = useState('');
  const debouncedSearchTerm = useDebounce(searchTerm, 300);

  const mockSearchApi = useCallback(
    (term: string) => {
      const chatListOfTab = MockApiTabContentDb.find(res => res.tabName === activeTab);
      if (term.trim() === '') {
        onSearchResults(chatListOfTab?.chatList ?? null);
        return;
      }
      const results = chatListOfTab?.chatList.filter((chat: IInboxChatList) =>
        chat.recepientName.toLowerCase().includes(term.toLowerCase()),
      );

      onSearchResults(results ?? null);
    },
    [onSearchResults, activeTab],
  );

  useEffect(() => {
    mockSearchApi(debouncedSearchTerm);
  }, [debouncedSearchTerm, mockSearchApi]);

  return (
    <div className="w-full h-[46px] border-b border-border">
      <div className="relative w-full h-full flex items-center">
        <Input
          placeholder={t(MESSAGES.INBOX_SEARCH_PLACEHOLDER)}
          className="pr-10 text-[var(--color-placeholder)] h-full rounded-none w-full border-none"
          value={searchTerm}
          onChange={e => setSearchTerm(e.target.value)}
        />
        <Search className="absolute right-3 top-1/2 -translate-y-1/2 h-4 w-4 text-[var(--color-placeholder)]" />
      </div>
    </div>
  );
};
