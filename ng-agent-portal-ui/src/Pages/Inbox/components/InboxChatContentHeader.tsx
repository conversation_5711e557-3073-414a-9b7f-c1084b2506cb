import {useState} from 'react';
import {MESSAGES} from '@/constants/messages.constant';
import {Button} from '@/components/ui/button';
import {Avatar, AvatarFallback} from '@/components/ui/avatar';
import {Video, PhoneCall, ScreenShare, EllipsisVertical} from 'lucide-react';
import {useTranslation} from 'react-i18next';
import {InboxTransferChatDialog} from '.';

interface InboxChatContentHeaderProps {
  handleTransferCard?: () => void;
}

export const InboxChatContentHeader: React.FC<InboxChatContentHeaderProps> = ({handleTransferCard}) => {
  const {t} = useTranslation();
  const [showTab, setShowTab] = useState(false);
  const [isTransferChatDialogOpen, setIsTransferChatDialogOpen] = useState(false);

  const handleOpenTransferChatDialog = () => {
    setIsTransferChatDialogOpen(true);
    setShowTab(false);
  };

  const handleCloseTransferChatDialog = () => {
    setIsTransferChatDialogOpen(false);
  };

  return (
    <div className="flex items-center justify-between p-4 border-b border-border">
      <div className="flex items-center gap-3">
        <Avatar className="w-11 h-11">
          <AvatarFallback className="text-sm">DG</AvatarFallback>
        </Avatar>

        <div>
          <p className="font-medium">Dave Grohl</p>
          <p className="text-sm text-muted-foreground">Amazon Ecom</p>
        </div>
      </div>

      <div className="flex items-center gap-2">
        <div className="flex gap-[22px]">
          <PhoneCall size={24} className="text-[var(--text-gray-color)]" />
          <Video size={24} className="text-[var(--text-gray-color)]" />
          <ScreenShare size={24} className="text-[var(--text-gray-color)]" />
        </div>

        <div className="vertical-divider-header"></div>

        <Button
          className="h-[40px] bg-[var(--color-btn-destructive-bg)] rounded-[5px] px-4 py-2 flex items-center justify-center"
          variant="destructive"
          title={t(MESSAGES.INBOX_END_CHAT_BUTTON)}
        >
          <span className="text-white text-[14px] leading-[21px] font-normal font-poppins text-center whitespace-nowrap">
            {t(MESSAGES.INBOX_END_CHAT_BUTTON)}
          </span>
        </Button>

        <div className="relative">
          <EllipsisVertical onClick={() => setShowTab(!showTab)} className="cursor-pointer" />
          {showTab && (
            <div
              onClick={handleOpenTransferChatDialog}
              className="absolute right-[8px] top-[20px] w-[172px] h-[52px] p-0 bg-white shadow-[0px_3px_15px_rgba(0,0,0,0.1)] 
                         rounded-[10px] opacity-100 border-0 flex items-center justify-center 
                         text-[var(--text-gray-color)] cursor-pointer hover:bg-gray-100 transition-colors"
            >
              {t(MESSAGES.TRANSFER_CHAT)}
            </div>
          )}
        </div>
      </div>

      <InboxTransferChatDialog
        isOpen={isTransferChatDialogOpen}
        onClose={handleCloseTransferChatDialog}
        handleTransferCard={handleTransferCard}
      />
    </div>
  );
};
