import React, {useState} from 'react';
import {FloatingField} from '@/components/floating-lebal';

interface LinkInsertionPopoverProps {
  readonly onInsert: (url: string, displayText: string) => void;
  readonly onClose: () => void;
  readonly isOpen: boolean;
}

export const InboxLinkInsertModal: React.FC<LinkInsertionPopoverProps> = ({onInsert, onClose, isOpen}) => {
  const [displayText, setDisplayText] = useState('');
  const [url, setUrl] = useState('');

  const isDisabled = !url.trim() && !displayText.trim();

  const handleInsert = () => {
    if (isDisabled) return;

    onInsert(url, displayText || url);
    setDisplayText('');
    setUrl('');
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="absolute bottom-9 right-10 w-120 h-55 bg-white p-5 rounded-lg shadow-xl border border-(--ui-border) z-50">
      <div className="flex flex-col gap-4">
        <FloatingField
          label="Display text"
          value={displayText}
          onChange={e => setDisplayText(e.target.value)}
          type="text"
          className="w-110 pl-2.5 h-11 border border-(--input-border) rounded-sm px-0 text-xs"
          disabled={false}
          data-testId="display-text"
        />

        <FloatingField
          label="Enter URL"
          value={url}
          onChange={e => setUrl(e.target.value)}
          type="text"
          className="w-110 pl-2.5 h-11 border border-(--input-border) rounded-sm px-0 text-xs"
          disabled={false}
          data-testId="url-input"
        />

        <div className="flex mt-5 justify-end gap-2">
          <button
            type="button"
            className="px-5.5 py-1.5 text-sm font-medium text-gray-600 rounded-sm cursor-pointer border border-(--input-border)"
            onClick={onClose}
          >
            CANCEL
          </button>
          <button
            type="button"
            className={`px-6.5 py-1.5 text-sm font-medium bg-[--primary] text-white rounded-sm ${
              isDisabled ? 'bg-(--primary-accent-light) cursor-not-allowed' : 'cursor-pointer bg-(--primary)'
            }`}
            onClick={handleInsert}
          >
            INSERT
          </button>
        </div>
      </div>
    </div>
  );
};
