import React from 'react';
import {Badge} from '@/components/ui/badge';
import {TabsList, TabsTrigger} from '@/components/ui/tab';
import {badgeColorMap} from '../constants/badge-classification.contant';
import type {InboxTopbarProps} from '../types/inbox-component-props';
import {useTranslation} from 'react-i18next';
import {LayoutAlignment} from '@/enums';
import {cn} from '@/lib/utils';

export const InboxTopbar: React.FC<InboxTopbarProps> = ({tabsData}) => {
  const {t, i18n} = useTranslation();
  const isRTL = i18n.dir() === LayoutAlignment.RTL.toString();

  // Wider tabs in RTL for better accommodation
  const tabWidthClasses = isRTL
    ? 'min-w-[7rem] max-w-[12rem]' // increase widths here for RTL
    : 'min-w-[5rem] max-w-[9rem]';

  return (
    <TabsList
      className={`flex w-full bg-transparent p-0 justify-start ${isRTL ? 'flex-row-reverse' : ''}`}
      style={isRTL ? {transform: 'translateX(-12px)'} : {}}
    >
      {tabsData.map(({tabId, tabName, chatCount}) => (
        <TabsTrigger
          key={tabId}
          value={tabName}
          className={`inbox-tab-trigger min-h-[20px] ${tabWidthClasses} px-3 text-[10px] sm:text-xs leading-tight flex items-center justify-between gap-1 text-center`}
        >
          <span className="whitespace-normal break-words leading-tight text-center">{t(tabName)}</span>

          <Badge
            className={cn(
              badgeColorMap[tabName],
              {'bg-[var(--color-tab-text-default)]': chatCount === 0},
              'text-white rounded-full h-4 px-2 text-[10px] font-normal shrink-0',
            )}
          >
            {chatCount}
          </Badge>
        </TabsTrigger>
      ))}
    </TabsList>
  );
};
