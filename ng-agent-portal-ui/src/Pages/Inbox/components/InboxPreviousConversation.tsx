import {Info} from 'lucide-react';
import {useState} from 'react';
import {InboxMessages} from './InboxMessages';
import type {Message} from '../types';
import {prevMessages} from '../mockData/mock-chat-content';

export const InboxPreviousConversation = () => {
  const [previousAgentMessages] = useState<Message[]>(prevMessages);
  return (
    <div
      className="mx-auto mt-4 mb-4 rounded-xl bg-white border border-[var(--color-silver-gray)]"
      data-testid="inbox-previous-conversation-container"
    >
      {/* Header */}
      <div className="flex items-center justify-between px-4 pt-3 pb-2 bg-[var(--color-silver-gray-light)] h-[43px] rounded-t-xl">
        <div className="w-[50px] h-[20px] text-left text-sm leading-[21px] tracking-normal text-[var(--text-dark-gray)] opacity-100 ">
          Agent 1
        </div>
        <Info size={16} className="text-[var(--text-label)]" />
      </div>

      {/* Padding wrapper with white background */}
      <div className="bg-[var(--color-silver-gray-light)] m-2 rounded-xl p-4 space-y-4 h-52 overflow-y-auto">
        <InboxMessages messages={previousAgentMessages} />
      </div>
    </div>
  );
};
