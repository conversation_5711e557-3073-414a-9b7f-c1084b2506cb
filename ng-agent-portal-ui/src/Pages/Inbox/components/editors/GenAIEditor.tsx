import {Button} from '@/components/ui';
import {Input} from '@/components/ui/input';
import {MESSAGES} from '@/constants';
import {Languages, RefreshCcw, WandSparkles} from 'lucide-react';
import {useTranslation} from 'react-i18next';
import {InboxChatSuggestion} from '../InboxChatSuggestion';
import {useState} from 'react';
const suggestions = ['Hello! How can I assist you today?', 'Hello there!'];

export const GenAIEditor = () => {
  const {t} = useTranslation();
  const [inputValue, setInputValue] = useState<string>('');
  return (
    <div
      data-testid="gen-ai-editor"
      className="border border-[var(--border-gray-color)] rounded-[10px] px-4 pt-4 pb-2 h-[160px] gen-ai-editor-wrapper"
    >
      <div className="flex flex-col justify-between h-full">
        <div className="h-[45px]  flex items-center  gen-ai-tools-wrapper w-fit border-0 rounded-xs p-2">
          <Button variant={'ghost'} className="text-primary hover:bg-primary/5">
            <WandSparkles className="text-primary" />
            <p className="text-sm">{t(MESSAGES.MAGIC_WRITE)}</p>
          </Button>
          <Button variant={'ghost'} className="text-default hover:bg-primary/5">
            <Languages className="text-default" />
            <p className="text-sm">{t(MESSAGES.TRANSLATION)}</p>
          </Button>
        </div>
        <div>
          <Input
            value={inputValue}
            placeholder={t(MESSAGES.CHAT_INPUT_PLACEHOLDER)}
            className="border-0 focus-visible:border-0 focus-visible:ring-0 focus-visible:outline-none shadow-none"
            onChange={e => setInputValue(e.target.value)}
          />
          <div className="w-[70%] flex gap-2 items-center">
            {suggestions.map(suggestion => (
              <InboxChatSuggestion
                key={suggestion}
                suggestion={suggestion}
                onSuggestionClick={(suggestion: string) => {
                  setInputValue(suggestion);
                }}
              />
            ))}
            <Button variant={'ghost'} className="text-primary hover:bg-primary/5" onClick={() => setInputValue('')}>
              <RefreshCcw className="text-primary" />
              <p className="text-md uppercase">{t(MESSAGES.REGENERATE)}</p>
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};
