import NoChatsIcon from '@/assets/inboxPanel/NoChatsToDisplay.svg';
import {MESSAGES} from '@/constants';
import {useTranslation} from 'react-i18next';
export const InboxNoChatView: React.FC = () => {
  const {t} = useTranslation();
  return (
    <div className="flex-1 flex flex-col items-center justify-center p-5 gap-2" data-testid="inbox-no-chat-view">
      <img src={NoChatsIcon} alt="No Chats" className="p-4 w-45" />
      <p className="font-medium"> {t(MESSAGES.NO_ACTIVE_CHAT_TITLE)}</p>
      <p className="text-sm">{t(MESSAGES.NO_ACTIVE_CHAT_DESCRIPTION)}</p>
    </div>
  );
};
