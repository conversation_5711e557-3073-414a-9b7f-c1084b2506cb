import React, {useEffect, useState} from 'react';
import type {IInboxChatContent} from '../types/inbox-tab-content';

import {TabsContent} from '@/components/ui/tabs';
import {MockApiTabContentDb} from '../mockData/mock-chat-content';
import type {InboxTabContentProps} from '../types/inbox-component-props';
import {InboxChatContent, InboxNoChatView, InboxSidebar} from '.';

export const InboxTabContent: React.FC<InboxTabContentProps> = ({tabData}) => {
  const [selectedTabContent, setSelectedTabContent] = useState<IInboxChatContent>({} as IInboxChatContent);
  useEffect(() => {
    //An api call to get tab content which will have the active chat list of self by default.

    const MockApiTabData = MockApiTabContentDb.find(res => res.tabId === tabData.tabId) ?? {};

    setSelectedTabContent(MockApiTabData as IInboxChatContent);
  }, [tabData.tabId]);

  return (
    <TabsContent key={tabData.tabId} value={tabData.tabName} className="flex-1 flex flex-col min-h-0">
      <div className="flex flex-1 bg-white rounded-none border border-[var(--border-gray-color)] shadow mr-3 mb-0 overflow-hidden min-h-0 relative">
        {!selectedTabContent.chatList || selectedTabContent.chatList.length === 0 ? (
          <InboxNoChatView />
        ) : (
          <>
            {/* Left panel */}
            <InboxSidebar sideBarContent={selectedTabContent} />
            {/* Right chat viewer*/}
            <InboxChatContent chatContent={selectedTabContent} />
          </>
        )}
      </div>
    </TabsContent>
  );
};
