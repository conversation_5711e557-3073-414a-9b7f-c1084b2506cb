import React from 'react';
import type {Message} from '../types';
import ChatFilePreview from './ChatFilePreview';
import {RichTextEditor} from '@/modules/rich-text-editor';
import {cn} from '@/lib/utils';

interface InboxMessagesProps {
  messages: Message[];
}

export const InboxMessages: React.FC<InboxMessagesProps> = ({messages}) => {
  return (
    <>
      {messages.map(msg => (
        <div
          key={msg.id}
          data-testid={`message-container-${msg.sender}`}
          className={`w-full flex flex-col ${msg.sender === 'right' ? 'items-end' : 'items-start'} mb-2`}
        >
          <div
            data-testid={`message-bubble-${msg.sender}`}
            className={cn(
              `relative ${
                msg.sender === 'right'
                  ? 'text-[var(--chat-text-dark)] bg-[var(--chat-bg-accent)] rounded-[20px_20px_0_20px] right-message-tail'
                  : 'text-card-foreground bg-[var(--chat-bg-light)] rounded-[20px_20px_20px_0] left-message-tail'
              } max-w-[70%] px-3 py-2 text-sm flex flex-col items-start justify-start`,
            )}
          >
            {msg.text?.trim() && (
              <div data-testid="chat-message">
                <RichTextEditor content={msg.text} readOnly={true} />
              </div>
            )}

            {msg.file && (
              <div className="mt-2">
                <ChatFilePreview file={msg.file} fileType={msg.fileType} fileName={msg.fileName} />
              </div>
            )}
          </div>

          <div
            className={`text-xs mt-1 text-gray-500 max-w-[70%] ${msg.sender === 'right' ? 'text-right' : 'text-left'}`}
          >
            {msg.time}
          </div>
        </div>
      ))}
    </>
  );
};
