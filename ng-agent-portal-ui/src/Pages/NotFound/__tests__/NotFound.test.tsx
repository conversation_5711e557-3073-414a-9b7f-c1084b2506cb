import {render, screen} from '@testing-library/react';
import {describe, expect, test} from 'vitest';
import NotFound from '../NotFound';

describe('NotFound', () => {
  test('renders NotFound text', () => {
    render(<NotFound />);
    expect(screen.getByText('NotFound')).toBeInTheDocument();
  });

  test('renders as a div element', () => {
    render(<NotFound />);
    const notFoundElement = screen.getByText('NotFound');
    expect(notFoundElement.tagName).toBe('DIV');
  });
});
