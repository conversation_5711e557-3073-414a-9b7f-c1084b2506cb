import {env} from '@/config/env';
import {socket} from '@/socket';
import moment from 'moment';
import React, {useEffect, useState} from 'react';

const WebUIPage: React.FC = () => {
  const [messages, setMessages] = useState([
    {author: 'User1', content: 'Hello everyone!', type: 'received'},
    {author: 'You', content: "Hi there! How's it going?", type: 'sent'},
    {author: 'User1', content: 'How can i help You?!', type: 'received'},
  ]);
  const [newMessage, setNewMessage] = useState('');
  const [ticketId, setTicketId] = useState('');
  const [conversationStarted, setConversationStarted] = useState(false);

  useEffect(() => {
    if (socket) {
      const handleReceiveMessage = (message: {text: string; senderId: string}) => {
        console.log('Received message:', message);
        if (message?.senderId !== `user-dummy-id`) {
          setMessages(messages => [...messages, {author: message?.senderId, content: message?.text, type: 'received'}]);
        }
      };

      socket.on('receiveMessage', handleReceiveMessage);

      return () => {
        socket.off('receiveMessage', handleReceiveMessage);
      };
    }
  }, [socket]);

  const handleStartConversation = async () => {
    try {
      const response = await fetch(`${env.COMMUNICATION_SERVICE_API_BASE_URL}/chats/start-conversation`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId: `user-dummy-id`,
        }),
      });
      if (response.ok) {
        setConversationStarted(true);
        const result = (await response.json()) as {data: {ticketId: string}};

        const newTicketId = result?.data?.ticketId;
        setTicketId(newTicketId);
        // for transferring to an agent
        try {
          if (newTicketId) {
            socket?.emit('joinChannel', newTicketId);
          }
        } catch (error) {
          console.error('Error joining channel:', error);
        }
      }
    } catch (error) {
      console.error(error);
    }
  };

  const handleSendMessage = () => {
    if (newMessage.trim() !== '') {
      setMessages([...messages, {author: 'You', content: newMessage, type: 'sent'}]);
      try {
        const messageData = {
          ticketId,
          text: newMessage,
          senderId: `user-dummy-id`,
          senderType: 'user',
        };
        console.log('Emitting sendMessage with data:', messageData);
        socket?.emit('sendMessage', messageData);
      } catch (error) {
        // eslint-disable-next-line no-console
        console.error(error);
      }
      setNewMessage('');
    }
  };

  if (!conversationStarted) {
    return (
      <div className="w-full h-lvh border overflow-hidden flex flex-col justify-center items-center font-sans">
        <div className="p-4 border rounded-md bg-gray-50">
          <h2 className="text-lg font-bold mb-4">Start a new conversation</h2>

          <button
            type="button"
            className="bg-primary text-white p-2 rounded-md w-full"
            onClick={() => {
              void handleStartConversation();
            }}
          >
            Start Conversation
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full  h-lvh border  overflow-hidden flex flex-col  font-sans">
      <header className="p-2 text-left border-b b">
        <div className="flex justify-between items-center">
          <div className="font-bold">Agent Chat</div>
          <div className="space-x-2">Ticket Id {ticketId}</div>
        </div>
      </header>
      <div className="flex-grow p-2 overflow-y-scroll">
        {messages.map((message, index) => (
          <div
            key={`${message.author}-${moment(new Date()).isLocal()}-${index}`}
            className={`mb-2 flex flex-col ${message.type === 'received' ? 'items-start' : 'items-end'}`}
          >
            <div
              className={`p-2 rounded-md ${message.type === 'received' ? 'bg-gray-100' : 'bg-blue-100'} w-fit max-w-[70%]`}
            >
              <div className="text-xs text-gray-500">{message.author}</div>
              <p>{message.content}</p>
            </div>
          </div>
        ))}
      </div>
      <div className="p-2 border-t bg-background">
        <div className="flex">
          <input
            type="text"
            placeholder="Type a message..."
            className="flex-grow p-2 border rounded-md"
            value={newMessage}
            onChange={e => setNewMessage(e.target.value)}
          />
          <button
            type="button"
            className="bg-primary text-white p-2 rounded-md ml-2"
            onClick={() => {
              void handleSendMessage();
            }}
          >
            Send
          </button>
        </div>
      </div>
    </div>
  );
};

export default WebUIPage;
