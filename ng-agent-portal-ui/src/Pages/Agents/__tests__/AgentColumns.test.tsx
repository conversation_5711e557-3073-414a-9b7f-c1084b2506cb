import {describe, it, expect, vi} from 'vitest';
import {agentColumns} from '@/Pages/Agents/AgentColumns';
import type {TFunction} from 'i18next';
import {render, screen} from '@testing-library/react';
import {mockUser} from '@/constants/test.constant';

describe('agentColumns', () => {
  const mockT: (key: string) => string = key => key;
  const mockRefetch = vi.fn();
  it('should define the correct columns', () => {
    const columns = agentColumns(mockT as TFunction<'translation', undefined>, mockRefetch, mockUser);
    expect(columns).toHaveLength(8);

    // Check name column
    expect(columns[0].id).toBe('name');
    expect(columns[0].header).toBe('NAME');
    expect(columns[0].cell).toBeDefined();

    // Check email column
    expect(columns[1].id).toBe('email');
    expect(columns[1].header).toBe('EMAIL');

    // Check mobile column
    expect(columns[2].id).toBe('phoneNumber');
    expect(columns[2].header).toBe('MOBILE');

    // Check department column
    expect(columns[3].id).toBe('department');
    expect(columns[3].header).toBe('DEPARTMENT');

    // Check createdAt column
    expect(columns[4].id).toBe('createdAt');
    expect(columns[4].header).toBe('CREATED_ON');
    expect(columns[4].cell).toBeDefined();

    // Check availability column
    expect(columns[5].header).toBe('AVAILABILITY');
    expect(columns[5].cell).toBeDefined();

    // Check status column
    expect(columns[6].header).toBe('STATUS');
    expect(columns[6].cell).toBeDefined();

    // Check actions column
    expect(columns[7].id).toBe('actions');
    expect(columns[7].header).toBe('ACTIONS');
    expect(columns[7].cell).toBeDefined();
  });

  it('should format createdAt date correctly', () => {
    const columns = agentColumns(mockT as TFunction<'translation', undefined>, mockRefetch, mockUser);
    const createdAtColumn = columns[4];
    const cellFunction = createdAtColumn.cell;

    if (cellFunction && typeof cellFunction === 'function') {
      // Correctly mock the row object
      const mockRow = {
        getValue: () => '2023-05-15T10:30:00Z',
      };

      // Pass mock row to the cell function
      const result = cellFunction({row: mockRow} as any);
      // Check if the date is formatted correctly
      render(result);

      expect(screen.getByText(/\d{1,2}\/\d{1,2}\/\d{4}/)).toBeInTheDocument();
    } else {
      expect(true).toBe(true);
    }
  });
});
