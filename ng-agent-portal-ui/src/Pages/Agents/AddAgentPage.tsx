'use client';

import {CommonBreadcrumb} from '@/components/BreadCrumbs/CommonBreadCrumbs';
import type {AgentFormValues} from '@/validations/agentSchema';
import SuccessModal from '@/components/modals/SuccessModal';
import {useCreateAgentMutation, useUpdateAgentMutation, useGetAgentByIdQuery} from '../../redux/agents/agentSlice';
import {useState} from 'react';
import {useNavigate, useSearchParams} from 'react-router-dom';
import {AgentForm} from '@/Pages/Agents/AgentForm';
import {MESSAGES} from '@/constants';
import {useTranslation} from 'react-i18next';
import type {ApiError, ApiResponse} from '@/types/api.type';
import {ToastColor} from '@/types';
import {useToast} from '@/hooks';

const AddAgentPage = () => {
  const {t} = useTranslation();
  const {toast} = useToast();
  const [searchParams] = useSearchParams();
  const agentId = searchParams.get('id');
  const navigate = useNavigate();
  const isEditMode = Boolean(agentId);
  const [successModalOpen, setSuccessModalOpen] = useState<boolean>(false);
  const [createAgent, {isLoading: isCreating}] = useCreateAgentMutation();
  const [updateAgent] = useUpdateAgentMutation();
  const {data: agentData, isLoading: isFetchingAgent} = useGetAgentByIdQuery(
    {id: agentId!},
    {
      skip: !isEditMode,
      refetchOnMountOrArgChange: true,
    },
  );
  const handleSubmitAgent = async (values: AgentFormValues) => {
    try {
      if (isEditMode) {
        await updateAgent({
          id: agentId!,
          data: {name: values.name, email: values.email, mobile: values.mobile, departmentId: values.departmentId},
        }).unwrap();
      } else {
        await createAgent({
          name: values.name,
          email: values.email,
          mobile: values.mobile,
          departmentId: values.departmentId,
        }).unwrap();
      }
      setSuccessModalOpen(true);
    } catch (error) {
      const message = ((error as ApiResponse<unknown>).data as ApiError)?.message;
      toast({
        title: 'Error',
        description: message ?? t('AGENT_CREATION_FAILED'),
        color: ToastColor.Error,
      });
    }
  };

  if (isEditMode && isFetchingAgent) return <p>{t('LOADING')}...</p>;

  return (
    <div className="ml-6 w-full pr-6">
      <CommonBreadcrumb
        items={[
          {label: t('TEAM'), href: '/agent/teams'},
          {label: isEditMode ? t('EDIT_AGENT_CRUMB') : t('ADD_AGENT_CRUMB')},
        ]}
      />
      <AgentForm
        onSubmit={values => void handleSubmitAgent(values)}
        isSubmitting={isCreating}
        defaultValues={isEditMode ? agentData?.data : undefined}
        isEditMode={isEditMode}
      />
      <SuccessModal
        isOpen={successModalOpen}
        onClose={() => {
          setSuccessModalOpen(false);
          void navigate('/teams'); // Redirect to the teams page after success
        }}
        message={t(isEditMode ? MESSAGES.AGENT_UPDATED : MESSAGES.AGENT_CREATED)}
      />
    </div>
  );
};
export default AddAgentPage;
