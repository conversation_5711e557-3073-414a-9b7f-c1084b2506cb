'use client';

import {useMemo, useState} from 'react';
import {useNavigate} from 'react-router-dom';
import {Button} from '@/components/ui/button';
import {Pencil, Trash2} from 'lucide-react';
import type {Row} from '@tanstack/react-table';
import {ToastColor, type AgentsDetails} from '@/types';
import {useDeleteAgentMutation} from '@/redux/agents/agentSlice';
import {GenericConfirmDialog} from '@/components/GenericConfirmDialog'; // adjust the path if needed
import {useAuth, useToast} from '@/hooks';
import {useTranslation} from 'react-i18next';
import type {ApiError, ApiResponse} from '@/types/api.type';
import {MESSAGES} from '@/constants';
import {PermissionsEnum} from '@/enums';

interface ActionsCellProps {
  row: Row<AgentsDetails>;
  refetch: () => void;
}

const ActionsCell: React.FC<ActionsCellProps> = ({row, refetch}) => {
  const navigate = useNavigate();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [deleteAgent, {isLoading}] = useDeleteAgentMutation();
  const {toast} = useToast();
  const {t} = useTranslation();
  const {user} = useAuth();

  const canEdit = useMemo(() => user?.permissions.includes(PermissionsEnum.UPDATE_AGENT), [user]);
  const canDelete = useMemo(() => user?.permissions.includes(PermissionsEnum.DELETE_AGENT), [user]);

  const agent = row.original;

  const handleEdit = () => {
    // eslint-disable-next-line @typescript-eslint/no-floating-promises
    navigate(`/addAgent?id=${agent.id}`);
  };

  const handleConfirmDelete = async (_remark: string, data?: AgentsDetails) => {
    if (!data) return;
    try {
      await deleteAgent({id: data.id}).unwrap();
      refetch();
    } catch (error) {
      console.error('Failed to delete agent:', error);
      const message = ((error as ApiResponse<unknown>).data as ApiError)?.message;
      toast({
        title: 'Error',
        description: message ?? t('AGENT_DELETION_FAILED'),
        color: ToastColor.Error,
      });
    } finally {
      setIsModalOpen(false);
      toast({
        title: 'Success',
        description: t('AGENT_DELETION_SUCCESS'),
        color: ToastColor.Success,
      });
    }
  };

  return (
    <>
      <div className="flex items-center justify-center gap-0 h-full">
        {canEdit && (
          <Button variant="ghost" size="icon" aria-label="Edit" onClick={handleEdit}>
            <Pencil className="h-2 w-2 text-gray-500" />
          </Button>
        )}
        {canDelete && (
          <Button
            variant="ghost"
            size="icon"
            aria-label="Delete"
            onClick={() => setIsModalOpen(true)}
            disabled={isLoading}
          >
            <Trash2 className="h-2 w-2 text-gray-500" />
          </Button>
        )}
      </div>

      <GenericConfirmDialog
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        headerTitle="DELETE_AGENT"
        confirmButtonText="DELETE"
        cancelButtonText="CANCEL"
        showTextArea={false}
        mainContentHeadline={MESSAGES.AGENT_DELETE_CONFIRMATION}
        onConfirm={(remark, data) => {
          void handleConfirmDelete(remark, data);
        }}
        data={agent}
      />
    </>
  );
};

export default ActionsCell;
