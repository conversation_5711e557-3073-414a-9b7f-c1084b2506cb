import React from 'react';
import {ToastColor} from '@/types';
import {useSnackbar, type OptionsObject} from 'notistack';
import {TOAST_AUTO_HIDE_DURATION} from '@/constants';
import CircleTick from '@/assets/icons/circle_tick.svg';
import {X} from 'lucide-react';

interface ToastOptions {
  title?: string;
  description: string;
  duration?: number;
  color?: ToastColor;
  anchorOrigin?: OptionsObject['anchorOrigin'];
}
export interface CustomSnackbarOptions extends OptionsObject {
  title?: string;
  color?: ToastColor;
}

export function useToast() {
  const {enqueueSnackbar} = useSnackbar();

  const toast = ({
    title,
    description,
    color = ToastColor.Info,
    duration = TOAST_AUTO_HIDE_DURATION,
    anchorOrigin = {vertical: 'top', horizontal: 'right'},
  }: ToastOptions) => {
    const options: CustomSnackbarOptions = {
      variant: 'custom',
      autoHideDuration: duration,
      title,
      color,
      anchorOrigin,
    };

    enqueueSnackbar(description, options);
  };

  return {toast};
}

// Custom Toast hook with custom content using React.createElement (valid in .ts file)
export function useCustomToast() {
  const { enqueueSnackbar, closeSnackbar } = useSnackbar();

  const customToast = ({
    title,
    description,
    duration = TOAST_AUTO_HIDE_DURATION,
    anchorOrigin = { vertical: 'top', horizontal: 'right' },
  }: ToastOptions) => {
    enqueueSnackbar(description, {
      variant: 'custom',
      autoHideDuration: duration,
      anchorOrigin,
      content: (key: import('notistack').SnackbarKey) =>
        React.createElement(
          'div',
          {
            style: {
              position: 'fixed',
              top: '20px',
              zIndex: 9999,
            },
          },
          React.createElement(
            'div',
            {
              className:
                'text-white px-5 py-3 rounded-xl shadow-lg flex items-center justify-between w-[400px]',
              style: { backgroundColor: '#4E4E4E' },
              role: 'alert',
            },
            React.createElement(
              'div',
              { className: 'flex items-center space-x-3' },
              React.createElement('img', { src: CircleTick, alt: 'tick', className: 'w-4 h-4' }),
              React.createElement(
                'div',
                null,
                title
                  ? React.createElement('div', { className: 'font-semibold' }, title)
                  : null,
                React.createElement('div', { className: 'text-sm' }, description)
              )
            ),
            React.createElement(
              'button',
              {
                onClick: () => closeSnackbar(key),
                className: 'ml-4 text-white hover:text-gray-300',
              },
              React.createElement(X, { className: 'w-4 h-4' })
            )
          )
        ),
    });
  };

  return { customToast };
}
