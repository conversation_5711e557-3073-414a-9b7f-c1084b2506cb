import {useCallback, useEffect} from 'react';
import {useAppDispatch, useAppSelector} from '../redux-hooks';
import {
  setCredentials,
  unsetCredentials,
  setPermissions,
  selectCurrentLoginStatus,
  selectCurrentAccessToken,
  selectCurrentRefreshToken,
  selectCurrentAuthState,
  selectCurrentPermissions,
  type AuthResData,
  selectCurrentUser,
} from '../../redux/auth/authSlice';
import {isTokenExpired} from '../../utils/tokenHelper';
import type {PermissionsEnum} from '../../enums';
import {useLazyGetMeQuery} from '@/redux/auth/authApiSlice';
import type {IUserWithPermissions} from '@/types';

export interface UseAuthReturn {
  // State
  isAuthenticated: boolean;
  accessToken: string | null;
  refreshToken: string | null;
  permissions: PermissionsEnum[] | null;
  authState: ReturnType<typeof selectCurrentAuthState>;
  user: IUserWithPermissions | null;

  // Actions
  login: (authData: AuthResData) => void;
  logout: () => void;
  setUserPermissions: (permissions: PermissionsEnum[]) => void;
  checkTokenExpiration: () => boolean;

  // Utilities
  hasPermission: (requiredPermissions?: PermissionsEnum[]) => boolean;
}

/**
 * Custom hook for authentication management
 * Provides login, logout, token management, and permission checking functionality
 */
export const useAuth = (): UseAuthReturn => {
  const dispatch = useAppDispatch();

  const [getMe, {isFetching, isLoading}] = useLazyGetMeQuery();

  // Selectors
  const isAuthenticated = useAppSelector(selectCurrentLoginStatus);
  const accessToken = useAppSelector(selectCurrentAccessToken);
  const refreshToken = useAppSelector(selectCurrentRefreshToken);
  const permissions = useAppSelector(selectCurrentPermissions);
  const authState = useAppSelector(selectCurrentAuthState);
  const user = useAppSelector(selectCurrentUser);

  /**
   * Login user with authentication data
   */
  const login = useCallback(
    (authData: AuthResData) => {
      dispatch(setCredentials(authData));
    },
    [dispatch],
  );

  /**
   * Logout user and clear all authentication data
   */
  const logout = useCallback(() => {
    dispatch(unsetCredentials());
  }, [dispatch]);

  /**
   * Set user permissions
   */
  const setUserPermissions = useCallback(
    (userPermissions: PermissionsEnum[]) => {
      dispatch(setPermissions(userPermissions));
    },
    [dispatch],
  );

  /**
   * Check if current token is expired
   */
  const checkTokenExpiration = useCallback((): boolean => {
    const expired = isTokenExpired();
    if (expired && isAuthenticated) {
      // Auto-logout if token is expired
      logout();
    }
    return expired;
  }, [isAuthenticated, logout]);

  const loadProfileAndSetInState = useCallback(async () => {
    if (!isLoading && !isFetching && !user) {
      await getMe();
    }
  }, [getMe, isLoading, isFetching, user]);

  useEffect(() => {
    if (accessToken) {
      void loadProfileAndSetInState();
    }
  }, [accessToken, loadProfileAndSetInState]);

  /**
   * Check if user has required permissions
   */
  const hasPermission = useCallback(
    (requiredPermissions?: PermissionsEnum[]): boolean => {
      if (!requiredPermissions || requiredPermissions.length === 0) {
        return true;
      }

      if (!permissions || permissions.length === 0) {
        return false;
      }

      return requiredPermissions.every(permission => permissions.includes(permission));
    },
    [permissions],
  );

  return {
    // State
    isAuthenticated,
    accessToken,
    refreshToken,
    permissions,
    authState,
    user,
    // Actions
    login,
    logout,
    setUserPermissions,
    checkTokenExpiration,

    // Utilities
    hasPermission,
  };
};
