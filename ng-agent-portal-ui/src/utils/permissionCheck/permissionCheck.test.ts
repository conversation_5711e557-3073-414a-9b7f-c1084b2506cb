import {describe, expect, it} from 'vitest';
import {hasPermission} from './permissionCheck';
import {PermissionsEnum} from '../../enums';

describe('hasPermission', () => {
  it('returns true when requiredPermission is undefined', () => {
    expect(hasPermission([PermissionsEnum.READ_AGENT], undefined)).toBe(true);
  });

  it('returns true when requiredPermission is an empty array', () => {
    expect(hasPermission([PermissionsEnum.READ_DEPARTMENT], [])).toBe(true);
  });

  it('returns true when user has at least one required permission', () => {
    expect(hasPermission([PermissionsEnum.READ_AGENT], [PermissionsEnum.READ_AGENT])).toBe(true);
  });

  it('returns false when user lacks all required permissions', () => {
    expect(hasPermission([PermissionsEnum.READ_AGENT], [PermissionsEnum.READ_DEPARTMENT])).toBe(false);
  });

  it('returns true when user has multiple required permissions', () => {
    expect(
      hasPermission([PermissionsEnum.READ_AGENT, PermissionsEnum.READ_DEPARTMENT], [PermissionsEnum.READ_AGENT]),
    ).toBe(true);
  });

  it('returns false when none of the required permissions match', () => {
    expect(
      hasPermission([PermissionsEnum.READ_AGENT], [PermissionsEnum.READ_DEPARTMENT, PermissionsEnum.CREATE_DEPARTMENT]),
    ).toBe(false);
  });
});
