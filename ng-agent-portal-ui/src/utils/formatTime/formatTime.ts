import moment from 'moment';

/**
 * Formats a 24-hour time string (HH:mm) to a 12-hour format with AM/PM.
 */
export function formatTime(time24: string) {
  return moment(time24, "HH:mm").format("hh:mmA");
}

/**
 * Converts a time string in HH:mm format to the total number of minutes.
 * @param time - Time string in HH:mm format.
 * @returns Total minutes as a number.
 */
export const toMinutes = (time: string) => {
  const [h, m] = time.split(':').map(Number);
  return h * 60 + m;
};