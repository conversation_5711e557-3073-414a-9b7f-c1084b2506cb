import type {FilterOptions} from '../../types/filter.types';

export interface DepartmentQueryOptions extends FilterOptions {
  includeAgents?: boolean;
}

export const buildDepartmentFilter = (options?: DepartmentQueryOptions | void): Record<string, string> | undefined => {
  if (!options) return undefined;

  const {includeAgents, ...filterOptions} = options;
  const params: Record<string, string> = {};

  // Add filter params if any filter options exist
  if (Object.keys(filterOptions).length > 0) {
    params.filter = JSON.stringify(filterOptions);
  }

  // Add includeAgents param if specified
  if (includeAgents !== undefined) {
    params.includeAgents = includeAgents.toString();
  }

  return Object.keys(params).length > 0 ? params : undefined;
};
