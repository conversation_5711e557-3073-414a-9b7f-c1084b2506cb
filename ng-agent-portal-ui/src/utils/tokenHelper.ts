import {setSecureItem, getSecureItem, removeItems} from './cacheHelper';
import type {AppLocation, SimpleNavigateFunction} from '../types/navigation.types';
import {AUTH_CACHE_KEYS} from '@/constants';

/**
 * Stores an access token securely in sessionStorage
 * @param token The token to store
 */
export const storeAccessToken = (token: string): void => {
  if (!token) return;
  setSecureItem(AUTH_CACHE_KEYS.ACCESS_TOKEN, token);
};

/**
 * Retrieves and decrypts the access token from sessionStorage
 * @returns The decrypted access token or null if not found
 */
export const getAccessToken = (): string | null => {
  return getSecureItem(AUTH_CACHE_KEYS.ACCESS_TOKEN);
};

/**
 * Stores a refresh token securely in sessionStorage
 * @param token The refresh token to store
 */
export const storeRefreshToken = (token: string): void => {
  if (!token) return;
  setSecureItem(AUTH_CACHE_KEYS.REFRESH_TOKEN, token);
};

/**
 * Retrieves and decrypts the refresh token from sessionStorage
 * @returns The decrypted refresh token or null if not found
 */
export const getRefreshToken = (): string | null => {
  return getSecureItem(AUTH_CACHE_KEYS.REFRESH_TOKEN);
};

/**
 * Stores token expiration time in sessionStorage
 * @param expiresIn Expiration time in seconds
 */
export const storeTokenExpiration = (expiresIn: number): void => {
  if (!expiresIn) return;
  const expirationTime = Date.now() + expiresIn * 1000;
  setSecureItem(AUTH_CACHE_KEYS.TOKEN_EXPIRATION, expirationTime.toString());
};

/**
 * Gets token expiration time from sessionStorage
 * @returns Expiration time as number or null if not found
 */
export const getTokenExpiration = (): number | null => {
  const expiration = getSecureItem(AUTH_CACHE_KEYS.TOKEN_EXPIRATION);
  return expiration ? parseInt(expiration, 10) : null;
};

/**
 * Checks if the current token is expired
 * @returns True if token is expired or expiration time is not found
 */
export const isTokenExpired = (): boolean => {
  const expiration = getTokenExpiration();
  if (!expiration) return true;
  return Date.now() > expiration;
};

/**
 * Clears all token-related data from sessionStorage
 */
export const clearTokens = (): void => {
  removeItems([AUTH_CACHE_KEYS.ACCESS_TOKEN, AUTH_CACHE_KEYS.REFRESH_TOKEN, AUTH_CACHE_KEYS.TOKEN_EXPIRATION]);
};

/**
 * Extracts token from URL query parameters
 * @param location The location object from react-router
 * @returns The token if found, null otherwise
 */
export const extractTokenFromUrl = (location: AppLocation): string | null => {
  const searchParams = new URLSearchParams(location.search);
  return searchParams.get('token');
};

/**
 * Removes token from URL query parameters
 * @param location The location object from react-router
 * @param navigate The navigate function from react-router
 */
export const removeTokenFromUrl = (location: AppLocation, navigate: SimpleNavigateFunction): void => {
  const searchParams = new URLSearchParams(location.search);

  if (!searchParams.has('token')) return;

  searchParams.delete('token');

  const newSearch = searchParams.toString();
  const newPath = {
    pathname: location.pathname,
    search: newSearch ? `?${newSearch}` : '',
  };

  navigate(newPath, {replace: true});
};
