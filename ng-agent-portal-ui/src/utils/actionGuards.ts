import {EndpointName, RequestStatus} from '@/constants/api.constant';
import type {Action, ActionMeta} from '@/types/api.type';

export function isGetMeFulfilledAction<T = unknown>(action: unknown): action is Action<T> {
  if (typeof action !== 'object' || action === null) {
    return false;
  }

  const maybeAction = action as {
    type?: unknown;
    payload?: unknown;
    meta?: unknown;
  };

  if (typeof maybeAction.meta !== 'object' || maybeAction.meta === null) {
    return false;
  }

  const meta = maybeAction.meta as Partial<ActionMeta>;

  return meta.arg?.endpointName === EndpointName.GetMe && meta.requestStatus === RequestStatus.Fulfilled;
}
