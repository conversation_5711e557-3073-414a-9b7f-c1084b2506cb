import {describe, expect, it, vi} from 'vitest';
import {getRouteConfig} from '../layoutRouteConfig';

// Mock the MainLayout component
vi.mock('../../Layouts/MainLayout/MainLayout', () => ({
  default: () => <div data-testid="mock-main-layout">Main Layout</div>,
}));

describe('layoutRouteConfig', () => {
  it('should return an array of route objects', () => {
    const routes = getRouteConfig();
    expect(Array.isArray(routes)).toBe(true);
    expect(routes.length).toBeGreaterThan(0);
  });

  it('should have a wildcard route for MainLayout', () => {
    const routes = getRouteConfig();
    const mainLayoutRoute = routes.find(route => route.path === '/*');

    expect(mainLayoutRoute).toBeDefined();
    expect(mainLayoutRoute?.path).toBe('/*');
    expect(mainLayoutRoute?.element).toBeDefined();
  });

  it('should have the correct structure for route objects', () => {
    const routes = getRouteConfig();

    routes.forEach(route => {
      expect(route).toHaveProperty('path');
      expect(route).toHaveProperty('element');
    });
  });
});
