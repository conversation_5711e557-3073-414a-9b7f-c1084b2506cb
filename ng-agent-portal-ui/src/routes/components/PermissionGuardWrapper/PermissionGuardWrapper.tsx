import {Navigate} from 'react-router-dom';
import type {PermissionsEnum} from '../../../enums';
import {usePermission} from '../../../hooks';

interface PermissionGuardProps {
  children: React.ReactNode;
  requiredPermissions?: PermissionsEnum[];
}

export const PermissionGuardWrapper = ({children, requiredPermissions}: PermissionGuardProps) => {
  const hasAccess = usePermission(requiredPermissions);

  return <>{hasAccess ? children : <Navigate to={'/not-authorized'} replace />}</>;
};
