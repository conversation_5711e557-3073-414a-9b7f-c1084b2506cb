import {render, screen} from '@testing-library/react';
import {describe, expect, test, vi} from 'vitest';
import {PermissionGuardWrapper} from '../PermissionGuardWrapper';
import {MemoryRouter} from 'react-router-dom';
import * as hooks from '../../../../hooks';

// Mock the usePermission hook
vi.mock('../../../../hooks', () => ({
  usePermission: vi.fn(),
}));

describe('PermissionGuardWrapper', () => {
  test('renders children when user has access', () => {
    // Mock the usePermission hook to return true
    vi.mocked(hooks.usePermission).mockReturnValue(true);

    render(
      <MemoryRouter>
        <PermissionGuardWrapper>
          <div data-testid="test-child">Test Child</div>
        </PermissionGuardWrapper>
      </MemoryRouter>,
    );

    expect(screen.getByTestId('test-child')).toBeInTheDocument();
    expect(screen.getByText('Test Child')).toBeInTheDocument();
  });

  test('redirects to not-authorized page when user does not have access', () => {
    // Mock the usePermission hook to return false
    vi.mocked(hooks.usePermission).mockReturnValue(false);

    render(
      <MemoryRouter>
        <PermissionGuardWrapper>
          <div data-testid="test-child">Test Child</div>
        </PermissionGuardWrapper>
      </MemoryRouter>,
    );

    // Child should not be rendered
    expect(screen.queryByTestId('test-child')).not.toBeInTheDocument();

    // Navigate should be called with the not-authorized path
    // We can't directly test the Navigate component's behavior in this test environment,
    // but we can verify that the child is not rendered, which implies the redirect is happening
  });

  test('passes requiredPermissions to usePermission hook', () => {
    const mockPermissions = ['READ', 'WRITE'] as any[];

    render(
      <MemoryRouter>
        <PermissionGuardWrapper requiredPermissions={mockPermissions}>
          <div>Test Child</div>
        </PermissionGuardWrapper>
      </MemoryRouter>,
    );

    expect(hooks.usePermission).toHaveBeenCalledWith(mockPermissions);
  });
});
