import i18n from 'i18next';
import {initReactI18next} from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';
import enTranslation from './locales/en/translation.json';
import arTranslation from './locales/ar/translation.json';
import frTranslation from './locales/fr/translation.json';
import porTranslation from './locales/por/translation.json';
import esTranslation from './locales/es/translation.json';
import {LayoutAlignment, Locales} from './enums';

void i18n
  .use(LanguageDetector)
  .use(initReactI18next)
  .init({
    resources: {
      en: {
        translation: enTranslation,
      },
      ar: {
        translation: arTranslation,
      },
      fr: {
        translation: frTranslation,
      },
      por: {
        translation: porTranslation,
      },
      es: {
        translation: esTranslation,
      },
    },
    fallbackLng: Locales.EN,
    debug: true,
    interpolation: {
      escapeValue: false, // react already escapes by default
    },
  });

// Set initial dir attribute based on detected language
document.documentElement.setAttribute(
  'dir',
  i18n.language === Locales.AR.toString() ? LayoutAlignment.RTL : LayoutAlignment.LTR,
);

i18n.on('languageChanged', lng => {
  console.info('Language changed to:', lng);
  document.documentElement.setAttribute(
    'dir',
    i18n.language === Locales.AR.toString() ? LayoutAlignment.RTL : LayoutAlignment.LTR,
  );
});

export default i18n;
