@import 'tailwindcss';
@import 'tw-animate-css';
@import 'react-phone-input-2/lib/style.css';

@custom-variant dark (&:is(.dark *));

@layer utilities {
      /* Hide scrollbar for Chrome, Safari and Opera */
      .no-scrollbar::-webkit-scrollbar {
          display: none;
      }
     /* Hide scrollbar for IE, Edge and Firefox */
      .no-scrollbar {
          -ms-overflow-style: none;  /* IE and Edge */
          scrollbar-width: none;  /* Firefox */
    }
  }

@theme inline {
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-success: var(--success);
  --color-success-foreground: var(--success-foreground);
  --color-warning: var(--warning);
  --color-warning-foreground: var(--warning-foreground);
  --color-info: var(--info);
  --color-info-foreground: var(--info-foreground);
  --color-info-background: var(--info-background);
  --color-info-border: var(--info-border);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-primary-accent: var(--primary-accent);
  --color-primary-accent-light: var(--primary-accent-light);
  --color-chip-border: var(--chip-border);
  --color-text-default: var(--text-default);
  --color-ui-border: var(--ui-border);
  --color-input-border: var(--input-border);
}

:root {
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.382 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: #496fdb;
  --primary-hover: #2563eb;
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: #ff686e;
  --destructive-foreground: #ffffff;
  --success: #64d6bc;
  --success-foreground: oklch(0.145 0 0);
  --warning: #f8dd87;
  --warning-foreground: oklch(0.145 0 0);
  --info-background: #f1f5ff;
  --info-border: #a5bbf6;
  --info: #7395f3;
  --info-foreground: oklch(0.205 0 0);
  --border: oklch(0.782 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.385 0.049 267.9);
  --sidebar-foreground: oklch(1 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.477 0.035 267);
  --sidebar-ring: oklch(0.708 0 0);
  --btn-hover: oklch(0.327 0.037 265.5);
  --powder-green: oklch(0.8024 0.1112 176.2);
  --powder-yellow: oklch(0.9016 0.11 92.56);
  --powder-red: oklch(0.7084 0.1843 20.74);
  --text-gray-color: oklch(0.4239 0 0);
  --text-white-color: oklch(1 0 0);
  --border-gray-color: oklch(0.8638 0 0);
  --color-placeholder: #7b7b7b;
  --color-icon: oklch(0.4239 0 0);
  --color-muted-text: oklch(0.66 0 0);
  --accent-purple: oklch(0.6096 0.2431 296.98);
  --button-blue: oklch(0.5713 0.1706 266.46);
  --sparkle-gradient: linear-gradient(180deg, #9751ff 0%, #b43570 100%);
  --border-light-gray: oklch(0.9128 0 0);
  --placeholder-gray: oklch(0.7058 0 0);
  --chat-bg-light: oklch(0.9821 0 0);
  --chat-bg-accent: oklch(0.5713 0.1706 266.46 / 10%);
  --chat-text-dark: oklch(0 0 0);
  --powder-blue: oklch(0.6862 0.1441 267.52);
  --primary-accent: #496fdb;
  --primary-accent-light: color-mix(in oklab, #496fdb 10%, white);
  --chip-border: #496fdb26;
  --ui-border: #e6e6e6;
  --text-label: #4e4e4e;
  --text-dark-gray: #00000075;
  --text-subtle: #d2d2d2;
  --color-silver-gray: #e5e7eb;
  --color-silver: #f1f1f1;
  --text-default: #4e4e4e;
  --color-tab-text-default: #757575;
  --color-tab-text-active: #4e4e4e;
  --color-tab-active-border: #496fdb;
  --color-inbox-tab-bg-active: #496fdb26;
  --color-inbox-tab-border-active: #496fdb40;
  --color-chat-item-hover-bg: #f9fbff;
  --color-chat-item-selected-bg: #f9fbff;
  --color-chat-item-selected-border: #496fdb;
  --color-badge-bg: #7395f3;
  --color-btn-destructive-bg: #ff686e;
  --color-border-input: #e2e2e2;
  --color-vertical-divider: #d1d5db;
  --color-status-online: #35d484;
  --color-status-away: #ffb049;
  --color-status-busy: #ff686e;
  --color-status-offline: #b0b0b0;
  --color-placeholders: #7b7b7b75;
  --color-icons: #7b7b7b;
  /* gray-300 Tailwind approx */
  --color-chat-bg-light: var(--chat-bg-light);
  /* Presumed defined elsewhere */
  --color-chat-bg-accent: var(--chat-bg-accent);
  /* Presumed defined elsewhere */
  --tabs-border: oklch(0.618 0.201 263);
  --table-border: oklch(0.911 0.015 265);
  --font-sans: 'Poppins';
  --chat-suggestion-button-border: #496fdb66;
  --color-silver-gray-light: #f3f3f3;
  --input-border: #dadce0;
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.618 0.201 263);
  --primary-foreground: oklch(0.205 0 0);
  --color-silver: #f1f1f1af;
  --color-silver-gray: #f3f3f3;
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: #ff686e;
  --destructive-foreground: oklch(1 0 0);
  --success: #64d6bc;
  --success-foreground: oklch(0.205 0 0);
  --warning: #f8dd87;
  --warning-foreground: oklch(0.205 0 0);
  --info-background: #f1f5ff;
  --info-border: #a5bbf6;
  --info: #7395f3;
  --info-foreground: oklch(0.205 0 0);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
  --powder-green: oklch(0.8024 0.1112 176.2);
  --powder-yellow: oklch(0.9016 0.11 92.56);
  --text-gray-color: oklch(0.4239 0 0);
  --text-white-color: oklch(1 0 0);
  --border-gray-color: oklch(0.8638 0 0);
  --color-placeholder: #7b7b7b;
  --color-icon: oklch(0.4239 0 0);
  --color-muted-text: oklch(0.66 0 0);
  --accent-purple: oklch(0.6096 0.2431 296.98);
  --button-blue: oklch(0.5713 0.1706 266.46);
  --border-light-gray: oklch(0.9128 0 0);
  --placeholder-gray: oklch(0.7058 0 0);
  --chat-bg-light: oklch(0.9821 0 0);
  --chat-bg-accent: oklch(0.5713 0.1706 266.46 / 10%);
  --chat-text-dark: oklch(0 0 0);
  --powder-blue: oklch(0.6862 0.1441 267.52);
  --color-silver-gray: #e5e7eb;
  --text-dark-gray: #00000075;
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground font-sans;
  }
}
