import {describe, it, expect, vi, beforeEach, afterEach} from 'vitest';
import {ThemeManager} from './ThemeManager';
import type {ThemeVariables} from '@/types/theme.type';

describe('ThemeManager', () => {
  const mockTheme: ThemeVariables = {
    primary: 'oklch(0.618 0.201 263)',
    foreground: 'oklch(0.382 0 0)',
    background: 'oklch(1 0 0)',
    card: 'oklch(1 0 0)',
    ring: 'oklch(0.708 0 0)',
    border: 'oklch(0.782 0 0)',
  };

  let setItemSpy: any; //NOSONAR
  let getItemSpy: any; //NOSONAR
  let documentSpy: any; //NOSONAR

  beforeEach(() => {
    // Mock localStorage
    setItemSpy = vi.spyOn(Storage.prototype, 'setItem');
    getItemSpy = vi.spyOn(Storage.prototype, 'getItem');

    // Mock document.documentElement
    documentSpy = {
      style: {
        setProperty: vi.fn(),
        removeProperty: vi.fn(),
      },
    };
    vi.spyOn(document, 'documentElement', 'get').mockReturnValue(documentSpy);
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('apply', () => {
    it('should apply theme variables to document root', () => {
      ThemeManager.apply(mockTheme);

      expect(documentSpy.style.setProperty).toHaveBeenCalledWith('--primary', mockTheme.primary);
      expect(documentSpy.style.setProperty).toHaveBeenCalledWith('--foreground', mockTheme.foreground);
      expect(documentSpy.style.setProperty).toHaveBeenCalledWith('--background', mockTheme.background);
      expect(documentSpy.style.setProperty).toHaveBeenCalledWith('--card', mockTheme.card);
      expect(documentSpy.style.setProperty).toHaveBeenCalledWith('--ring', mockTheme.ring);
      expect(documentSpy.style.setProperty).toHaveBeenCalledWith('--border', mockTheme.border);
    });
  });

  describe('reset', () => {
    it('should remove theme variables from document root', () => {
      ThemeManager.reset(mockTheme);

      expect(documentSpy.style.removeProperty).toHaveBeenCalledWith('--primary');
      expect(documentSpy.style.removeProperty).toHaveBeenCalledWith('--foreground');
      expect(documentSpy.style.removeProperty).toHaveBeenCalledWith('--background');
      expect(documentSpy.style.removeProperty).toHaveBeenCalledWith('--card');
      expect(documentSpy.style.removeProperty).toHaveBeenCalledWith('--ring');
      expect(documentSpy.style.removeProperty).toHaveBeenCalledWith('--border');
    });

    it('should handle undefined defaultTheme', () => {
      ThemeManager.reset();
      expect(documentSpy.style.removeProperty).not.toHaveBeenCalled();
    });
  });

  describe('persist', () => {
    it('should save theme to localStorage', () => {
      ThemeManager.persist(mockTheme);

      expect(setItemSpy).toHaveBeenCalledWith('theme', JSON.stringify(mockTheme));
    });
  });

  describe('load', () => {
    it('should load theme from localStorage', () => {
      getItemSpy.mockReturnValue(JSON.stringify(mockTheme));

      const result = ThemeManager.load();

      expect(getItemSpy).toHaveBeenCalledWith('theme');
      expect(result).toEqual(mockTheme);
    });

    it('should return null if no theme is stored', () => {
      getItemSpy.mockReturnValue(null);

      const result = ThemeManager.load();

      expect(result).toBeNull();
    });
  });

  describe('applyStored', () => {
    it('should apply stored theme if available', () => {
      getItemSpy.mockReturnValue(JSON.stringify(mockTheme));
      const applySpy = vi.spyOn(ThemeManager, 'apply');

      ThemeManager.applyStored();

      expect(applySpy).toHaveBeenCalledWith(mockTheme);
    });

    it('should not apply theme if none is stored', () => {
      getItemSpy.mockReturnValue(null);
      const applySpy = vi.spyOn(ThemeManager, 'apply');

      ThemeManager.applyStored();

      expect(applySpy).not.toHaveBeenCalled();
    });
  });
});
