import React, {useCallback, useEffect, useMemo, useState} from 'react';
import {ThemeManager} from './ThemeManager';
import {data} from './data';
import type {ThemeVariables} from '@/types';
import {ThemeContext} from './ThemeContext';

export const ThemeProvider: React.FC<React.PropsWithChildren> = ({children}) => {
  const [themeState, setThemeState] = useState<ThemeVariables | null>(null);

  const setTheme = useCallback((newTheme: ThemeVariables) => {
    ThemeManager.apply(newTheme);
    ThemeManager.persist(newTheme);
    setThemeState(newTheme);
  }, []);

  useEffect(() => {
    const loadTheme = () => {
      setTheme(data);
    };
    loadTheme();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const contextValue = useMemo(() => ({themeState, setTheme}), [themeState, setTheme]);

  return <ThemeContext value={contextValue}>{children}</ThemeContext>;
};
