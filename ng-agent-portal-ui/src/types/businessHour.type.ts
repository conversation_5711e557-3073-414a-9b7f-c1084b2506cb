import type { BusinessHourCategory } from '@/enums/bussiness-hour.enum';
import type {PaginatedResponse} from './api.type';

interface Department {
  id: string;
  name: string;
}

export interface Slot {
  day: string;
  open: string;
  close: string;
}

export interface IBusinessHour {
  id: string;
  name: string;
  timezone: string;
  isDefault: boolean;
  category: BusinessHourCategory;
  message: string;
  departments: Department[];
  slots: Slot[];
}

export interface BusinessHourPayload {
  name: string;
  timezone: string;
  category: BusinessHourCategory; // using the enum
  message: string;
  departments: string[]; // array of department IDs
  slots: DaySlotPayload[];
}

export interface DaySlotPayload {
  day: string; // e.g., "Everyday", "Monday", etc.
  ranges: SlotRangePayload[];
}

export interface SlotRangePayload {
  open: string;
  close: string;
}

export type BusinessHourListResponse = PaginatedResponse<IBusinessHour>;
