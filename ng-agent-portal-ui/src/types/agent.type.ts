import type {AgentPaginatedResponse, AgentResponse} from './api.type';

export enum AgentAvailability {
  ONLINE = 'online',
  AWAY = 'away',
  BUSY = 'busy',
  OFFLINE = 'offline',
}

export type AgentsDetails = {
  id: string;
  name: string;
  email: string;
  mobile: string;
  departmentId: string;
  createdAt: string;
  availability: AgentAvailability;
  status: AgentStatus;
};

export enum AgentStatus {
  ACTIVE = 'Active',
  INACTIVE = 'Inactive',
}

export type AgentListResponse = AgentPaginatedResponse<AgentsDetails>;
export type AgentSoloResponse = AgentResponse<AgentsDetails>;
