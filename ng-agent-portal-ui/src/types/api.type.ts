import type {EndpointName, RequestStatus} from '@/constants/api.constant';

export interface ApiResponse<T> {
  success: boolean;
  message: string;
  data: T;
}

export interface PaginatedResponse<T> {
  success: boolean;
  message: string;
  data: T[];
  meta: {
    total: number;
    offset: number;
  };
}
export interface ApiError {
  success: boolean;
  message: string;
}

export interface AgentPaginatedResponse<T> {
  success: boolean;
  message: string;
  data: {
    totalUsers: number;
    fetchedUsersCount: number;
    data: T[];
  };
}

export interface AgentResponse<T> {
  success: boolean;
  message: string;
  data: T;
}

export type ActionMeta = {
  arg: {
    endpointName: EndpointName;
  };
  requestStatus: RequestStatus;
};

export type Action<T> = {
  type: string;
  payload: T;
  meta: ActionMeta;
};
