import {describe, it, expect} from 'vitest';
import {getDepartmentSchema} from '../departmentSchema';

const t = (key: string) => {
  const translations: Record<string, string> = {
    DEPARTMENT_NAME_REQUIRED: 'Department name is required',
    DEPARTMENT_NAME_SPACES_ONLY: 'Department name cannot be only spaces',
    DEPARTMENT_NAME_INVALID_CHARACTERS: 'Only alphanumeric characters are allowed',
  };
  return translations[key] ?? key;
};

const departmentSchema = getDepartmentSchema(t);

describe('getDepartmentSchema', () => {
  it('validates a valid department name', () => {
    const result = departmentSchema.safeParse({name: 'HR Department'});
    expect(result.success).toBe(true);
    if (result.success) {
      expect(result.data.name).toBe('HR Department');
    }
  });

  it('trims whitespace from the name', () => {
    const result = departmentSchema.safeParse({name: '  HR Department  '});
    expect(result.success).toBe(true);
    if (result.success) {
      expect(result.data.name).toBe('HR Department');
    }
  });

  it('rejects an empty name', () => {
    const result = departmentSchema.safeParse({name: ''});
    expect(result.success).toBe(false);
    if (!result.success) {
      expect(result.error.issues[0].message).toBe('Department name is required');
    }
  });

  it('rejects a name with only whitespace', () => {
    const result = departmentSchema.safeParse({name: '   '});
    expect(result.success).toBe(false);
    if (!result.success) {
      expect(result.error.issues[0].message).toBe('Department name is required');
    }
  });

  it('rejects a name with special characters', () => {
    const result = departmentSchema.safeParse({name: 'HR Department!'});
    expect(result.success).toBe(false);
    if (!result.success) {
      expect(result.error.issues[0].message).toBe('Only alphanumeric characters are allowed');
    }
  });

  it('rejects a name with symbols', () => {
    const result = departmentSchema.safeParse({name: 'HR@Department'});
    expect(result.success).toBe(false);
    if (!result.success) {
      expect(result.error.issues[0].message).toBe('Only alphanumeric characters are allowed');
    }
  });

  it('allows numbers in the name', () => {
    const result = departmentSchema.safeParse({name: 'Department 123'});
    expect(result.success).toBe(true);
    if (result.success) {
      expect(result.data.name).toBe('Department 123');
    }
  });

  it('allows spaces in the name', () => {
    const result = departmentSchema.safeParse({name: 'Human Resources Department'});
    expect(result.success).toBe(true);
    if (result.success) {
      expect(result.data.name).toBe('Human Resources Department');
    }
  });
});
