import {BusinessHourCategory} from '@/enums/bussiness-hour.enum';
import { toMinutes } from '@/utils/formatTime/formatTime';
import * as z from 'zod';

export const businessHourSchema = (t: (key: string) => string) =>
  z.object({
    name: z
      .string()
      .min(1, {message: t('NAME_IS_REQUIRED')})
      .max(32, {message: t('NAME_MAX_32_CHAR')})
      .regex(/^[a-zA-Z0-9._ ]+$/, {message: t('NAME_ONLY_ALPHANUMERIC_DOT_UNDERSCORE')}),
    message: z.string().max(250, {message: t('MESSAGE_TOO_LONG')}),
    timezone: z.string().min(1, {message: t('TIMEZONE_IS_REQUIRED')}),
    category: z.literal(BusinessHourCategory.EVERYDAY),
    department: z
      .array(
        z.object({
          id: z.string(),
          value: z.string(),
        }),
      )
      .min(1, {message: t('SELECT_AT_LEAST_ONE_DEPARTMENT')}),
    timeRanges: z
      .array(
        z
          .object({
            from: z.string(),
            to: z.string(),
            id: z.string(),
          })
          .refine(
            slot => {
              if (!slot.from || !slot.to) return true;
              const [fh, fm] = slot.from.split(':').map(Number);
              const [th, tm] = slot.to.split(':').map(Number);
              return th * 60 + tm > fh * 60 + fm;
            },
            {
              message: t('END_TIME_GREATER_THAN_START_TIME'),
              path: ['to'],
            },
          ),
      )
      .min(1, {message: t('AT_LEAST_ONE_TIME_RANGE_IS_REQUIRED')})
      .superRefine((ranges, ctx) => {
        const toMinutes = (t: string) => {
          const [h, m] = t.split(':').map(Number);
          return h * 60 + m;
        };
        const sorted = [...ranges]
          .map((s, i) => ({...s, index: i}))
          .sort((a, b) => toMinutes(a.from) - toMinutes(b.from));
        for (let i = 0; i < sorted.length - 1; i++) {
          if (toMinutes(sorted[i].to) > toMinutes(sorted[i + 1].from)) {
            ctx.addIssue({
              code: z.ZodIssueCode.custom,
              message: t('TIME_SLOTS_OVERLAPPING'),
              path: [sorted[i].index, 'to'],
            });
            ctx.addIssue({
              code: z.ZodIssueCode.custom,
              message: t('TIME_SLOTS_OVERLAPPING'),
              path: [sorted[i + 1].index, 'from'],
            });
          }
        }
      }),
  });

export type BusinessHourValues = z.infer<ReturnType<typeof businessHourSchema>>;

// Original custom structure (just array)
export const customBusinessHourSchema = (t: (key: string) => string) =>
  z
    .array(
      z
        .object({
          day: z.string(),
          selected: z.boolean(),
          timeSlots: z.array(
            z.object({
              from: z.string().min(1, {message: t('START_TIME_IS_REQUIRED')}),
              to: z.string().min(1, {message: t('END_TIME_IS_REQUIRED')}),
              id: z.string().optional(),
              errorMessage: z.string().optional(),
            }),
          ),
        })
        .superRefine((data, ctx) => {
          if (data.selected) {
            data.timeSlots.forEach((slot, index) => {
              // Skip if either time is missing (handled by min(1) elsewhere)
              if (!slot.from || !slot.to) return;

              const fromMinutes = toMinutes(slot.from);
              const toMinutesValue = toMinutes(slot.to);

              // End time must be after start time
              if (toMinutesValue <= fromMinutes) {
                ctx.addIssue({
                  path: ['timeSlots', index, 'to'],
                  message: t('END_TIME_GREATER_THAN_START_TIME'),
                  code: z.ZodIssueCode.custom,
                });
              }
            });

            const sortedSlots = [...data.timeSlots]
              .map((s, index) => ({...s, originalIndex: index}))
              .sort((a, b) => toMinutes(a.from) - toMinutes(b.from));

            for (let i = 0; i < sortedSlots.length - 1; i++) {
              if (toMinutes(sortedSlots[i].to) > toMinutes(sortedSlots[i + 1].from)) {
                ctx.addIssue({
                  path: ['timeSlots', sortedSlots[i].originalIndex, 'to'],
                  message: t('TIME_SLOTS_OVERLAPPING'),
                  code: z.ZodIssueCode.custom,
                });
                ctx.addIssue({
                  path: ['timeSlots', sortedSlots[i + 1].originalIndex, 'from'],
                  message: t('TIME_SLOTS_OVERLAPPING'),
                  code: z.ZodIssueCode.custom,
                });
              }
            }
          }
        }),
    )
    .refine(days => days.some(d => d.selected), {message: t('SELECT_AT_LEAST_ONE_DAY'), path: []});

export type CustomBusinessHourValues = z.infer<ReturnType<typeof customBusinessHourSchema>>;

// ✅ Wrapped version to match object-based structure
export const unifiedCustomBusinessHourSchema = (t: (key: string) => string) =>
  z.object({
    name: z.string().min(1, {message: t('NAME_IS_REQUIRED')}),
    message: z.string().max(250, {message: t('MESSAGE_TOO_LONG')}),
    timezone: z.string().min(1, {message: t('TIMEZONE_IS_REQUIRED')}),
    category: z.literal(BusinessHourCategory.CUSTOM),
    department: z
      .array(
        z.object({
          id: z.string(),
          value: z.string(),
        }),
      )
      .min(1, {message: t('SELECT_AT_LEAST_ONE_DEPARTMENT')}),
    customTimeRanges: customBusinessHourSchema(t),
  });

export type UnifiedCustomBusinessHourValues = z.infer<ReturnType<typeof unifiedCustomBusinessHourSchema>>;

export const discriminatedBusinessHourSchema = (t: (key: string) => string) =>
  z.discriminatedUnion('category', [businessHourSchema(t), unifiedCustomBusinessHourSchema(t)]);

export type DiscriminatedBusinessHourValues = z.infer<ReturnType<typeof discriminatedBusinessHourSchema>>;
