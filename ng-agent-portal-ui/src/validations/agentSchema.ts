import * as z from 'zod';

export const agentSchema = z.object({
  name: z
    .string()
    .min(2, {message: 'Agent name must be at least 2 characters long'})
    .max(25, {message: 'Agent name cannot exceed 25 characters.'})
    .regex(/^[A-Za-z ]+$/, 'Please enter a valid agent name. Only letters and spaces are allowed.')
    .transform(val => val.trim()),
  email: z
    .string()
    .email('Please enter a valid email address.')
    .transform(val => val.trim()),
  mobile: z.string().regex(/^\+\d{12}$/, {
    message: 'Please enter a valid 10-digit mobile number.',
  }),
  departmentId: z
    .string()
    .min(1, 'Department name is required.')
    .transform(val => val.trim()),
});

export type AgentFormValues = z.infer<typeof agentSchema>;
