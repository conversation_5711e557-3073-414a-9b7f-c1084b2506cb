// schema.ts
import * as z from 'zod';

export const getDepartmentSchema = (t: (key: string) => string) =>
  z.object({
    name: z
      .string()
      .transform(val => val.trim())
      .refine(val => val.length > 0, {message: t('DEPARTMENT_NAME_REQUIRED')})
      .refine(val => !/^\s*$/.test(val), {message: t('DEPARTMENT_NAME_SPACES_ONLY')})
      .refine(val => /^[a-zA-Z0-9\s]+$/.test(val), {
        message: t('DEPARTMENT_NAME_INVALID_CHARACTERS'),
      }),
  });

export type DepartmentFormValues = z.infer<ReturnType<typeof getDepartmentSchema>>;
