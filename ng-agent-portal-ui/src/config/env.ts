/**
 * Environment configuration with type safety
 */

import {getConfig} from './config';

// Define the structure of our environment variables
export interface EnvConfig {
  AGENT_PORTAL_FACADE_API_BASE_URL: string;
  COMMUNICATION_SERVICE_API_BASE_URL?: string;
  AUTH_LOGIN_URL: string;
  CACHE_SECRET_KEY: string;
  COMMUNICATION_SOCKET_URL: string;
}

// Get environment variables with fallbacks
export const env: EnvConfig = {
  ...getConfig(),
};

// Validate required environment variables
export function validateEnv(): void {
  const missingVars: string[] = [];

  if (!env.AGENT_PORTAL_FACADE_API_BASE_URL) {
    const missingVars = ['VITE_AGENT_PORTAL_FACADE_API_BASE_URL', 'VITE_COMMUNICATION_SERVICE_API_BASE_URL'];
    missingVars.push(...missingVars);
  }

  if (missingVars.length > 0) {
    throw new Error(`Missing required environment variables: ${missingVars.join(', ')}. Please check your .env file.`);
  }
}
