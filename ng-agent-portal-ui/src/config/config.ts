interface AppConfig {
  AGENT_PORTAL_FACADE_API_BASE_URL: string;
  COMMUNICATION_SERVICE_API_BASE_URL: string;
  AUTH_LOGIN_URL: string;
  CACHE_SECRET_KEY: string;
  COMMUNICATION_SOCKET_URL: string;
}

declare global {
  interface Window {
    __APP_CONFIG__: AppConfig;
  }
}
const devConfig: AppConfig = {
  AGENT_PORTAL_FACADE_API_BASE_URL: import.meta.env.VITE_AGENT_PORTAL_FACADE_API_BASE_URL,
  COMMUNICATION_SERVICE_API_BASE_URL: import.meta.env.VITE_COMMUNICATION_SERVICE_API_BASE_URL,
  AUTH_LOGIN_URL: import.meta.env.VITE_AUTH_LOGIN_URL,
  CACHE_SECRET_KEY: import.meta.env.VITE_CACHE_SECRET_KEY,
  COMMUNICATION_SOCKET_URL: import.meta.env.VITE_COMMUNICATION_SOCKET_URL,
};

export const getConfig = (): AppConfig => {
  return window?.__APP_CONFIG__ && Object.keys(window.__APP_CONFIG__)?.length ? window.__APP_CONFIG__ : devConfig;
};
