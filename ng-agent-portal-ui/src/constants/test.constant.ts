import {PermissionsEnum} from '@/enums';
import type {IUserWithPermissions} from '@/types';

export const mockUser: IUserWithPermissions = {
  id: 'test-id',
  name: 'Test User',
  username: '<EMAIL>',
  permissions: [
    PermissionsEnum.CREATE_AGENT,
    PermissionsEnum.DELETE_AGENT,
    PermissionsEnum.UPDATE_AGENT,
    PermissionsEnum.READ_AGENT,
    PermissionsEnum.CREATE_DEPARTMENT,
    PermissionsEnum.DELETE_DEPARTMENT,
    PermissionsEnum.UPDATE_DEPARTMENT,
    PermissionsEnum.READ_DEPARTMENT,
  ],
};
