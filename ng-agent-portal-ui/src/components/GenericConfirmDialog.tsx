import {useState, type ReactNode} from 'react';
import {Dialog, DialogContent, DialogTitle, DialogOverlay} from '@/components/ui/dialog';
import {Button} from '@/components/ui/button';
import {useTranslation} from 'react-i18next';
import {X} from 'lucide-react';

interface GenericConfirmDialogProps<TData> {
  isOpen: boolean;
  onClose: () => void;
  headerTitle: string;
  confirmButtonText: string;
  cancelButtonText: string;
  onConfirm: (remark: string, data?: TData) => void;
  showTextArea?: boolean;
  textAreaPlaceholder?: string;
  mainContentHeadline?: string;
  customContent?: ReactNode;
  data?: TData;
}

export const GenericConfirmDialog = <TData,>({
  isOpen,
  onClose,
  headerTitle,
  confirmButtonText,
  cancelButtonText,
  onConfirm,
  showTextArea = false,
  textAreaPlaceholder = '',
  mainContentHeadline = '',
  customContent,
  data,
}: GenericConfirmDialogProps<TData>) => {
  const {t} = useTranslation();
  const [remark, setRemark] = useState('');

  const handleConfirm = () => {
    onConfirm(remark, data);
    setRemark(''); // Clear remark after confirmation
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogOverlay className="fixed inset-0 bg-black/50 z-40" />
      <DialogContent className="bg-white rounded-xl gap-0 shadow-lg w-[418px] h-[238px] p-0 overflow-hidden flex flex-col [&>button]:hidden z-50">
        {/* Header */}
        <div className="relative bg-white border-b border-gray-200 h-[60px] flex items-center">
          <DialogTitle className="pl-5 m-0 text-[14px] leading-[21px] font-medium text-[#313131] uppercase font-poppins">
            {t(headerTitle)}
          </DialogTitle>
          <button
            type="button"
            onClick={onClose}
            className="absolute right-5 top-1/2 -translate-y-1/2 text-[#4E4E4E] hover:text-gray-700"
            aria-label="Close"
          >
            <X size={20} />
          </button>
        </div>

        {/* Main Content */}
        <div className="flex justify-center items-center flex-grow p-6">
          {showTextArea ? (
            <textarea
              id="remark"
              placeholder={t(textAreaPlaceholder)}
              className="
                w-[378px] h-[68px] p-2
                border border-gray-300 rounded-md resize-none
                outline-none hover:border-gray-300 focus:border-gray-300 focus:ring-0
                placeholder:text-left placeholder:text-gray-300
                placeholder:font-normal placeholder:text-[14px] placeholder:leading-[20px]
                placeholder:font-poppins
              "
              value={remark}
              onChange={e => setRemark(e.target.value)}
            />
          ) : (
            (customContent ??
            (mainContentHeadline && (
              <p className="text-[14px] leading-[20px] font-normal text-[var(--text-gray-color)] font-sans">
                {t(mainContentHeadline)}
              </p>
            )))
          )}
        </div>

        {/* Footer */}
        <div className="flex items-center justify-end h-[79px]  px-8 gap-2">
          <Button
            variant="outline"
            className="border-[var(--border-gray-color)] text-[var(--text-gray-color)] font-normal leading-[21px]"
            onClick={onClose}
          >
            {t(cancelButtonText)}
          </Button>
          <Button
            className="text-white font-normal bg-[var(--button-blue)] hover:bg-[#3f61c7] leading-[21px]"
            onClick={handleConfirm}
          >
            {t(confirmButtonText)}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};
