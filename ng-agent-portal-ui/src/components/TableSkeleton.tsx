import React, {useMemo} from 'react';
import {Table, TableHeader, TableRow, TableHead, TableBody, TableCell} from '@/components/ui/table';
import {Skeleton} from '@/components/ui/skeleton';

interface TableSkeletonProps {
  columnCount: number;
  rowCount?: number;
}

const TableSkeleton: React.FC<TableSkeletonProps> = ({columnCount, rowCount = 5}) => {
  const columnKeys = useMemo(() => Array.from({length: columnCount}, () => Math.random().toString()), [columnCount]);
  const rowKeys = useMemo(() => Array.from({length: rowCount}, () => Math.random().toString()), [rowCount]);
  return (
    <div className="rounded-[9px] overflow-hidden">
      <Table>
        <TableHeader>
          <TableRow>
            {columnKeys.map(item => (
              <TableHead key={item} className="text-sm border-b border-[var(--table-border)]">
                <Skeleton className="w-3/4 h-4" />
              </TableHead>
            ))}
          </TableRow>
        </TableHeader>
        <TableBody>
          {rowKeys.map(row => (
            <TableRow key={row}>
              {columnKeys.map(col => (
                <TableCell key={col} className="text-default text-xs">
                  <Skeleton className="w-full h-4" />
                </TableCell>
              ))}
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
};

export default TableSkeleton;
