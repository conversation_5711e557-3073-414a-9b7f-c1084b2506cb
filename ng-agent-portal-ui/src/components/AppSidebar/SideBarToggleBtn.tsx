import React from 'react';
import {Button, useSidebar} from '../ui';
import {DynamicIcon} from 'lucide-react/dynamic';
import {useTranslation} from 'react-i18next';
import {LayoutAlignment} from '@/enums';

export const SideBarToggleBtn: React.FC = () => {
  const {toggleSidebar, state} = useSidebar();
  const {i18n} = useTranslation();
  const isRTL = i18n.dir() === LayoutAlignment.RTL.toString();
  const iconName = isRTL
    ? state === 'collapsed'
      ? 'chevron-left'
      : 'chevron-right'
    : state === 'collapsed'
      ? 'chevron-right'
      : 'chevron-left';

  // Adjust translation: in LTR shift left (negative X), in RTL shift right (positive X)
  const translateX = isRTL ? 'translate-x-3' : '-translate-x-3';

  return (
    <Button
      className={`${translateX} w-6 h-6 rounded-full bg-red-500 flex items-center justify-center translate-y-6 hover:cursor-pointer hover:bg-red-300 transition-transform duration-400 ease-out-in`}
      style={{zIndex: 1000}}
      onClick={toggleSidebar}
      aria-label={
        state === 'collapsed'
          ? isRTL
            ? 'Expand sidebar'
            : 'Expand sidebar'
          : isRTL
            ? 'Collapse sidebar'
            : 'Collapse sidebar'
      }
    >
      <DynamicIcon name={iconName} color="white" size="0.8rem" />
    </Button>
  );
};
