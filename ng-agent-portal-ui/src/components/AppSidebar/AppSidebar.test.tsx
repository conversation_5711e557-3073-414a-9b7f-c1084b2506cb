/* eslint-disable @typescript-eslint/no-explicit-any */
import {screen, fireEvent} from '@testing-library/react';
import {describe, it, expect, vi} from 'vitest';
import {AppSidebar} from './AppSidebar';

// Mock UI components
vi.mock('../ui', async () => {
  const actual = await vi.importActual<object>('../ui');
  return {
    ...actual,
    useSidebar: vi.fn(),
    Sidebar: ({children, ...props}: any) => <aside {...props}>{children}</aside>,
    SidebarHeader: ({children}: any) => <header>{children}</header>,
    SidebarGroup: ({children}: any) => <div>{children}</div>,
    SidebarGroupContent: ({children, ...props}: any) => <div {...props}>{children}</div>,
    SidebarContent: ({children}: any) => <section>{children}</section>,
    SidebarMenu: ({children}: any) => <ul>{children}</ul>,
    SidebarMenuItem: ({children, ...props}: any) => <li {...props}>{children}</li>,
    SidebarMenuButton: ({children, onClick}: any) => <button onClick={onClick}>{children}</button>,
    SidebarSeparator: () => <hr />,
    SidebarGroupLabel: ({children, ...props}: any) => <span {...props}>{children}</span>,
  };
});

// Fix image asset mocks (must return `default`)
vi.mock('../../assets/comviva-logo-full.svg', () => ({
  default: 'full-logo.svg',
}));

vi.mock('../../assets/comviva-logo-icon.svg', () => ({
  default: 'icon-logo.svg',
}));

// Mock navigate
const mockNavigate = vi.fn();
vi.mock('react-router-dom', () => ({
  useNavigate: () => mockNavigate,
}));

// Mock sidebarItems config
vi.mock('./sidebarRouteConfig', () => ({
  sidebarItems: [
    {
      title: 'Dashboard',
      url: '/dashboard',
      icon: () => <svg data-testid="dashboard-icon" />,
    },
    {isSeparator: true},
    {
      title: 'Settings',
      url: '/settings',
      icon: () => <svg data-testid="settings-icon" />,
    },
  ],
}));

// Import after mocking
import {useSidebar} from '../ui';
import {renderWithStore} from '@/testUtils/test-utils';

describe('AppSidebar', () => {
  it('renders the icon logo when sidebar is collapsed', () => {
    (useSidebar as any).mockReturnValue({state: 'collapsed'});

    renderWithStore(<AppSidebar />);

    expect(screen.getByTestId('sidebar-logo-icon')).toBeInTheDocument();
    expect(screen.queryByTestId('sidebar-logo-full')).not.toBeInTheDocument();
  });

  it('renders the full logo when sidebar is expanded', () => {
    (useSidebar as any).mockReturnValue({state: 'expanded'});

    renderWithStore(<AppSidebar />);

    expect(screen.getByTestId('sidebar-logo-full')).toBeInTheDocument();
    expect(screen.queryByTestId('sidebar-logo-icon')).not.toBeInTheDocument();
  });

  it('renders sidebar menu items and icons', () => {
    (useSidebar as any).mockReturnValue({state: 'expanded'});

    renderWithStore(<AppSidebar />);

    expect(screen.getByText('Dashboard')).toBeInTheDocument();
    expect(screen.getByTestId('dashboard-icon')).toBeInTheDocument();

    expect(screen.getByText('Settings')).toBeInTheDocument();
    expect(screen.getByTestId('settings-icon')).toBeInTheDocument();
  });

  it('navigates to correct route on menu item click', () => {
    (useSidebar as any).mockReturnValue({state: 'expanded'});

    renderWithStore(<AppSidebar />);
    fireEvent.click(screen.getByText('Dashboard'));

    expect(mockNavigate).toHaveBeenCalledWith('/dashboard');
  });
});
