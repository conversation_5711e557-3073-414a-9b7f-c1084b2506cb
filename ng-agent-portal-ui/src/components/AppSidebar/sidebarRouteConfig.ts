import {MESSAGES} from '@/constants/messages.constant';
import {RouteConstant} from '@/constants';
import {ChartPie, MessageCircle, Users, BookText, SatelliteDish, FileClock, Settings, Bell} from 'lucide-react';
import {PermissionsEnum} from '@/enums';

// Menu items.
interface SidebarItem {
  id: string; // Added unique ID
  title?: string;
  url?: string;
  icon?: React.ComponentType;
  isSeparator?: boolean;
  requiredPermissions?: PermissionsEnum[];
}

export const sidebarItems: SidebarItem[] = [
  {
    id: 'separator-1',
    isSeparator: true,
  },
  {
    id: 'reports',
    title: MESSAGES.SIDEBAR_MENU_REPORTS,
    url: '#',
    icon: ChartPie,
    requiredPermissions: [PermissionsEnum.READ_AGENT],
  },
  {
    id: 'separator-2',
    isSeparator: true,
  },
  {
    id: 'chats',
    title: MESSAGES.SIDEBAR_MENU_INBOX,
    url: RouteConstant.INBOX,
    icon: MessageCircle,
    requiredPermissions: [PermissionsEnum.READ_AGENT],
  },
  {
    id: 'teams',
    title: MESSAGES.SIDEBAR_MENU_TEAMS,
    url: RouteConstant.TEAMS,
    icon: Users,
    requiredPermissions: [PermissionsEnum.READ_DEPARTMENT],
  },
  {
    id: 'knowledge-base',
    title: MESSAGES.SIDEBAR_MENU_KNOWLEDGE_BASE,
    url: '#',
    icon: BookText,
  },
  {
    id: 'integrations',
    title: MESSAGES.SIDEBAR_MENU_INTEGRATIONS,
    url: '#',
    icon: SatelliteDish,
  },
  {
    id: 'activity-log',
    title: MESSAGES.SIDEBAR_MENU_ACTIVITY_LOG,
    url: '#',
    icon: FileClock,
  },
  {
    id: 'separator-3',
    isSeparator: true,
  },
  {
    id: 'settings',
    title: MESSAGES.SIDEBAR_MENU_SETTINGS,
    url: RouteConstant.SETTINGS,
    icon: Settings,
    requiredPermissions: [PermissionsEnum.READ_DEPARTMENT],
  },
  {
    id: 'notifications',
    title: MESSAGES.SIDEBAR_MENU_NOTIFICATIONS,
    url: '#',
    icon: Bell,
  },
];
