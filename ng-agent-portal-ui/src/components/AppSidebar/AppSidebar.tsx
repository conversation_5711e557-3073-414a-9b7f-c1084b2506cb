import {
  AvatarFallback,
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarSeparator,
  useSidebar,
} from '../ui';
import {cn} from '../../lib/utils';
import FullLogo from '../../assets/comviva-logo-full.svg';
import IconLogo from '../../assets/comviva-logo-icon.svg';
import {Avatar} from '@radix-ui/react-avatar';
import {SidebarGroupLabel} from '../ui/sidebar';
import {useNavigate} from 'react-router-dom';
import {sidebarItems} from './sidebarRouteConfig';
import {RouteConstant} from '@/constants';
import {useTranslation} from 'react-i18next';
import {LayoutAlignment, PermissionsEnum} from '@/enums';
import {useAuth} from '@/hooks';
import {getInitials} from '@/utils/getInitials';

export function AppSidebar() {
  const {state} = useSidebar();
  const navigate = useNavigate();
  const {t} = useTranslation();
  const {user} = useAuth();
  const isRtl = document.documentElement.dir === LayoutAlignment.RTL.toString();

  const hasAccess = (requiredPermissions?: PermissionsEnum[]) =>
    !requiredPermissions?.length || requiredPermissions.some(permission => user?.permissions.includes(permission));

  return (
    <Sidebar collapsible="icon" side={isRtl ? 'right' : 'left'} className="bg-[--sidebar] text-[--sidebar-foreground]">
      <SidebarHeader>
        <SidebarGroupContent onClick={() => void navigate(RouteConstant.HOME)}>
          {state === 'collapsed' ? (
            <img
              data-testid="sidebar-logo-icon"
              src={IconLogo}
              alt="Comviva Logo"
              className="mx-auto my-6 hover:cursor-pointer w-7 transition-transform duration-400 ease-out-in delay-400"
            />
          ) : (
            <img
              data-testid="sidebar-logo-full"
              src={FullLogo}
              alt="Comviva Logo"
              className="mx-auto my-6 hover:cursor-pointer transition-transform duration-100 ease-in-out delay-100"
            />
          )}
        </SidebarGroupContent>
      </SidebarHeader>
      <SidebarContent>
        <SidebarGroup className="align-center px-0 w-full">
          <SidebarGroupContent>
            <SidebarMenu className={cn({'items-center ': state === 'collapsed'})}>
              <SidebarMenuItem className="py-4 flex px-4">
                <Avatar className="w-8 h-8 rounded-full shadow-sm bg-white text-slate-600 font-medium flex items-center justify-center">
                  <AvatarFallback className="text-[#4E4E4E]">{getInitials(user?.name ?? '')}</AvatarFallback>
                </Avatar>
                <SidebarGroupLabel className={cn({hidden: state === 'collapsed'}, 'text-sm font-medium text-white')}>
                  {user?.name}
                </SidebarGroupLabel>
              </SidebarMenuItem>
              {sidebarItems
                .filter(item => item.isSeparator ?? hasAccess(item.requiredPermissions))
                .map(item =>
                  item.isSeparator ? (
                    <SidebarMenuItem
                      key={item.id}
                      className={cn({'w-85/100': state === 'expanded'}, 'flex flex-col items-center mx-auto')}
                    >
                      <SidebarSeparator />
                    </SidebarMenuItem>
                  ) : (
                    <SidebarMenuItem key={item.id} className="p-0">
                      <SidebarMenuButton
                        onClick={() => {
                          if (item.url) {
                            void navigate(item.url);
                          }
                        }}
                        className={cn(
                          'flex items-center w-full p-5 my-1  transition-colors rounded-none',
                          'hover:bg-[var(--btn-hover)] hover:text-red-500 hover:[&>svg]:text-red-500 hover:cursor-pointer',
                        )}
                        asChild
                      >
                        <div className="flex items-center gap-2 w-full">
                          {item.icon ? <item.icon /> : null}
                          <span className={cn('text-sm', {hidden: state === 'collapsed'})}>
                            {t(String(item.title))}
                          </span>
                        </div>
                      </SidebarMenuButton>
                    </SidebarMenuItem>
                  ),
                )}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>
    </Sidebar>
  );
}
