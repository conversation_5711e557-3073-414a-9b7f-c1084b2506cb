import {screen, fireEvent} from '@testing-library/react';
import {describe, it, expect, vi, beforeEach} from 'vitest';
import {AppSidebar} from '../AppSidebar';

// Mock UI components
vi.mock('../../ui', async () => {
  const actual = await vi.importActual<object>('../../ui');
  return {
    ...actual,
    useSidebar: vi.fn(),
    Sidebar: ({children, ...props}: any) => (
      <aside data-testid="sidebar" {...props}>
        {children}
      </aside>
    ),
    SidebarHeader: ({children}: any) => <header data-testid="sidebar-header">{children}</header>,
    SidebarGroup: ({children}: any) => <div data-testid="sidebar-group">{children}</div>,
    SidebarGroupContent: ({children, ...props}: any) => (
      <div data-testid="sidebar-group-content" {...props}>
        {children}
      </div>
    ),
    SidebarContent: ({children}: any) => <section data-testid="sidebar-content">{children}</section>,
    SidebarMenu: ({children}: any) => <ul data-testid="sidebar-menu">{children}</ul>,
    SidebarMenuItem: ({children, ...props}: any) => (
      <li data-testid="sidebar-menu-item" {...props}>
        {children}
      </li>
    ),
    SidebarMenuButton: ({children, onClick}: any) => (
      <button data-testid="sidebar-menu-button" onClick={onClick}>
        {children}
      </button>
    ),
    SidebarSeparator: () => <hr data-testid="sidebar-separator" />,
    SidebarGroupLabel: ({children, ...props}: any) => (
      <span data-testid="sidebar-group-label" {...props}>
        {children}
      </span>
    ),
  };
});

// Fix image asset mocks
vi.mock('../../../assets/comviva-logo-full.svg', () => ({
  default: 'full-logo.svg',
}));

vi.mock('../../../assets/comviva-logo-icon.svg', () => ({
  default: 'icon-logo.svg',
}));

// Mock navigate
const mockNavigate = vi.fn();
vi.mock('react-router-dom', () => ({
  useNavigate: () => mockNavigate,
}));

// Mock sidebarItems config
vi.mock('../sidebarRouteConfig', () => ({
  sidebarItems: [
    {
      id: 'reports',
      title: 'SIDEBAR_MENU_REPORTS',
      url: '#',
      icon: () => <svg data-testid="reports-icon" />,
    },
    {
      id: 'separator-1',
      isSeparator: true,
    },
    {
      id: 'inbox',
      title: 'SIDEBAR_MENU_INBOX',
      url: '/inbox',
      icon: () => <svg data-testid="inbox-icon" />,
    },
  ],
}));

// Mock translation
vi.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => key,
  }),
}));

// Import after mocking
import {useSidebar} from '../../ui';
import {renderWithStore} from '@/testUtils/test-utils';

describe('AppSidebar', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    document.documentElement.dir = 'ltr';
  });

  it('renders the icon logo when sidebar is collapsed', () => {
    (useSidebar as any).mockReturnValue({state: 'collapsed'});

    renderWithStore(<AppSidebar />);

    const iconLogo = screen.getByAltText('Comviva Logo');
    expect(iconLogo).toBeInTheDocument();
    expect(iconLogo).toHaveAttribute('src', 'icon-logo.svg');
  });

  it('renders the full logo when sidebar is expanded', () => {
    (useSidebar as any).mockReturnValue({state: 'expanded'});

    renderWithStore(<AppSidebar />);

    const fullLogo = screen.getByAltText('Comviva Logo');
    expect(fullLogo).toBeInTheDocument();
    expect(fullLogo).toHaveAttribute('src', 'full-logo.svg');
  });

  it('renders sidebar menu items and icons', () => {
    (useSidebar as any).mockReturnValue({state: 'expanded'});

    renderWithStore(<AppSidebar />);

    expect(screen.getByText('SIDEBAR_MENU_REPORTS')).toBeInTheDocument();
    expect(screen.getByTestId('reports-icon')).toBeInTheDocument();
    expect(screen.getByText('SIDEBAR_MENU_INBOX')).toBeInTheDocument();
    expect(screen.getByTestId('inbox-icon')).toBeInTheDocument();
  });

  it('navigates to correct route on menu item click', () => {
    (useSidebar as any).mockReturnValue({state: 'expanded'});

    renderWithStore(<AppSidebar />);

    const inboxButton = screen
      .getAllByTestId('sidebar-menu-button')
      .find(button => button.textContent?.includes('SIDEBAR_MENU_INBOX'));

    fireEvent.click(inboxButton!);
    expect(mockNavigate).toHaveBeenCalledWith('/inbox');
  });

  it('navigates to home when logo is clicked', () => {
    (useSidebar as any).mockReturnValue({state: 'expanded'});

    renderWithStore(<AppSidebar />);

    const logoContainer = screen.getAllByTestId('sidebar-group-content');
    fireEvent.click(logoContainer[0]);

    expect(mockNavigate).toHaveBeenCalledWith('/');
  });

  it('handles RTL layout correctly', () => {
    document.documentElement.dir = 'rtl';
    (useSidebar as any).mockReturnValue({state: 'expanded'});

    renderWithStore(<AppSidebar />);

    const sidebar = screen.getByTestId('sidebar');
    expect(sidebar).toHaveAttribute('side', 'right');
  });
});
