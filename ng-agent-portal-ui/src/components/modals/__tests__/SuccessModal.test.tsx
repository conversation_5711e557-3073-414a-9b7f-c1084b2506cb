import {describe, it, expect, vi} from 'vitest';
import {render, screen, fireEvent} from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import SuccessModal from '../SuccessModal';

describe('SuccessModal', () => {
  const mockOnClose = vi.fn();
  const mockMessage = 'Operation completed successfully!';

  it('renders the success modal when isOpen is true', () => {
    render(<SuccessModal isOpen={true} onClose={mockOnClose} message={mockMessage} />);

    // Check if the success title is rendered
    const titleElement = screen.getByText('Success');
    expect(titleElement).toBeInTheDocument();

    // Check if the message is rendered
    const messageElement = screen.getByText(mockMessage);
    expect(messageElement).toBeInTheDocument();

    // Check if the Done button is rendered
    const doneButton = screen.getByText('Done');
    expect(doneButton).toBeInTheDocument();

    // Check if the success icon is rendered
    expect(screen.getByAltText('Success')).toBeInTheDocument();

    // Check styling (note: this is testing implementation details, which is generally not recommended,
    // but included here to verify the specific styling requirements)
    expect(titleElement).toHaveClass('text-[24px]');
    expect(titleElement).toHaveClass('text-[#313131]');
    expect(messageElement).toHaveClass('text-[16px]');
    expect(messageElement).toHaveClass('text-[#8D919D]');
    expect(doneButton.closest('button')).toHaveClass('h-[40px]');
    expect(doneButton.closest('button')).toHaveClass('w-[104px]');
  });

  it('does not render the modal when isOpen is false', () => {
    render(<SuccessModal isOpen={false} onClose={mockOnClose} message={mockMessage} />);

    // Check that the modal content is not in the document
    expect(screen.queryByText('Success')).not.toBeInTheDocument();
    expect(screen.queryByText(mockMessage)).not.toBeInTheDocument();
    expect(screen.queryByText('Done')).not.toBeInTheDocument();
  });

  it('calls onClose when Done button is clicked', async () => {
    const user = userEvent.setup();

    render(<SuccessModal isOpen={true} onClose={mockOnClose} message={mockMessage} />);

    // Click the Done button
    const doneButton = screen.getByText('Done');
    await user.click(doneButton);

    // Check if onClose was called
    expect(mockOnClose).toHaveBeenCalledTimes(1);
  });

  it('calls onClose when dialog is closed via escape key or outside click', () => {
    render(<SuccessModal isOpen={true} onClose={mockOnClose} message={mockMessage} />);

    // Simulate dialog close event
    fireEvent.keyDown(screen.getByRole('dialog'), {key: 'Escape', code: 'Escape'});

    // Check if onClose was called
    expect(mockOnClose).toHaveBeenCalled();
  });
});
