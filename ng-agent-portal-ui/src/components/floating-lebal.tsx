import {useState} from 'react';
import type {ChangeEvent} from 'react';
import {Select, SelectTrigger, SelectValue, SelectContent, SelectItem} from './ui/select';
import {cn} from '@/lib/utils';
import {FieldType} from '@/enums/floating-lebal.enums';

interface OptionType {
  label: string;
  value: string;
}

interface FloatingFieldProps {
  readonly label: string;
  readonly value: string | number;
  readonly onChange: (event: ChangeEvent<HTMLInputElement | HTMLTextAreaElement> | {target: {value: string}}) => void;
  readonly as?: FieldType;
  readonly options?: OptionType[];
  readonly disabled?: boolean;
  readonly readOnly?: boolean;
  readonly type?: string;
  readonly 'data-testid'?: string;
  readonly [key: string]: unknown;
}

export function FloatingField({
  label,
  value,
  onChange,
  as = FieldType.INPUT,
  options = [],
  disabled = false,
  readOnly = false,
  type = 'text',
  'data-testid': testId,
  ...props
}: FloatingFieldProps) {
  const [isFocused, setIsFocused] = useState(false);
  const hasValue = value?.toString().length > 0;

  const sharedProps = {
    value,
    onChange,
    onFocus: () => setIsFocused(true),
    onBlur: () => setIsFocused(false),
    disabled,
    readOnly,
    className: 'w-full border',
    placeholder: ' ',
    ...props,
  };

  // Lookup object for rendering fields based on FieldType
  const renderFieldMap = {
    [FieldType.TEXTAREA]: () => <textarea data-testid={testId} {...sharedProps} rows={3} />,
    [FieldType.SELECT]: () =>
      readOnly ? (
        <div className="w-full border p-2 bg-gray-100 rounded text-sm text-gray-700">{value}</div>
      ) : (
        <div className="relative">
          <Select
            value={value.toString()}
            onValueChange={(val: string) => onChange({target: {value: val}})}
            disabled={disabled}
          >
            <SelectTrigger className={cn(sharedProps.className, disabled && 'opacity-50')}>
              <SelectValue placeholder={label} />
            </SelectTrigger>
            <SelectContent>
              {options.map(opt => (
                <SelectItem key={opt.value} value={opt.value}>
                  {opt.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      ),
    [FieldType.INPUT]: () => <input data-testid={testId} {...sharedProps} type={type} />,
  };

  // Get the render function from the map, defaulting to INPUT if as is undefined or invalid
  const renderField = renderFieldMap[as] || renderFieldMap[FieldType.INPUT];

  return (
    <div className="relative w-full">
      {renderField()}
      <label
        className={cn(
          'absolute left-2 bg-white px-1 transition-all duration-200 pointer-events-none',
          isFocused || hasValue ? '-top-2 text-xs text-(--color-tab-text-default)' : 'top-3 text-sm text-gray-400',
        )}
      >
        {label}
      </label>
    </div>
  );
}
