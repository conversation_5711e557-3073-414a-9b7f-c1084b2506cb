import React from 'react';
import {<PERSON>er, DrawerClose, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>eader, DrawerTitle} from '@/components/ui/drawer';
import {Button} from '@/components/ui/button';
import {Checkbox} from '@/components/ui/checkbox';
import {cn} from '@/lib/utils';
import {FilterItemType, type AppliedFilter, type FilterGroup} from '@/types';

interface FilterPanelProps {
  isOpen: boolean;
  onClose: () => void;
  groups: FilterGroup[];
  onApplyFilters: (applliedFilters: AppliedFilter | null) => void;
  activeFilters: AppliedFilter | null;
}

export function FilterPanel({isOpen, onClose, groups, onApplyFilters}: Readonly<FilterPanelProps>) {
  const [filterGroups, setFilterGroups] = React.useState<FilterGroup[]>(groups);

  const handleItemChange = (groupIndex: number, itemIndex: number, checked: boolean) => {
    const updatedGroups = [...filterGroups];
    updatedGroups[groupIndex].items[itemIndex].selected = checked;
    handleApplyFilters(updatedGroups);
    setFilterGroups(updatedGroups);
  };

  const handleApplyFilters = (updatedGroups: FilterGroup[]) => {
    const activeFilters = updatedGroups
      .filter(group => group.items.some(item => item.selected))
      .reduce((acc, group) => {
        acc[group.id] = group.items.filter(item => item.selected).map(item => item.id);
        return acc;
      }, {} as AppliedFilter);
    onApplyFilters(activeFilters);
  };

  const handleResetFilters = () => {
    setFilterGroups(
      groups.map(group => ({
        ...group,
        items: group.items.map(item => ({...item, selected: false})),
      })),
    );

    onApplyFilters(null);
  };

  return (
    <Drawer open={isOpen} onOpenChange={onClose} direction="right">
      <DrawerContent className="h-full !w-[300px] sm:!w-[350px] p-0">
        <DrawerHeader className="border-b p-4">
          <div className="flex items-center justify-between">
            <DrawerTitle className="text-lg font-semibold">Filters</DrawerTitle>
            <DrawerClose className="p-1 rounded-sm">
              <Button variant="link" className="text-destructive" onClick={handleResetFilters}>
                CLEAR
              </Button>
            </DrawerClose>
          </div>
        </DrawerHeader>

        <div className="overflow-y-auto p-4 h-[calc(100%-130px)]">
          {filterGroups.map((group, groupIndex) => (
            <div key={group.id} className="mb-6">
              <p className="text-[var(--text-subtle)] text-sm mb-3">{group.name}</p>
              {group.items[0]?.type === FilterItemType.CHECKBOX ? (
                <div className="space-y-2">
                  {group.items.map((item, itemIndex) => (
                    <label key={item.id} className="flex items-center space-x-2 cursor-pointer">
                      <Checkbox
                        id={`${group.id}-${item.id}`}
                        checked={item.selected}
                        onCheckedChange={checked => handleItemChange(groupIndex, itemIndex, checked as boolean)}
                        className={cn(
                          'border-ui-border h-[22px] w-[22px] rounded-sm transition-colors',
                          'data-[state=checked]:bg-[#496FDB] data-[state=checked]:border-[#496FDB] data-[state=checked]:hover:bg-[#496FDB]',
                        )}
                      />
                      <span className="text-text-label text-xs">{item.label}</span>
                    </label>
                  ))}
                </div>
              ) : (
                <div className="flex flex-wrap gap-2">
                  {group.items.map((item, itemIndex) => (
                    <Button
                      key={item.id}
                      variant={item.selected ? 'primary' : 'default'}
                      className={cn(
                        'px-3 py-1 rounded-full text-xs cursor-pointer border',
                        item.selected
                          ? 'border-chip-border'
                          : 'text-primary-accent border-chip-border bg-transparent hover:bg-primary-accent-light hover:text-primary-accent active:bg-primary-accent-light active:text-primary-accent',
                      )}
                      onClick={() => handleItemChange(groupIndex, itemIndex, !item.selected)}
                    >
                      {item.label}
                    </Button>
                  ))}
                </div>
              )}
            </div>
          ))}
        </div>
      </DrawerContent>
    </Drawer>
  );
}
