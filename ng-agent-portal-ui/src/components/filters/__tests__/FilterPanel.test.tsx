import {render, screen, fireEvent} from '@testing-library/react';
import {describe, it, expect, vi, beforeEach, type Mock} from 'vitest';
import {FilterPanel} from '../FilterPanel';
import {FilterItemType, type FilterGroup} from '@/types';

describe('FilterPanel', () => {
  let mockOnApplyFilters: Mock;
  let mockOnClose: Mock;

  const mockFilterGroups: FilterGroup[] = [
    {
      id: 'status',
      name: 'Status',
      items: [
        {id: 'active', label: 'Active', selected: false, type: FilterItemType.CHECKBOX},
        {id: 'inactive', label: 'Inactive', selected: false, type: FilterItemType.CHECKBOX},
      ],
    },
    {
      id: 'category',
      name: 'Category',
      items: [
        {id: 'urgent', label: 'Urgent', selected: false, type: FilterItemType.CHIP},
        {id: 'normal', label: 'Normal', selected: false, type: FilterItemType.CHIP},
      ],
    },
  ];

  beforeEach(() => {
    mockOnApplyFilters = vi.fn();
    mockOnClose = vi.fn();
    vi.clearAllMocks();
  });

  it('renders filter groups and items correctly', () => {
    render(
      <FilterPanel
        isOpen={true}
        onClose={mockOnClose}
        groups={mockFilterGroups}
        onApplyFilters={mockOnApplyFilters}
        activeFilters={null}
      />,
    );

    // Check if filter groups are rendered
    expect(screen.getByText('Status')).toBeInTheDocument();
    expect(screen.getByText('Category')).toBeInTheDocument();

    // Check if filter items are rendered
    expect(screen.getByText('Active')).toBeInTheDocument();
    expect(screen.getByText('Inactive')).toBeInTheDocument();
    expect(screen.getByText('Urgent')).toBeInTheDocument();
    expect(screen.getByText('Normal')).toBeInTheDocument();
  });

  it('calls onApplyFilters when a checkbox filter is selected', () => {
    render(
      <FilterPanel
        isOpen={true}
        onClose={mockOnClose}
        groups={mockFilterGroups}
        onApplyFilters={mockOnApplyFilters}
        activeFilters={null}
      />,
    );

    // Find and click a checkbox
    const activeCheckbox = screen.getByLabelText('Active');
    fireEvent.click(activeCheckbox);

    // Check if onApplyFilters was called with the correct parameters
    expect(mockOnApplyFilters).toHaveBeenCalledWith({status: ['active']});
  });

  it('calls onApplyFilters when a button filter is clicked', () => {
    render(
      <FilterPanel
        isOpen={true}
        onClose={mockOnClose}
        groups={mockFilterGroups}
        onApplyFilters={mockOnApplyFilters}
        activeFilters={null}
      />,
    );

    mockOnApplyFilters.mockClear();
    // Find and click a button filter
    const urgentButton = screen.getByText('Urgent');
    fireEvent.click(urgentButton);
    expect(mockOnApplyFilters).toHaveBeenCalledTimes(1);
    expect(mockOnApplyFilters).toHaveBeenCalledWith({status: ['active'], category: ['urgent']});
  });

  it('calls onApplyFilters with null when reset button is clicked', () => {
    render(
      <FilterPanel
        isOpen={true}
        onClose={mockOnClose}
        groups={mockFilterGroups}
        onApplyFilters={mockOnApplyFilters}
        activeFilters={null}
      />,
    );

    // Click the reset button
    const resetButton = screen.getByText('CLEAR');
    fireEvent.click(resetButton);

    // Check if onApplyFilters was called with null
    expect(mockOnApplyFilters).toHaveBeenCalledWith(null);
  });
});
