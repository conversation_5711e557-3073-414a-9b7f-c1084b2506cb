import {Navigate} from 'react-router-dom';
import {useAppSelector, usePermission} from '@/hooks';
import type {JSX} from 'react';
import {MESSAGES, RouteConstant} from '@/constants';
import type {PermissionsEnum} from '@/enums';
import {selectUserLoading} from '@/redux/auth/authSlice';
import {Loader} from './ui/loader';
import {useTranslation} from 'react-i18next';

interface AuthorizedRouteProps {
  element: JSX.Element;
  requiredPermissions?: PermissionsEnum[];
}

const AuthorizedRoute = ({element, requiredPermissions = []}: AuthorizedRouteProps) => {
  const hasAccess = usePermission(requiredPermissions);
  const isUserLoading = useAppSelector(selectUserLoading);
  const {t} = useTranslation();

  if (isUserLoading) {
    return <Loader message={t(MESSAGES.CHECKING_PERMISSIONS)} />;
  }
  return hasAccess ? element : <Navigate replace to={RouteConstant.FORBIDDEN} />;
};

export default AuthorizedRoute;
