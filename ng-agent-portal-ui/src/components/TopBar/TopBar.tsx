import {useState, useRef, useEffect} from 'react';
import {Bell, Menu} from 'lucide-react';
import {Button} from '../ui';
import i18n from '@/i18n';
import {LANGUAGE_OPTIONS, MESSAGES} from '@/constants';
import {useTranslation} from 'react-i18next';

const TopBar = () => {
  const {t} = useTranslation();
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const menuRef = useRef<HTMLDivElement>(null);

  const toggleMenu = () => setIsMenuOpen(prev => !prev);

  const handleLanguageChange = (lang: string) => {
    void i18n.changeLanguage(lang);
    setIsMenuOpen(false);
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setIsMenuOpen(false);
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  return (
    <header className="w-full h-15 border-b border-border bg-background px-6 flex items-center justify-between shadow-md">
      <div className="text-lg font-semibold" />

      <div className="flex items-center space-x-4 relative" ref={menuRef}>
        <div className="relative">
          <Button
            variant="ghost"
            aria-label={t(MESSAGES.ARIA_LABEL_SELECT_LANGUAGE)}
            onClick={toggleMenu}
            className="flex items-center px-3 py-2"
          >
            <Menu className="w-5 h-5 text-red-500" />
            <span className="text-xs font-normal ml-2 uppercase">{t(MESSAGES.LANGUAGE_EN)}</span>
          </Button>

          {isMenuOpen && (
            <div className="absolute right-0 mt-2 w-36 bg-white border border-gray-200 rounded shadow-md z-20">
              {LANGUAGE_OPTIONS.map(({code, label}) => (
                <button
                  key={code}
                  onClick={() => handleLanguageChange(code)}
                  className="block w-full text-left px-4 py-2 text-sm hover:bg-gray-100"
                  type="button"
                >
                  {label}
                </button>
              ))}
            </div>
          )}
        </div>

        <Button variant="ghost" size="icon" aria-label={t(MESSAGES.ARIA_LABEL_NOTIFICATIONS)}>
          <Bell className="w-5 h-5" />
        </Button>
      </div>
    </header>
  );
};

export {TopBar};
