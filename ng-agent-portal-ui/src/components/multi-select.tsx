import * as React from 'react';
import {Check, ChevronDown, X} from 'lucide-react';
import {cn} from '@/lib/utils';
import {Popover, PopoverContent, PopoverTrigger} from '@/components/ui/popover';

type Option = {
  id: string;
  label: string;
  value: string;
};

interface MultiSelectProps {
  options: Option[];
  value: { id: string; value: string }[];
  onChange: (value: { id: string; value: string }[]) => void;
  placeholder?: string;
  className?: string;
}

export const MultiSelect: React.FC<MultiSelectProps> = ({options, value, onChange, placeholder, className}) => {
  const [open, setOpen] = React.useState(false);

  const handleSelect = (selectedValue: string, selectedId: string) => {
    const exists = value.some(v => v.value === selectedValue);

    if (exists) {
      onChange(value.filter(v => v.value !== selectedValue));
    } else {
      onChange([...value, { id: selectedId, value: selectedValue }]);
    }
  };

  const removeTag = (val: string) => {
    onChange(value.filter(v => v.value !== val));
  };

  const selectedOptions = options.filter(option =>
    value.some(v => v.value === option.value)
  );

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <div className="relative w-full">
          <div className={cn('flex flex-wrap gap-1 border rounded-sm px-3 py-2 min-h-10 cursor-default', className)}>
            {selectedOptions.length ? (
              <>
                {selectedOptions.slice(0, 3).map(opt => (
                  <div key={opt.value} className="flex items-center bg-muted rounded-full px-2 py-0.5 text-sm">
                    {opt.label}
                    <X
                      className="ml-1 w-3 h-3 cursor-pointer"
                      onMouseDown={e => {
                        e.preventDefault();
                        e.stopPropagation();
                        removeTag(opt.value);
                      }}
                    />
                  </div>
                ))}
                {selectedOptions.length > 3 && (
                  <div className="flex items-center bg-muted rounded-full px-2 py-0.5 text-sm">
                    +{selectedOptions.length - 3}
                  </div>
                )}
              </>
            ) : (
              <span className="text-muted-foreground">{placeholder}</span>
            )}
          </div>

          {/* PopoverToggle icon only */}
          <div
            className="absolute right-2 top-1/2 -translate-y-1/2 text-muted-foreground cursor-pointer"
            onClick={() => setOpen(prev => !prev)}
          >
            <ChevronDown className="w-4 h-4" />
          </div>
        </div>
      </PopoverTrigger>

      <PopoverContent className="max-h-64 w-sm overflow-auto p-1 text-gray-600 text-sm">
        {options.map(option => {
          const isSelected = value.some(v => v.value === option.value);
          return (
            <div
              key={option.value}
              className="cursor-pointer px-3 py-2 flex items-center justify-between hover:bg-muted"
              onClick={() => handleSelect(option.value, option.id)}
            >
              <span>{option.label}</span>
              {isSelected && <Check className="w-4 h-4 text-primary" />}
            </div>
          );
        })}
      </PopoverContent>
    </Popover>
  );
};
