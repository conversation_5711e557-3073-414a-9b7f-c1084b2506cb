import React, {useState} from 'react';
import {Select, SelectTrigger, SelectValue, SelectContent, SelectItem} from './ui/select';
import {cn} from '@/lib/utils';
import {MultiSelect} from './multi-select';

type Option = {id: string; value: string; label: string};

type FloatingFieldProps<T = string | string[]> = {
  label: string;
  value: T;
  onChange: (e: T | React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => void;
  as?: 'input' | 'textarea' | 'select' | 'multi-select';
  options?: Option[];
  disabled?: boolean;
  type?: string;
  [key: string]: unknown;
};

export function FloatingField<T = string | string[]>({
  label,
  value,
  onChange,
  as = 'input',
  options = [],
  disabled,
  type = 'text',
  ...props
}: FloatingFieldProps<T>) {
  const [isFocused, setIsFocused] = useState(false);

  // For multi-select, value is an array
  const hasValue =
    as === 'multi-select' ? Array.isArray(value) && value.length > 0 : (value ?? '').toString().length > 0;

  const sharedProps = {
    value,
    onChange,
    onFocus: () => setIsFocused(true),
    onBlur: () => setIsFocused(false),
    disabled,
    className:
      'w-full border rounded px-3 pt-3 pb-2 text-base text-black focus:outline-none disabled:opacity-50 bg-white focus:ring-1 focus:ring-blue-700 min-h-12',
    placeholder: ' ',
    ...props,
  };

  let fieldContent: React.ReactNode;
  if (as === 'textarea') {
    fieldContent = <textarea {...sharedProps} value={typeof value === 'string' ? value : ''} rows={3} />;
  } else if (as === 'select') {
    fieldContent = (
      <div className="relative">
        <Select
          value={value as string}
          defaultValue={value as string}
          onValueChange={val => {
            if (typeof onChange === 'function') {
              onChange({target: {value: val}} as React.ChangeEvent<HTMLInputElement>);
            }
          }}
          disabled={disabled}
        >
          <SelectTrigger className={cn(sharedProps.className, disabled && 'opacity-50')}>
            <SelectValue placeholder={label} />
          </SelectTrigger>
          <SelectContent>
            {options.map(opt => (
              <SelectItem key={opt.value} value={opt.value}>
                {opt.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
    );
  } else if (as === 'multi-select') {
    fieldContent = (
      <fieldset className="relative" onFocus={() => setIsFocused(true)} onBlur={() => setIsFocused(false)}>
        <MultiSelect
          options={options}
          value={value as {id: string; value: string}[]}
          onChange={onChange as (value: {id: string; value: string}[]) => void}
          className={sharedProps.className}
        />
      </fieldset>
    );
  } else {
    fieldContent = (
      <input {...sharedProps} value={typeof value === 'string' || typeof value === 'number' ? value : ''} type={type} />
    );
  }

  return (
    <div className="relative w-full mt-0">
      {fieldContent}
      <label
        className={cn(
          'absolute left-3 bg-white px-1 transition-all duration-200 pointer-events-none',
          isFocused || hasValue ? '-top-2 text-xs text-gray-600' : 'top-3.5 text-base text-gray-500',
        )}
      >
        {label}
      </label>
    </div>
  );
}
