import {env} from '@/config/env';
import {getAccessToken} from '@/utils/tokenHelper';
import {useEffect} from 'react';
import type {ReactNode} from 'react';

interface AuthGuardProps {
  children: ReactNode;
}

export const AuthGuard = ({children}: AuthGuardProps) => {
  useEffect(() => {
    const token = getAccessToken();
    if (!token) {
      window.location.href = `${env.AUTH_LOGIN_URL}`;
    }
  }, []);

  return <>{children}</>;
};
