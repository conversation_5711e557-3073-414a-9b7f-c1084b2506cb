import {useEffect} from 'react';
import {useNavigate, useLocation} from 'react-router-dom';
import {useAuth} from '@/hooks';
import {extractTokenFromUrl, removeTokenFromUrl} from '@/utils/tokenHelper';
import type {AuthResData} from '@/redux/auth/authSlice';

const RedirectHandler = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const {login} = useAuth();

  useEffect(() => {
    const token = extractTokenFromUrl(location);

    if (token) {
      login({
        access_token: token,
        refresh_token: '',
        expires_in: 3600,
        token_type: 'Bearer',
        'not-before-policy': 0,
        session_state: '',
        scope: '',
        refresh_expires_in: 86400,
      } as AuthResData);

      // eslint-disable-next-line @typescript-eslint/no-misused-promises
      void removeTokenFromUrl(location, navigate);

      // Don't redirect yet, wait for next render
    } else {
      void navigate('/chats', {replace: true});
    }
  }, [login, location, navigate]);

  return null;
};

export default RedirectHandler;
