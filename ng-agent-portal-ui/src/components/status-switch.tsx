import React from 'react';
import {Switch} from '../components/ui/switch';

interface StatusSwitchProps {
  isActive: boolean;
  onStatusChange: (checked: boolean) => void;
  disabled?: boolean;
  className?: string;
}

const StatusSwitch: React.FC<StatusSwitchProps> = ({isActive, onStatusChange, disabled = false, className}) => {
  return <Switch checked={isActive} onCheckedChange={onStatusChange} disabled={disabled} className={className} />;
};

export default StatusSwitch;
