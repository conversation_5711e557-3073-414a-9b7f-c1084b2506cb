import {describe, it, expect} from 'vitest';
import {render, screen} from '@testing-library/react';
import {CommonBreadcrumb} from '../CommonBreadCrumbs';
import {RouteConstant} from '@/constants';

describe('CommonBreadcrumb', () => {
  it('renders breadcrumb items correctly', () => {
    const items = [
      {label: 'Home', href: RouteConstant.HOME},
      {label: 'Teams', href: RouteConstant.TEAMS},
      {label: 'Current Page'},
    ];

    render(<CommonBreadcrumb items={items} />);

    // Check if all labels are rendered
    expect(screen.getByText('Home')).toBeInTheDocument();
    expect(screen.getByText('Teams')).toBeInTheDocument();
    expect(screen.getByText('Current Page')).toBeInTheDocument();
  });

  it('renders links for items with href', () => {
    const items = [{label: 'Home', href: RouteConstant.HOME}, {label: 'Current Page'}];

    render(<CommonBreadcrumb items={items} />);

    // Check if the link is rendered correctly
    const homeLink = screen.getByText('Home');
    expect(homeLink.closest('a')).toHaveAttribute('href', '/');
  });

  it('renders current page without link', () => {
    const items = [{label: 'Home', href: RouteConstant.HOME}, {label: 'Current Page'}];

    render(<CommonBreadcrumb items={items} />);

    // Check if the current page is not a link
    const currentPage = screen.getByText('Current Page');
    expect(currentPage.closest('a')).toBeNull();
  });

  it('renders separators between items', () => {
    const items = [
      {label: 'Home', href: RouteConstant.HOME},
      {label: 'Teams', href: RouteConstant.TEAMS},
      {label: 'Current Page'},
    ];

    render(<CommonBreadcrumb items={items} />);

    // There should be 2 separators for 3 items
    const separators = document.querySelectorAll('svg');
    expect(separators.length).toBe(2);
  });

  it('renders empty breadcrumb when no items are provided', () => {
    render(<CommonBreadcrumb items={[]} />);

    // The breadcrumb container should be empty
    const breadcrumbList = document.querySelector('[data-slot="breadcrumb-list"]');
    expect(breadcrumbList).toBeInTheDocument();
    expect(breadcrumbList?.children.length).toBe(0);
  });
});
