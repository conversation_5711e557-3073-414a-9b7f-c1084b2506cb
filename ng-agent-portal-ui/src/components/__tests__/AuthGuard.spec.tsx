import {describe, it, expect, vi, beforeEach} from 'vitest';
import {render, screen} from '@testing-library/react';
import {MemoryRouter} from 'react-router-dom';
import {AuthGuard} from '../AuthGuard';

interface MockLocation {
  href: string;
}

beforeEach(() => {
  vi.resetAllMocks();
  localStorage.clear();
  sessionStorage.clear();

  // Override window.location with a type-safe mock
  const mockLocation: MockLocation = {
    href: 'http://localhost/test',
  };

  Object.defineProperty(window, 'location', {
    writable: true,
    configurable: true,
    value: mockLocation,
  });
});

describe('AuthGuard', () => {
  it('renders children when token is present', () => {
    localStorage.setItem('token', 'valid-token');

    render(
      <MemoryRouter>
        <AuthGuard>
          <div>Protected Content</div>
        </AuthGuard>
      </MemoryRouter>,
    );

    expect(screen.getByText('Protected Content')).toBeInTheDocument();
  });

  it('redirects to login when token is not present', () => {
    const redirectSpy = vi.spyOn(window.location, 'href', 'set');

    render(
      <MemoryRouter>
        <AuthGuard>
          <div>Protected Content</div>
        </AuthGuard>
      </MemoryRouter>,
    );

    expect(redirectSpy).toHaveBeenCalledWith('http://example.com/login');
  });

  it('checks sessionStorage if token not in localStorage', () => {
    sessionStorage.setItem('token', 'session-token');

    render(
      <MemoryRouter>
        <AuthGuard>
          <div>Session Token Content</div>
        </AuthGuard>
      </MemoryRouter>,
    );

    expect(screen.getByText('Session Token Content')).toBeInTheDocument();
  });
});
