import {render, screen, fireEvent} from '@testing-library/react';
import {beforeEach, describe, expect, it, vi} from 'vitest';
import {Toaster} from '../Toaster/Toaster';
import {ToastColor} from '@/types';

// Mock notistack's useSnackbar hook
const mockCloseSnackbar = vi.fn();
vi.mock('notistack', async () => {
  const actual = await vi.importActual('notistack');
  return {
    ...actual,
    useSnackbar: vi.fn(() => ({
      closeSnackbar: mockCloseSnackbar,
      enqueueSnackbar: vi.fn(),
    })),
  };
});

describe('Toaster Component', () => {
  const mockOnClose = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders with default props', () => {
    render(<Toaster title="Test Title" message="Test Content" onClose={mockOnClose} id="test-toast" />);

    // Check if title and content are rendered
    expect(screen.getByText('Test Title')).toBeInTheDocument();
    expect(screen.getByText('Test Content')).toBeInTheDocument();

    // Check if OK button is rendered
    expect(screen.getByText('OK')).toBeInTheDocument();
  });

  it('calls closeSnackbar when close button is clicked', () => {
    render(<Toaster title="Test Title" message="Test Content" onClose={mockOnClose} id="test-toast" />);

    // Click the close button (X icon)
    const closeButton = screen.getByRole('button', {name: 'Close'});
    fireEvent.click(closeButton);

    // Check if closeSnackbar was called with the correct id
    expect(mockCloseSnackbar).toHaveBeenCalledTimes(1);
    expect(mockCloseSnackbar).toHaveBeenCalledWith('test-toast');
  });

  it('calls closeSnackbar when OK button is clicked', () => {
    render(<Toaster title="Test Title" message="Test Content" onClose={mockOnClose} id="test-toast" />);

    // Click the OK button
    const okButton = screen.getByText('OK');
    fireEvent.click(okButton);

    // Check if closeSnackbar was called with the correct id
    expect(mockCloseSnackbar).toHaveBeenCalledTimes(1);
    expect(mockCloseSnackbar).toHaveBeenCalledWith('test-toast');
  });

  it('renders with success variant', () => {
    render(
      <Toaster
        title="Success"
        message="Operation successful"
        onClose={mockOnClose}
        color={ToastColor.Success}
        id="success-toast"
      />,
    );

    // Check if title is rendered
    expect(screen.getByText('Success')).toBeInTheDocument();

    // Check if the container has the success background class
    const container = screen.getByRole('alert').firstChild;
    expect(container).toHaveClass('bg-success');
    expect(container).toHaveClass('border-1-solid-success');
  });

  it('renders with error variant', () => {
    render(
      <Toaster
        title="Error"
        message="Operation failed"
        onClose={mockOnClose}
        color={ToastColor.Error}
        id="error-toast"
      />,
    );

    // Check if title is rendered
    expect(screen.getByText('Error')).toBeInTheDocument();

    // Check if the container has the error background class
    const container = screen.getByRole('alert').firstChild;
    expect(container).toHaveClass('bg-destructive');
    expect(container).toHaveClass('border-1-solid-destructive');
  });

  it('renders with warning variant', () => {
    render(
      <Toaster
        title="Warning"
        message="Proceed with caution"
        onClose={mockOnClose}
        color={ToastColor.Warning}
        id="warning-toast"
      />,
    );

    // Check if title is rendered
    expect(screen.getByText('Warning')).toBeInTheDocument();

    // Check if the container has the warning background class
    const container = screen.getByRole('alert').firstChild;
    expect(container).toHaveClass('bg-warning');
    expect(container).toHaveClass('border-1-solid-warning');
  });

  it('renders with info variant', () => {
    render(
      <Toaster
        title="Info"
        message="For your information"
        onClose={mockOnClose}
        color={ToastColor.Info}
        id="info-toast"
      />,
    );

    // Check if title is rendered
    expect(screen.getByText('Info')).toBeInTheDocument();

    // Check if the container has the info background class
    const container = screen.getByRole('alert').firstChild;
    expect(container).toHaveClass('bg-info-background');
    expect(container).toHaveClass('border-1-solid-info');
  });
});
