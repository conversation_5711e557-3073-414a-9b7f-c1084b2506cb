import {cn} from '@/lib/utils';
import {Loader2} from 'lucide-react';

interface LoaderProps {
  className?: string;
  message?: string;
}

export const Loader = ({className, message = 'Loading...'}: LoaderProps) => {
  return (
    <div className={cn('flex flex-col items-center justify-center py-10 gap-2', className)}>
      <Loader2 className="animate-spin h-8 w-8 text-muted-foreground" />
      <p className="text-sm text-muted-foreground">{message}</p>
    </div>
  );
};
