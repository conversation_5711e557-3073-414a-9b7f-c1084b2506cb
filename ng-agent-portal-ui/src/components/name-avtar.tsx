import React from 'react';
import {Avatar, AvatarFallback, AvatarImage} from '../components/ui/avatar';
import {cn} from '@/lib/utils';

interface NameWithAvatarProps {
  name: string;
  imageUrl?: string;
  className?: string;
}

const NameWithAvatar: React.FC<NameWithAvatarProps> = ({name, imageUrl, className}) => {
  const getInitials = (fullName: string): string => {
    return fullName
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  return (
    <div className={cn('flex items-center gap-3', className)}>
      <Avatar className="h-8 w-8 bg-gray-100">
        <AvatarImage src={imageUrl} alt={name} />
        <AvatarFallback className="text-xs font-medium text-gray-600 bg-gray-100">{getInitials(name)}</AvatarFallback>
      </Avatar>
      <span className="text-xs text-default">{name}</span>
    </div>
  );
};

export default NameWithAvatar;
