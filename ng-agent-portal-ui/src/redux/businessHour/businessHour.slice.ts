import {apiSlice} from '../apiSlice';
import {ApiSliceIdentifier} from '@/constants';
import type {BusinessHourListResponse, BusinessHourPayload} from '@/types/businessHour.type';

const apiSliceIdentifier = ApiSliceIdentifier.AGENT_PORTAL_FACADE;

export const businessHourApiSlice = apiSlice.injectEndpoints({
  endpoints: builder => ({
    getBusinessHours: builder.query<BusinessHourListResponse, void>({
      query: () => ({
        url: '/business-hour',
        apiSliceIdentifier,
      }),
    }),

    createBusinessHour: builder.mutation<BusinessHourPayload, Partial<BusinessHourPayload>>({
      query: businessHour => ({
        url: '/business-hour',
        method: 'POST',
        body: businessHour,
        apiSliceIdentifier,
      }),
    }),

    updateBusinessHour: builder.mutation<BusinessHourPayload, {id: string; businessHour: Partial<BusinessHourPayload>}>(
      {
        query: ({id, businessHour}) => ({
          url: `/business-hour/${id}`,
          method: 'PATCH',
          body: businessHour,
          apiSliceIdentifier,
        }),
      },
    ),

    deleteBusinessHour: builder.mutation<{success: boolean; message: string}, {id: string | number}>({
      query: ({id}) => ({
        url: `/business-hour/${id}`,
        method: 'DELETE',
        apiSliceIdentifier,
      }),
    }),
  }),
});

export const {
  useGetBusinessHoursQuery,
  useCreateBusinessHourMutation,
  useUpdateBusinessHourMutation,
  useDeleteBusinessHourMutation,
} = businessHourApiSlice;
