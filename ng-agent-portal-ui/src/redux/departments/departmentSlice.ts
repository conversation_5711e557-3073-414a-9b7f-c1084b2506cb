import {apiSlice} from '../apiSlice';
import type {DepartmentListResponse, IDepartment} from '../../types';
import type {FilterOptions} from '../../types/filter.types';
import {ApiSliceIdentifier} from '@/constants';
import {buildDepartmentFilter, type DepartmentQueryOptions} from '../../utils/filter/departmentFilter';

// Re-export for convenience
export type {DepartmentQueryOptions};

const apiSliceIdentifier = ApiSliceIdentifier.AGENT_PORTAL_FACADE;

export const departmentApiSlice = apiSlice.injectEndpoints({
  endpoints: builder => ({
    getDepartments: builder.query<DepartmentListResponse, DepartmentQueryOptions | void>({
      query: queryOptions => ({
        url: '/departments',
        apiSliceIdentifier,
        params: buildDepartmentFilter(queryOptions),
      }),
    }),

    getDepartmentById: builder.query<IDepartment, {id: string | number; options?: FilterOptions}>({
      query: ({id, options}) => ({
        url: `/departments/${id}`,
        apiSliceIdentifier,
        params: JSON.stringify(options),
      }),
    }),

    createDepartment: builder.mutation<IDepartment, {name: string}>({
      query: department => ({
        url: '/departments',
        method: 'POST',
        body: department,
        apiSliceIdentifier,
      }),
    }),

    updateDepartment: builder.mutation<IDepartment, {id: string | number; data: Partial<IDepartment>}>({
      query: ({id, data}) => ({
        url: `/departments/${id}`,
        method: 'PATCH',
        body: data,
        apiSliceIdentifier,
      }),
    }),

    deleteDepartment: builder.mutation<{success: boolean; message: string}, {id: string | number}>({
      query: ({id}) => ({
        url: `/departments/${id}`,
        method: 'DELETE',
        apiSliceIdentifier,
      }),
    }),
  }),
});

export const {
  useGetDepartmentsQuery,
  useGetDepartmentByIdQuery,
  useCreateDepartmentMutation,
  useUpdateDepartmentMutation,
  useDeleteDepartmentMutation,
} = departmentApiSlice;
