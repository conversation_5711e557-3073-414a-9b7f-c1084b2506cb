import {apiSlice} from '../apiSlice';
import type {Message} from '../../Pages/Inbox/types';
import {ApiSliceIdentifier} from '@/constants';

const apiSliceIdentifier = ApiSliceIdentifier.COMMUNICATION_SERVICE;

export const chatApiSlice = apiSlice.injectEndpoints({
  endpoints: builder => ({
    getMessagesByChatId: builder.query<{data: Message[]}, string>({
      query: chatId => ({
        url: `/chats/${chatId}/messages`,
        apiSliceIdentifier,
      }),
    }),
    acceptChatTransfer: builder.mutation<{data: {chatId: string}}, {ticketId: string}>({
      query: ({ticketId}) => ({
        url: `/chats/tickets/${ticketId}/agents/agent-dummy-id/action/accept`,
        method: 'POST',
        body: {agentId: `agent-dummy-id`},
        apiSliceIdentifier,
      }),
    }),
  }),
});

export const {useGetMessagesByChatIdQuery, useAcceptChatTransferMutation} = chatApiSlice;
