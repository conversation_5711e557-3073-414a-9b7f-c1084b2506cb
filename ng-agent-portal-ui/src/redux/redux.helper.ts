import {ApiSliceIdentifier} from '../constants';
import {env} from '../config/env';

export function getBaseUrl(apiSliceIdentifier?: ApiSliceIdentifier) {
  const baseUrlMap: Partial<Record<ApiSliceIdentifier, string | undefined>> = {
    [ApiSliceIdentifier.AGENT_PORTAL_FACADE]: env.AGENT_PORTAL_FACADE_API_BASE_URL,
    [ApiSliceIdentifier.COMMUNICATION_SERVICE]: env.COMMUNICATION_SERVICE_API_BASE_URL,
  };

  return baseUrlMap[apiSliceIdentifier!];
}
