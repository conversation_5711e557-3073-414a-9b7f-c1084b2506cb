import {isGetMeFulfilledAction} from '@/utils/actionGuards';
import type {Middleware} from '@reduxjs/toolkit';
import {setUser, setUserLoading} from '../auth/authSlice';
import type {IUserWithPermissions} from '@/types';

const storageMiddleware: Middleware = store => next => (action: unknown) => {
  if (isGetMeFulfilledAction(action)) {
    const {payload} = action;
    store.dispatch(setUser(payload as IUserWithPermissions));
    store.dispatch(setUserLoading(false));
  }

  return next(action);
};

export default storageMiddleware;
