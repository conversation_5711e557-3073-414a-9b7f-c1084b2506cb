import {apiSlice} from '../apiSlice';
import type {IUserWithPermissions} from '../../types';
import {ApiSliceIdentifier} from '@/constants';

const apiSliceIdentifier = ApiSliceIdentifier.AGENT_PORTAL_FACADE;

export const authApiSlice = apiSlice.injectEndpoints({
  endpoints: builder => ({
    getMe: builder.query<IUserWithPermissions, void>({
      query: () => ({
        url: '/auth/me',
        apiSliceIdentifier,
      }),
    }),
  }),
});

export const {useGetMeQuery, useLazyGetMeQuery} = authApiSlice;
