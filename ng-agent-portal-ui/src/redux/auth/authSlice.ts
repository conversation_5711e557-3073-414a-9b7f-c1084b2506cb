import {createSlice, type PayloadAction} from '@reduxjs/toolkit';
import type {RootState} from '../store';
import type {PermissionsEnum} from '../../enums';
import {
  getAccessToken,
  getRefreshToken,
  getTokenExpiration,
  storeAccessToken,
  storeRefreshToken,
  storeTokenExpiration,
  clearTokens,
} from '../../utils/tokenHelper';
import type {IUserWithPermissions} from '@/types';

export interface AuthState {
  isLoggedIn: boolean;
  accessToken: string | null;
  refreshToken: string | null;
  expires: number | null;
  permissions: PermissionsEnum[] | null;
  user: IUserWithPermissions | null;
  isUserLoading: boolean;
}

export interface AuthResData {
  access_token: string;
  expires_in: number;
  refresh_expires_in: number;
  refresh_token: string;
  token_type: string;
  'not-before-policy': number;
  session_state: string;
  scope: string;
}

const initialState: AuthState = {
  accessToken: getAccessToken(),
  refreshToken: getRefreshToken(),
  expires: getTokenExpiration(),
  isLoggedIn: !!getAccessToken(),
  permissions: null,
  user: null,
  isUserLoading: true,
};

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    setCredentials: (state, action: PayloadAction<AuthResData>) => {
      const {access_token, refresh_token, expires_in} = action.payload;

      // Store tokens securely using tokenHelper
      storeAccessToken(access_token);
      storeRefreshToken(refresh_token);
      storeTokenExpiration(expires_in);

      // Update state
      state.accessToken = access_token;
      state.refreshToken = refresh_token;
      state.expires = Date.now() + expires_in * 1000; // Convert to timestamp
      state.isLoggedIn = true;
    },
    unsetCredentials: state => {
      // Clear tokens from storage
      clearTokens();

      // Update state
      state.accessToken = null;
      state.refreshToken = null;
      state.expires = null;
      state.isLoggedIn = false;
      state.permissions = null;
    },
    setPermissions: (state, action: PayloadAction<PermissionsEnum[]>) => {
      state.permissions = action.payload;
    },

    setUser: (state, action: PayloadAction<IUserWithPermissions>) => {
      state.user = action.payload;
    },

    setUserLoading: (state, action: PayloadAction<boolean>) => {
      state.isUserLoading = action.payload;
    },
  },
});

export const {setCredentials, unsetCredentials, setPermissions, setUser, setUserLoading} = authSlice.actions;

export default authSlice.reducer;

// Selectors
export const selectCurrentLoginStatus = (state: RootState) => state.auth.isLoggedIn;
export const selectCurrentAccessToken = (state: RootState) => state.auth.accessToken;
export const selectCurrentRefreshToken = (state: RootState) => state.auth.refreshToken;
export const selectCurrentAuthState = (state: RootState) => state.auth;
export const selectCurrentPermissions = (state: RootState) => state.auth.permissions;
export const selectCurrentUser = (state: RootState) => state.auth.user;
export const selectUserLoading = (state: RootState) => state.auth.isUserLoading;
