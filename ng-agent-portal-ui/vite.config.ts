import {defineConfig} from 'vitest/config';
import react from '@vitejs/plugin-react';
import path from 'path';
import tailwindcss from '@tailwindcss/vite';

export default defineConfig({
  base: '/agent/',
  plugins: [react(), tailwindcss()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
  test: {
    globals: true,
    environment: 'jsdom',
    setupFiles: './src/setupTests.ts',
    coverage: {
      reporter: ['text', 'html', 'lcov', 'cobertura'],
      exclude: [
        'node_modules/',
        'src/setupTests.ts',
        'src/assets/',
        'src/components/ui',
        'src/lib',
        'src/App.tsx',
        'src/main.tsx',
        'src/hooks',
        'src/redux',
        'src/types',
        '**/**/index.ts',
        '**/index.ts',
        '**/**/index.tsx',
        '**/index.tsx',
      ],
      include: ['src/**/*.{ts,tsx}'],
    },
  },
  build: {
    outDir: 'dist',
  },
});
