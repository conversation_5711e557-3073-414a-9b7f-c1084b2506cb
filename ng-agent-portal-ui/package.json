{"name": "starter-template", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "start": "serve -s dist -l 8080", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "test": "vitest", "test:no-watch": "vitest run", "test:coverage": "vitest run --coverage", "commitlint": "commitlint --edit", "commit": "git-cz", "prepare": "husky", "prettier:cli": "prettier \"**/*.tsx\" \"**/*.ts\" \"**/*.js\" \"**/*.html\" \"**/*.css\"", "prettier:fix": "npm run prettier:cli -- --write"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "@reduxjs/toolkit": "^2.8.2", "@tailwindcss/vite": "^4.1.7", "@tanstack/react-table": "^8.21.3", "@testing-library/user-event": "^14.6.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "crypto-js": "^4.2.0", "dompurify": "^3.2.6", "emoji-picker-react": "^4.12.2", "i18next": "^25.2.1", "i18next-browser-languagedetector": "^8.1.0", "lucide-react": "^0.511.0", "moment": "^2.30.1", "moment-timezone": "^0.6.0", "node-cache": "^5.1.2", "notistack": "^3.0.2", "prosemirror-commands": "^1.7.1", "prosemirror-history": "^1.4.1", "prosemirror-keymap": "^1.2.3", "prosemirror-model": "^1.25.1", "prosemirror-schema-basic": "^1.2.4", "prosemirror-schema-list": "^1.5.1", "prosemirror-state": "^1.4.3", "prosemirror-view": "^1.40.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.56.4", "react-i18next": "^15.5.2", "react-phone-input-2": "^2.15.1", "react-redux": "^9.2.0", "react-router-dom": "^7.6.0", "serve": "^14.2.4", "socket.io-client": "^4.8.1", "tailwind-merge": "^3.3.0", "tailwindcss": "^4.1.7", "vaul": "^1.1.2", "zod": "^3.25.30"}, "devDependencies": {"@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@eslint/js": "^9.25.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/crypto-js": "^4.2.2", "@types/node": "^22.15.20", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@types/react-router-dom": "^5.3.3", "@vitejs/plugin-react": "^4.4.1", "@vitest/coverage-v8": "^3.1.4", "commitizen": "^4.3.1", "cz-customizable": "^7.4.0", "eslint": "^9.25.0", "eslint-plugin-react-dom": "^1.50.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "eslint-plugin-react-x": "^1.50.0", "globals": "^16.0.0", "husky": "^9.1.7", "jsdom": "^26.1.0", "prettier": "^3.5.3", "tw-animate-css": "^1.3.0", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5", "vitest": "^3.1.4"}, "config": {"commitizen": {"path": "./node_modules/cz-customizable"}, "cz-customizable": {"config": "./.cz-config.cjs"}}}